# Docker ignore file
# 排除不需要複製到 Docker 鏡像中的文件和目錄

# Git 相關
.git
.gitignore
.gitattributes

# Python 相關
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PYMANIFEST

# 虛擬環境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE 和編輯器
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 測試和覆蓋率
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# 日誌文件
*.log
logs/

# 數據文件（開發用）
data/
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
.env.local
.env.development
.env.test
.env.production
config/local/

# 臨時文件
tmp/
temp/
*.tmp
*.temp

# 文檔構建
docs/_build/
docs/build/
site/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# 安全相關
bandit-report.json

# 性能分析
profile.stats

# 備份文件
*.bak
*.backup

# 壓縮文件
*.zip
*.tar.gz
*.rar

# 系統文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker 相關
Dockerfile.dev
docker-compose.dev.yml
docker-compose.override.yml

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# 開發工具
Makefile.dev
*.md
README*
CHANGELOG*
LICENSE*
CONTRIBUTING*

# 依賴鎖定文件（在 Dockerfile 中單獨處理）
requirements-dev.txt
requirements-test.txt
requirements-frozen.txt

# 本地配置
local_settings.py
settings_local.py

# 媒體文件（如果不需要）
media/
static/uploads/

# 緩存目錄
.cache/
.pytest_cache/

# 本地數據庫文件
*.db-journal
*.db-wal
*.db-shm

# 本地開發腳本
scripts/dev/
scripts/local/

# 測試輸出
test-results/
test-reports/

# 本地環境特定文件
.env.example
.env.template