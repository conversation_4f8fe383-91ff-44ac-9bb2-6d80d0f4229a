# Pre-commit 配置文件
# 用於代碼質量檢查和自動格式化

repos:
  # Python 代碼格式化
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=100]

  # Import 排序
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=100]

  # 代碼風格檢查
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        args: [
          --max-line-length=100,
          --extend-ignore=E203,W503,E501,
          --exclude=.git,__pycache__,.venv,build,dist,*.egg-info
        ]

  # 類型檢查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-PyYAML]
        args: [--ignore-missing-imports, --no-strict-optional]

  # 安全檢查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, ., -f, json, -o, bandit-report.json]
        exclude: tests/

  # 基本檢查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # 檢查文件大小
      - id: check-added-large-files
        args: [--maxkb=1000]
      
      # 檢查 YAML 語法
      - id: check-yaml
      
      # 檢查 JSON 語法
      - id: check-json
      
      # 檢查合併衝突標記
      - id: check-merge-conflict
      
      # 檢查文件名
      - id: check-case-conflict
      
      # 移除尾隨空白
      - id: trailing-whitespace
        exclude: \.md$
      
      # 確保文件以換行符結尾
      - id: end-of-file-fixer
      
      # 檢查私鑰
      - id: detect-private-key
      
      # 檢查 requirements.txt
      - id: requirements-txt-fixer

  # Jupyter Notebook 清理
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.1
    hooks:
      - id: nbqa-black
        additional_dependencies: [black]
      - id: nbqa-isort
        additional_dependencies: [isort]

  # 文檔字符串檢查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: [--convention=google, --add-ignore=D100,D101,D102,D103,D104,D105]

# 全局配置
default_stages: [commit]
fail_fast: false

# CI 配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks
    
    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false