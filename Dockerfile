# 台灣股票預測系統 Dockerfile
# 基於 Python 3.11 的多階段構建

# 構建階段
FROM python:3.11-slim as builder

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    pkg-config \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安裝 TA-Lib C 庫
RUN wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz && \
    tar -xzf ta-lib-0.4.0-src.tar.gz && \
    cd ta-lib/ && \
    ./configure --prefix=/usr && \
    make && \
    make install && \
    cd .. && \
    rm -rf ta-lib ta-lib-0.4.0-src.tar.gz

# 升級 pip 和安裝構建工具
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# 複製依賴文件
COPY requirements.txt pyproject.toml ./

# 安裝 Python 依賴
RUN pip install --no-cache-dir -r requirements.txt

# 運行階段
FROM python:3.11-slim as runtime

# 設置環境變數
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    FLASK_APP=web_app.py \
    FLASK_ENV=production

# 創建非 root 用戶
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 安裝運行時依賴
RUN apt-get update && apt-get install -y \
    libopenblas0 \
    liblapack3 \
    libgomp1 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 從構建階段複製 TA-Lib 庫
COPY --from=builder /usr/lib/libta_lib.* /usr/lib/
COPY --from=builder /usr/include/ta-lib/ /usr/include/ta-lib/

# 從構建階段複製 Python 包
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 設置工作目錄
WORKDIR /app

# 創建必要的目錄
RUN mkdir -p /app/logs /app/data /app/static /app/templates && \
    chown -R appuser:appuser /app

# 複製應用程式代碼
COPY --chown=appuser:appuser . .

# 切換到非 root 用戶
USER appuser

# 健康檢查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8889/health || exit 1

# 暴露端口
EXPOSE 8889

# 啟動命令
CMD ["python", "web_app.py"]

# 多階段構建標籤
LABEL maintainer="Stock Prediction Team <<EMAIL>>" \
      version="1.0.0" \
      description="台灣股票預測系統" \
      org.opencontainers.image.title="Taiwan Stock Prediction System" \
      org.opencontainers.image.description="整合新聞分析與技術指標的智能股票預測平台" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="Stock Prediction Team" \
      org.opencontainers.image.licenses="MIT"