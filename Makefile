# Makefile for Taiwan Stock Prediction System
# 台灣股票預測系統 Makefile

.PHONY: help install install-dev test test-unit test-integration lint format type-check security-check clean run-web run-tests coverage docs build docker-build docker-run setup-hooks pre-commit

# 默認目標
help:
	@echo "台灣股票預測系統 - 可用命令:"
	@echo ""
	@echo "安裝和設置:"
	@echo "  install          安裝生產環境依賴"
	@echo "  install-dev      安裝開發環境依賴"
	@echo "  setup-hooks      設置 pre-commit hooks"
	@echo ""
	@echo "代碼質量:"
	@echo "  lint             運行代碼檢查 (flake8)"
	@echo "  format           格式化代碼 (black + isort)"
	@echo "  type-check       類型檢查 (mypy)"
	@echo "  security-check   安全檢查 (bandit)"
	@echo "  pre-commit       運行所有 pre-commit 檢查"
	@echo ""
	@echo "測試:"
	@echo "  test             運行所有測試"
	@echo "  test-unit        運行單元測試"
	@echo "  test-integration 運行集成測試"
	@echo "  coverage         生成測試覆蓋率報告"
	@echo ""
	@echo "運行:"
	@echo "  run-web          啟動 Web 應用"
	@echo "  run-download     下載股票數據"
	@echo ""
	@echo "文檔和構建:"
	@echo "  docs             生成文檔"
	@echo "  build            構建分發包"
	@echo "  clean            清理臨時文件"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build     構建 Docker 鏡像"
	@echo "  docker-run       運行 Docker 容器"

# Python 解釋器
PYTHON := python3
PIP := pip3

# 項目配置
PROJECT_NAME := tw-stock-prediction
SRC_DIR := .
TEST_DIR := tests
DOCS_DIR := docs
BUILD_DIR := build
DIST_DIR := dist

# 安裝依賴
install:
	@echo "安裝生產環境依賴..."
	$(PIP) install -r requirements.txt

install-dev:
	@echo "安裝開發環境依賴..."
	$(PIP) install -e ".[dev,testing,docs]"
	@echo "安裝 pre-commit..."
	pre-commit install

# 設置 pre-commit hooks
setup-hooks:
	@echo "設置 pre-commit hooks..."
	pre-commit install
	pre-commit install --hook-type commit-msg

# 代碼質量檢查
lint:
	@echo "運行代碼風格檢查..."
	flake8 $(SRC_DIR) --exclude=.venv,build,dist,*.egg-info

format:
	@echo "格式化代碼..."
	black $(SRC_DIR) --exclude="/(build|dist|\.venv|\.git)/"
	isort $(SRC_DIR) --skip-glob="*/.venv/*" --skip-glob="*/build/*" --skip-glob="*/dist/*"

type-check:
	@echo "運行類型檢查..."
	mypy $(SRC_DIR) --exclude="(build|dist|\.venv)"

security-check:
	@echo "運行安全檢查..."
	bandit -r $(SRC_DIR) -f json -o bandit-report.json || true
	@echo "安全檢查報告已生成: bandit-report.json"

pre-commit:
	@echo "運行所有 pre-commit 檢查..."
	pre-commit run --all-files

# 測試
test:
	@echo "運行所有測試..."
	pytest $(TEST_DIR) -v --tb=short

test-unit:
	@echo "運行單元測試..."
	pytest $(TEST_DIR)/unit -v --tb=short

test-integration:
	@echo "運行集成測試..."
	pytest $(TEST_DIR)/integration -v --tb=short

test-slow:
	@echo "運行慢速測試..."
	pytest $(TEST_DIR) -v --tb=short -m "slow"

test-api:
	@echo "運行 API 測試..."
	pytest $(TEST_DIR) -v --tb=short -m "api"

coverage:
	@echo "生成測試覆蓋率報告..."
	pytest $(TEST_DIR) --cov=$(SRC_DIR) --cov-report=html --cov-report=term-missing
	@echo "覆蓋率報告已生成: htmlcov/index.html"

# 運行應用
run-web:
	@echo "啟動 Web 應用..."
	$(PYTHON) web_app.py

run-download:
	@echo "下載股票數據..."
	$(PYTHON) download_twii_index.py

run-news-crawler:
	@echo "運行新聞爬蟲..."
	$(PYTHON) news_crawler.py

run-prediction:
	@echo "運行股價預測..."
	$(PYTHON) stock_prediction.py

# 文檔
docs:
	@echo "生成文檔..."
	@if [ -d "$(DOCS_DIR)" ]; then \
		cd $(DOCS_DIR) && make html; \
	else \
		echo "文檔目錄不存在，跳過文檔生成"; \
	fi

docs-serve:
	@echo "啟動文檔服務器..."
	@if [ -d "$(DOCS_DIR)/_build/html" ]; then \
		$(PYTHON) -m http.server 8080 -d $(DOCS_DIR)/_build/html; \
	else \
		echo "請先運行 'make docs' 生成文檔"; \
	fi

# 構建和分發
build:
	@echo "構建分發包..."
	$(PYTHON) -m build

build-wheel:
	@echo "構建 wheel 包..."
	$(PYTHON) -m build --wheel

build-sdist:
	@echo "構建源碼包..."
	$(PYTHON) -m build --sdist

# 清理
clean:
	@echo "清理臨時文件..."
	rm -rf $(BUILD_DIR)
	rm -rf $(DIST_DIR)
	rm -rf *.egg-info
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf .mypy_cache
	rm -rf __pycache__
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type f -name "*.log" -delete
	rm -f bandit-report.json

clean-all: clean
	@echo "深度清理..."
	rm -rf .venv
	rm -rf node_modules

# Docker
docker-build:
	@echo "構建 Docker 鏡像..."
	docker build -t $(PROJECT_NAME):latest .

docker-run:
	@echo "運行 Docker 容器..."
	docker run -p 8889:8889 --env-file .env $(PROJECT_NAME):latest

docker-run-dev:
	@echo "運行開發模式 Docker 容器..."
	docker run -p 8889:8889 -v $(PWD):/app --env-file .env $(PROJECT_NAME):latest

# 數據庫操作
db-init:
	@echo "初始化資料庫..."
	$(PYTHON) -c "from database.init_db import init_database; init_database()"

db-migrate:
	@echo "運行資料庫遷移..."
	@echo "資料庫遷移功能待實現"

db-seed:
	@echo "填充測試數據..."
	$(PYTHON) create_twii_table.py

# 開發工具
dev-setup: install-dev setup-hooks
	@echo "開發環境設置完成！"

dev-check: lint type-check security-check test
	@echo "開發檢查完成！"

dev-format: format
	@echo "代碼格式化完成！"

# 性能分析
profile:
	@echo "運行性能分析..."
	$(PYTHON) -m cProfile -o profile.stats web_app.py
	@echo "性能分析結果已保存到 profile.stats"

# 依賴管理
update-deps:
	@echo "更新依賴..."
	$(PIP) list --outdated

freeze-deps:
	@echo "凍結當前依賴版本..."
	$(PIP) freeze > requirements-frozen.txt

# 安全掃描
security-audit:
	@echo "運行安全審計..."
	$(PIP) audit

# 代碼統計
stats:
	@echo "代碼統計:"
	@echo "Python 文件數量:"
	@find . -name "*.py" -not -path "./.venv/*" -not -path "./build/*" -not -path "./dist/*" | wc -l
	@echo "代碼行數:"
	@find . -name "*.py" -not -path "./.venv/*" -not -path "./build/*" -not -path "./dist/*" -exec wc -l {} + | tail -1
	@echo "測試文件數量:"
	@find $(TEST_DIR) -name "*.py" | wc -l

# 快速命令別名
q-test: test-unit
q-lint: lint
q-format: format
q-check: lint type-check
q-clean: clean

# 幫助信息
info:
	@echo "項目信息:"
	@echo "  名稱: $(PROJECT_NAME)"
	@echo "  Python: $(shell $(PYTHON) --version)"
	@echo "  Pip: $(shell $(PIP) --version)"
	@echo "  工作目錄: $(PWD)"
	@echo "  源碼目錄: $(SRC_DIR)"
	@echo "  測試目錄: $(TEST_DIR)"