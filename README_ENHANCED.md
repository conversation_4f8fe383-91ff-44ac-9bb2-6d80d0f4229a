# 增強版股票分析系統

## 概述

這是一個使用現代軟件架構模式重構的股票分析系統，採用了依賴注入、統一錯誤處理、性能監控等最佳實踐。

## 🚀 新功能特性

### 1. 依賴注入架構
- **DIContainer**: 統一的依賴注入容器
- **接口抽象**: 定義了 `IDatabaseManager`、`IConfigManager`、`ILogger` 等接口
- **鬆耦合設計**: 組件間通過接口交互，便於測試和維護

### 2. 增強的配置管理
- **多格式支持**: JSON、INI 配置文件
- **環境變量覆蓋**: 支持通過環境變量動態配置
- **配置驗證**: 自動驗證配置的有效性
- **熱重載**: 支持配置的動態重載

### 3. 統一錯誤處理
- **自定義異常**: 定義了業務相關的異常類型
- **錯誤代碼**: 每個錯誤都有唯一的錯誤代碼
- **異常裝飾器**: `@handle_exception` 統一處理異常
- **錯誤追蹤**: 完整的錯誤堆棧和上下文信息

### 4. 性能監控
- **執行時間監控**: 自動記錄操作執行時間
- **數據庫操作監控**: 記錄查詢性能和影響行數
- **API調用監控**: 記錄外部API調用的性能
- **內存使用監控**: 監控系統資源使用情況

### 5. 增強的日誌系統
- **多級日誌**: 支持不同級別的日誌記錄
- **日誌輪轉**: 自動管理日誌文件大小和數量
- **結構化日誌**: 支持結構化的日誌格式
- **性能日誌**: 專門的性能監控日誌

### 6. 改進的數據庫管理
- **連接池**: 使用 SQLAlchemy 連接池管理數據庫連接
- **事務管理**: 完整的事務支持和自動回滾
- **會話管理**: 上下文管理器確保資源正確釋放
- **查詢優化**: 提供查詢性能監控和優化建議

## 📁 項目結構

```
stock-analysis/
├── config/
│   └── config.json              # 主配置文件
├── models/
│   ├── combined_model.py        # 增強的組合模型
│   ├── price_model.py          # 價格預測模型
│   └── news_model.py           # 新聞情緒模型
├── utils/
│   ├── dependency_injection.py  # 依賴注入容器
│   ├── enhanced_config_manager.py # 增強配置管理器
│   ├── enhanced_logger.py       # 增強日誌管理器
│   ├── enhanced_db_manager.py   # 增強數據庫管理器
│   ├── app_factory.py          # 應用程序工廠
│   └── exceptions.py           # 自定義異常
├── tests/
│   └── test_enhanced_architecture.py # 架構測試
├── main_enhanced.py            # 增強版主程序
└── README_ENHANCED.md          # 本文件
```

## 🛠️ 安裝和配置

### 1. 安裝依賴

```bash
pip install -r requirements.txt
```

### 2. 配置文件

創建 `config/config.json`：

```json
{
  "database": {
    "path": "data/stock_data.db",
    "pool_size": 10,
    "max_overflow": 20,
    "timeout": 30.0
  },
  "api": {
    "alpha_vantage_key": "YOUR_API_KEY",
    "news_api_key": "YOUR_NEWS_API_KEY",
    "request_timeout": 30.0,
    "max_retries": 3
  },
  "logging": {
    "level": "INFO",
    "log_dir": "logs",
    "max_file_size": 10485760,
    "backup_count": 5,
    "enable_console": true,
    "enable_file": true
  },
  "model": {
    "prediction_days": 30,
    "technical_indicators": [
      "SMA_20", "SMA_50", "EMA_12", "EMA_26",
      "RSI", "MACD", "MACD_signal", "BB_upper", "BB_lower"
    ],
    "sentiment_weight": 0.3,
    "technical_weight": 0.7,
    "model_save_dir": "models/saved"
  },
  "performance": {
    "cache_size": 1000,
    "cache_ttl_seconds": 3600,
    "batch_size": 100,
    "max_workers": 4
  }
}
```

### 3. 環境變量

可以通過環境變量覆蓋配置：

```bash
export STOCK_API__ALPHA_VANTAGE_KEY="your_api_key"
export STOCK_DATABASE__PATH="/path/to/database.db"
export STOCK_LOGGING__LEVEL="DEBUG"
```

## 🚀 使用方法

### 1. 命令行界面

```bash
# 預測股價
python main_enhanced.py predict --stock 2330 --days 30

# 訓練模型
python main_enhanced.py train --stock 2330 --start 2023-01-01 --end 2023-12-31

# 系統健康檢查
python main_enhanced.py health-check

# 配置管理
python main_enhanced.py config --show
python main_enhanced.py config --set model.prediction_days 60

# 數據統計
python main_enhanced.py data stats --stock 2330
```

### 2. 程序化使用

```python
from utils.app_factory import get_app_factory

# 創建應用程序工廠
with get_app_factory() as app_factory:
    # 獲取服務
    logger = app_factory.get_logger()
    config_manager = app_factory.get_config_manager()
    
    # 創建模型
    model = app_factory.create_combined_model()
    
    # 進行預測
    try:
        features = model.prepare_features('2330', '2023-01-01', '2023-12-31')
        predictions = model.predict('2330', 30)
        logger.info(f"預測完成: {predictions}")
    except Exception as e:
        logger.error(f"預測失敗: {e}")
```

### 3. 自定義服務

```python
from utils.dependency_injection import DIContainer
from utils.enhanced_logger import EnhancedLogger

# 創建容器
container = DIContainer()

# 註冊自定義服務
container.register_singleton('custom_service', lambda: MyCustomService())

# 註冊依賴其他服務的服務
container.register_transient(
    'complex_service',
    lambda: ComplexService(
        logger=container.resolve('logger'),
        config=container.resolve('config_manager')
    )
)

# 解析服務
service = container.resolve('complex_service')
```

## 🧪 測試

### 運行所有測試

```bash
python -m pytest tests/ -v
```

### 運行特定測試

```bash
python tests/test_enhanced_architecture.py
```

### 測試覆蓋率

```bash
pip install coverage
coverage run -m pytest tests/
coverage report
coverage html  # 生成HTML報告
```

## 📊 性能監控

### 1. 日誌監控

系統會自動記錄性能指標：

```
2024-01-01 10:00:00 | INFO | 性能記錄: prepare_features | operation=prepare_features | duration_ms=150.25
2024-01-01 10:00:01 | WARNING | 性能警告: slow_query 執行時間較長 | operation=slow_query | duration_ms=2500.0
```

### 2. 數據庫監控

```
2024-01-01 10:00:00 | INFO | 數據庫操作: SELECT stock_data | operation=SELECT | table=stock_data | affected_rows=1000 | duration_ms=45.2
```

### 3. API調用監控

```
2024-01-01 10:00:00 | INFO | API調用成功 | method=GET | url=https://api.example.com | status_code=200 | duration_ms=320.5
```

## 🔧 故障排除

### 1. 常見問題

**問題**: 配置文件加載失敗
```
ConfigurationError: 加載配置文件失敗: [Errno 2] No such file or directory
```
**解決**: 確保配置文件路徑正確，或使用 `--config` 參數指定配置文件

**問題**: 數據庫連接失敗
```
DatabaseConnectionError: 無法連接到數據庫: database is locked
```
**解決**: 檢查數據庫文件權限，確保沒有其他進程占用數據庫

**問題**: API密鑰無效
```
APIError: API調用失敗: Invalid API key
```
**解決**: 檢查配置文件中的API密鑰，或通過環境變量設置

### 2. 調試模式

啟用詳細日誌：

```bash
python main_enhanced.py --log-level DEBUG --verbose predict --stock 2330
```

### 3. 健康檢查

定期運行健康檢查：

```bash
python main_enhanced.py health-check
```

## 🔄 遷移指南

### 從舊版本遷移

1. **更新導入語句**:
   ```python
   # 舊版本
   from config.config_manager import ConfigManager
   from utils.logger import logger
   
   # 新版本
   from utils.app_factory import get_app_factory
   
   app_factory = get_app_factory()
   config_manager = app_factory.get_config_manager()
   logger = app_factory.get_logger()
   ```

2. **更新模型初始化**:
   ```python
   # 舊版本
   model = CombinedModel(config_manager)
   
   # 新版本
   model = app_factory.create_combined_model()
   ```

3. **更新錯誤處理**:
   ```python
   # 舊版本
   try:
       result = some_operation()
   except Exception as e:
       logger.error(f"操作失敗: {e}")
   
   # 新版本
   @handle_exception
   def some_operation():
       # 操作邏輯
       pass
   ```

## 🤝 貢獻指南

1. Fork 項目
2. 創建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

### 代碼規範

- 使用 `flake8` 進行代碼檢查
- 使用 `black` 進行代碼格式化
- 編寫單元測試
- 添加適當的文檔字符串

## 📝 更新日誌

### v2.0.0 (2024-01-01)
- ✨ 新增依賴注入架構
- ✨ 新增統一錯誤處理
- ✨ 新增性能監控
- ✨ 增強配置管理
- ✨ 改進日誌系統
- ✨ 優化數據庫管理
- 🐛 修復多個已知問題
- 📚 完善文檔和測試

### v1.0.0 (2023-12-01)
- 🎉 初始版本發布
- 📊 基本股票分析功能
- 🤖 機器學習預測模型
- 📰 新聞情緒分析

## 📄 許可證

MIT License - 詳見 [LICENSE](LICENSE) 文件

## 📞 支持

如有問題或建議，請：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索 [Issues](https://github.com/your-repo/issues)
3. 創建新的 Issue
4. 聯繫維護者

---

**注意**: 這是一個增強版本，包含了現代軟件開發的最佳實踐。建議在生產環境使用前進行充分測試。