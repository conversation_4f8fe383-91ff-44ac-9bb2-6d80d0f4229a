# analyzers/__init__.py

# 導入各個分析器
from .financial_analyzer import FinancialAnalyzer
from .news_analyzer import NewsAnalyzer
from .technical_analyzer import TechnicalAnalyzer

# 實現 AnalyzerFactory 類別
class AnalyzerFactory:
    """
    分析器工廠類別，負責建立不同類型的分析器
    """
    
    def create_analyzer(self, analyzer_type, database_path=None):
        """
        根據類型建立對應的分析器
        
        Args:
            analyzer_type (str): 分析器類型，可以是 'technical', 'news', 'financial'
            database_path (str): 資料庫路徑
            
        Returns:
            所請求的分析器實例
        """
        if analyzer_type == 'technical':
            return TechnicalAnalyzer(database_path) if database_path else TechnicalAnalyzer()
        elif analyzer_type == 'news':
            return NewsAnalyzer(database_path) if database_path else NewsAnalyzer()
        elif analyzer_type == 'financial':
            return FinancialAnalyzer(database_path) if database_path else FinancialAnalyzer()
        elif analyzer_type == 'news_price':
            from .news_price_analyzer import NewsPriceAnalyzer
            return NewsPriceAnalyzer(database_path) if database_path else NewsPriceAnalyzer()
        else:
            raise ValueError(f"不支援的分析器類型: {analyzer_type}")

# 確保 AnalyzerFactory 可以被導入
__all__ = ['AnalyzerFactory']