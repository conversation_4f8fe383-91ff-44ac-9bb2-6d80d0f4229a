import sqlite3


def create_stock_info_table(conn):
    cursor = conn.cursor()
    cursor.execute(
        """
    CREATE TABLE IF NOT EXISTS stock_info (
        stock_id TEXT PRIMARY KEY,
        name TEXT,
        industry TEXT,
        market TEXT,
        listed_date DATE,
        capital REAL,
        outstanding_shares REAL
    )
    """
    )
    conn.commit()


def create_daily_prices_table(conn):
    cursor = conn.cursor()
    cursor.execute(
        """
    CREATE TABLE IF NOT EXISTS daily_prices (
        stock_id TEXT,
        date DATE,
        open REAL,
        high REAL,
        low REAL,
        close REAL,
        volume INTEGER,
        PRIMARY KEY (stock_id, date)
    )
    """
    )
    conn.commit()


def create_technical_indicators_table(conn):
    cursor = conn.cursor()
    cursor.execute(
        """
    CREATE TABLE IF NOT EXISTS technical_indicators (
        stock_id TEXT,
        date DATE,
        MA5 REAL,
        MA20 REAL,
        MA60 REAL,
        RSI REAL,
        MACD REAL,
        KD REAL,
        PRIMARY KEY (stock_id, date)
    )
    """
    )
    conn.commit()


def create_financial_reports_table(conn):
    cursor = conn.cursor()
    cursor.execute(
        """
    CREATE TABLE IF NOT EXISTS financial_reports (
        stock_id TEXT,
        report_type TEXT,
        report_date DATE,
        revenue REAL,
        net_profit REAL,
        eps REAL,
        total_assets REAL,
        total_liabilities REAL,
        equity REAL,
        gross_profit_margin REAL,
        operating_profit_margin REAL,
        net_profit_margin REAL,
        PRIMARY KEY (stock_id, report_type, report_date)
    )
    """
    )
    conn.commit()


def create_stock_news_table(conn):
    cursor = conn.cursor()
    cursor.execute(
        """
    CREATE TABLE IF NOT EXISTS stock_news (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        content TEXT,
        url TEXT,
        publish_date DATE,
        source TEXT,
        fetch_date DATE,
        UNIQUE(url, publish_date)
    )
    """
    )
    conn.commit()


def main():
    # 連接資料庫
    conn = sqlite3.connect("tw_stock_data.db")

    try:
        # 創建所有表格
        create_stock_info_table(conn)
        create_daily_prices_table(conn)
        create_technical_indicators_table(conn)
        create_financial_reports_table(conn)
        create_stock_news_table(conn)

        print("所有表格已成功創建")

    finally:
        conn.close()


if __name__ == "__main__":
    main()
