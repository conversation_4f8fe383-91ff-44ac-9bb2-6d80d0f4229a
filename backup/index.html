<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台股預測系統</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #0d6efd, #0dcaf0);
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-bottom: 2rem;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card {
            border-radius: 10px;
            transition: transform 0.3s, box-shadow 0.3s;
            margin-bottom: 20px;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #0d6efd;
        }
        .feature-description {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .footer {
            background-color: #343a40;
            color: #f8f9fa;
            padding: 2rem 0;
            margin-top: 2rem;
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            padding: 8px 20px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            transform: scale(1.05);
        }
        .dashboard-summary {
            background-color: #fff;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .stock-trend {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }
        .trend-up {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }
        .trend-down {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
        .action-box {
            background-color: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .quick-stats {
            border-left: 5px solid #0d6efd;
            padding-left: 15px;
            margin: 15px 0;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .news-item {
            background-color: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s;
        }
        .news-item:hover {
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
        .sentiment-positive {
            color: #28a745;
        }
        .sentiment-negative {
            color: #dc3545;
        }
        .sentiment-neutral {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>台股預測系統
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">首頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">資料下載</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/predict">股票預測</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/batch_predict">批次預測</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/news">新聞分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/topics">熱門主題</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/train">模型訓練</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <input class="form-control me-2" type="search" placeholder="股票代碼" id="quickSearchInput">
                    <button class="btn btn-outline-light" onclick="quickSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 頁首 -->
    <header class="header">
        <div class="container">
            <h1 class="display-4">台股預測系統</h1>
            <p class="lead">結合技術分析與新聞情緒，讓您的投資決策更明智</p>
            <div class="mt-4">
                <a href="/predict" class="btn btn-light btn-lg me-2">
                    <i class="fas fa-chart-line me-2"></i>開始預測
                </a>
                <a href="/news" class="btn btn-outline-light btn-lg">
                    <i class="far fa-newspaper me-2"></i>新聞分析
                </a>
            </div>
        </div>
    </header>

    <!-- 主要內容 -->
    <main class="container">
        <!-- 儀表板摘要 -->
        <section class="dashboard-summary mb-4">
            <div class="row">
                <div class="col-md-4">
                    <h5>大盤走勢</h5>
                    <p class="fs-4">台灣加權指數: <span id="twii-index">--</span> 
                        <span class="stock-trend trend-up" id="twii-trend">
                            <i class="fas fa-caret-up"></i> --
                        </span>
                    </p>
                    <p class="text-muted">最後更新: <span id="last-update">--</span></p>
                </div>
                <div class="col-md-4">
                    <h5>今日預測結果</h5>
                    <div class="quick-stats">
                        <p>上漲預測: <span id="pred-up-count" class="fw-bold text-success">--</span> 檔股票</p>
                        <p>下跌預測: <span id="pred-down-count" class="fw-bold text-danger">--</span> 檔股票</p>
                        <p>平均信心度: <span id="pred-confidence" class="fw-bold">--%</span></p>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>熱門股票</h5>
                    <ul class="list-group list-group-flush" id="hot-stocks">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            載入中...
                        </li>
                    </ul>
                </div>
            </div>
            <div class="action-box text-center">
                <p class="mb-2">快速預測您的股票</p>
                <div class="d-flex justify-content-center">
                    <input type="text" class="form-control me-2" placeholder="請輸入股票代碼" id="homeStockInput" style="max-width: 200px;">
                    <button class="btn btn-primary" onclick="redirectToPrediction()">
                        <i class="fas fa-search me-1"></i> 開始分析
                    </button>
                </div>
            </div>
        </section>

        <!-- 功能區塊 -->
        <section class="row mb-4">
            <h3 class="mb-4">主要功能</h3>
            
            <div class="col-md-3">
                <div class="feature-card p-4">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h4>資料下載</h4>
                        <p class="feature-description">
                            下載最新的股價資料、技術指標、財務資料及新聞。資料同步是精準預測的基礎。
                        </p>
                        <a href="/download" class="btn btn-primary mt-3">下載資料</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="feature-card p-4">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>股票預測</h4>
                        <p class="feature-description">
                            結合技術指標和新聞情緒，預測單一股票的未來走勢。提供詳細分析圖表和相關指標解釋。
                        </p>
                        <a href="/predict" class="btn btn-primary mt-3">開始預測</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="feature-card p-4">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h4>新聞分析</h4>
                        <p class="feature-description">
                            分析特定股票的相關新聞情緒，找出市場情緒趨勢。自動識別正面、負面和中性新聞。
                        </p>
                        <a href="/news" class="btn btn-primary mt-3">查看新聞</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="feature-card p-4">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h4>批次預測</h4>
                        <p class="feature-description">
                            同時分析多檔股票，快速找出有潛力的投資標的。支援自定義排序和篩選條件。
                        </p>
                        <a href="/batch_predict" class="btn btn-primary mt-3">批次預測</a>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="row mb-4">
            <div class="col-md-4">
                <div class="feature-card p-4">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <h4>熱門主題</h4>
                        <p class="feature-description">
                            自動識別市場熱門話題和趨勢，追蹤相關股票表現。幫助您把握市場脈動。
                        </p>
                        <a href="/topics" class="btn btn-primary mt-3">查看主題</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card p-4">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4>模型訓練</h4>
                        <p class="feature-description">
                            自行訓練專屬的股票預測模型，提高預測準確率。可調整參數和資料範圍。
                        </p>
                        <a href="/train" class="btn btn-primary mt-3">訓練模型</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card p-4">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h4>API 服務</h4>
                        <p class="feature-description">
                            通過 API 調用預測功能，整合到您自己的應用程式中。支援程式化交互。
                        </p>
                        <a href="/api/docs" class="btn btn-primary mt-3">查看文檔</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 分析示例 -->
        <section class="row mb-4">
            <h3 class="mb-4">分析示例</h3>
            
            <div class="col-md-6">
                <div class="chart-container">
                    <h5>技術與新聞融合分析</h5>
                    <div class="text-center">
                        <img src="/static/sample_analysis.png" alt="分析示例" class="img-fluid">
                        <p class="mt-2 text-muted">台積電 (2330) 技術分析與新聞情緒融合結果</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="p-3">
                    <h5>最新相關新聞</h5>
                    <div id="sample-news">
                        <div class="news-item">
                            <div class="d-flex justify-content-between">
                                <h6>台積電將擴大3奈米產能，上修今年營收預期</h6>
                                <span class="sentiment-positive"><i class="fas fa-smile"></i> 正面</span>
                            </div>
                            <p class="text-muted mb-0">來源: 經濟日報 - 2025/04/02</p>
                        </div>
                        
                        <div class="news-item">
                            <div class="d-flex justify-content-between">
                                <h6>半導體產業展望：台積電、聯發科引領AI革命</h6>
                                <span class="sentiment-positive"><i class="fas fa-smile"></i> 正面</span>
                            </div>
                            <p class="text-muted mb-0">來源: 工商時報 - 2025/04/01</p>
                        </div>
                        
                        <div class="news-item">
                            <div class="d-flex justify-content-between">
                                <h6>全球晶片需求上升，台積電Q2可望優於預期</h6>
                                <span class="sentiment-neutral"><i class="fas fa-meh"></i> 中性</span>
                            </div>
                            <p class="text-muted mb-0">來源: 聯合報 - 2025/03/30</p>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <a href="/news?stock_id=2330" class="btn btn-outline-primary">查看更多新聞</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系統特色 -->
        <section class="mb-4">
            <h3 class="mb-4">系統特色</h3>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-group">
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            結合技術分析與新聞情緒的綜合預測
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            支援多檔股票批次預測與分析
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            自動識別與分類股票相關新聞
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            提供直觀的數據視覺化與圖表分析
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-group">
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            靈活的模型訓練與參數調整
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            多種技術指標和篩選條件
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            開放 API 接口，支援程式化交互
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            同時支援網頁版和桌面應用程式
                        </li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <!-- 頁尾 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>台股預測系統</h5>
                    <p>結合技術分析與新聞情緒，讓您的投資決策更明智。</p>
                </div>
                <div class="col-md-4">
                    <h5>功能導航</h5>
                    <ul class="list-unstyled">
                        <li><a href="/predict" class="text-light">股票預測</a></li>
                        <li><a href="/batch_predict" class="text-light">批次預測</a></li>
                        <li><a href="/news" class="text-light">新聞分析</a></li>
                        <li><a href="/topics" class="text-light">熱門主題</a></li>
                        <li><a href="/train" class="text-light">模型訓練</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>聯絡我們</h5>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p><i class="fas fa-phone me-2"></i> (02) 1234-5678</p>
                    <div class="mt-3">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-linkedin fa-lg"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-github fa-lg"></i></a>
                    </div>
                </div>
            </div>
            <hr class="mt-4 mb-4" style="border-color: rgba(255,255,255,0.1);">
            <div class="text-center">
                <p class="mb-0">© 2025 台股預測系統. 保留所有權利。</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 安全地存取localStorage
        const storage = {
            getItem: function(key) {
                try {
                    return window.localStorage.getItem(key);
                } catch (e) {
                    console.warn('無法使用 localStorage:', e);
                    return null;
                }
            },
            setItem: function(key, value) {
                try {
                    window.localStorage.setItem(key, value);
                    return true;
                } catch (e) {
                    console.warn('無法使用 localStorage:', e);
                    return false;
                }
            },
            removeItem: function(key) {
                try {
                    window.localStorage.removeItem(key);
                    return true;
                } catch (e) {
                    console.warn('無法使用 localStorage:', e);
                    return false;
                }
            }
        };
        
        // 快速搜尋功能
        function quickSearch() {
            const searchInput = document.getElementById('quickSearchInput');
            const stockId = searchInput.value.trim();
            if (stockId) {
                window.location.href = `/predict?stock_id=${stockId}`;
            }
        }

        // 首頁的股票分析重定向
        function redirectToPrediction() {
            const stockInput = document.getElementById('homeStockInput');
            const stockId = stockInput.value.trim();
            if (stockId) {
                window.location.href = `/predict?stock_id=${stockId}`;
            } else {
                alert('請輸入有效的股票代碼');
            }
        }

        // Enter 鍵觸發搜尋
        document.getElementById('quickSearchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                quickSearch();
            }
        });

        document.getElementById('homeStockInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                redirectToPrediction();
            }
        });

        // 模擬載入大盤資訊
        function loadMarketInfo() {
            setTimeout(() => {
                document.getElementById('twii-index').textContent = '24,126.45';
                document.getElementById('twii-trend').innerHTML = '<i class="fas fa-caret-up"></i> ****5%';
                document.getElementById('last-update').textContent = '2025/04/06 14:30';
            }, 1000);
        }

        // 模擬載入預測統計
        function loadPredictionStats() {
            setTimeout(() => {
                document.getElementById('pred-up-count').textContent = '127';
                document.getElementById('pred-down-count').textContent = '75';
                document.getElementById('pred-confidence').textContent = '73.8%';
            }, 1500);
        }

        // 模擬載入熱門股票
        function loadHotStocks() {
            const hotStocks = [
                {code: '2330', name: '台積電', trend: '****%', class: 'text-success'},
                {code: '2454', name: '聯發科', trend: '****%', class: 'text-success'},
                {code: '2317', name: '鴻海', trend: '-0.7%', class: 'text-danger'},
                {code: '2308', name: '台達電', trend: '****%', class: 'text-success'},
                {code: '2412', name: '中華電', trend: '+0.3%', class: 'text-success'}
            ];

            setTimeout(() => {
                const container = document.getElementById('hot-stocks');
                container.innerHTML = '';
                
                hotStocks.forEach(stock => {
                    container.innerHTML += `
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>
                                <strong>${stock.code}</strong> ${stock.name}
                            </span>
                            <span class="${stock.class}">
                                ${stock.trend}
                            </span>
                        </li>
                    `;
                });
            }, 1200);
        }

        // 頁面載入時執行
        window.onload = function() {
            loadMarketInfo();
            loadPredictionStats();
            loadHotStocks();
        }
    </script>
</body>
</html>