# 儲存此檔案為 stock_data_downloader.py

import os
import pandas as pd
import numpy as np
import datetime
import sqlite3
from finlab import data
import finlab
import time
import logging
from tqdm import tqdm

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("stock_data_download.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("台股資料下載")

class StockDataDownloader:
    def __init__(self, api_key_path=None, api_key=None, database_path=None):
        """初始化資料庫管理器"""
        if database_path is None:
            # 指定固定的絕對路徑，放在data子目錄下
            base_dir = "/Users/<USER>/python/training/stock/main-news/"
            self.database_path = os.path.join(base_dir, "database", "tw_stock_data.db")
            
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
        else:
            self.database_path = database_path
           
        # 設置 API 金鑰
        if api_key:
            self.api_key = api_key
        elif api_key_path:
            self.api_key = self.get_api_key(api_key_path)
        else:
            # 嘗試從預設位置讀取
            try:
                file_path = 'apikey.txt'
                with open(file_path, "r") as f:
                    self.api_key = f.read().strip()
                    logging.info(f"成功從 {file_path} 讀取金鑰。")
            except Exception as e:
                logging.error(f"讀取金鑰檔案時發生錯誤: {str(e)}")
                self.api_key = None
        
        # 確認是否已登入，若未登入則嘗試登入
        try:
            import finlab
            if self.api_key:
                finlab.login(self.api_key)
                logging.info("成功登入 FinLab API。")
        except Exception as e:
            logging.error(f"登入 FinLab API 時發生錯誤: {str(e)}")
        
        # 建立資料庫連接
        self.conn = sqlite3.connect(self.database_path)
        logger.info(f"已連接到資料庫: {self.database_path}")
    # 將此方法添加到 stock_data_downloader.py 檔案中的 StockDataDownloader 類
    def get_api_key(self, file_path):
        """從檔案讀取API金鑰

        Args:
            file_path (str): API金鑰檔案路徑

        Returns:
            str: API金鑰
        """
        try:
            with open(file_path, "r") as f:
                api_key = f.read().strip()
                logging.info(f"成功從 {file_path} 讀取金鑰。")
                return api_key
        except Exception as e:
            logging.error(f"讀取金鑰檔案時發生錯誤: {str(e)}")
            return None
    def download_stock_categories(self):
        """下載股票產業類別資料"""
        logger.info("開始下載股票類別資料...")
        
        try:
            # 股票類別
            categories = data.get("security_categories")
            # 顯示一些資訊以便偵錯
            logger.info(f"security_categories 資料形狀: {categories.shape}")
            if categories.shape[1] > 10:
                logger.info(f"欄位數量: {len(categories.columns)}, 前5個欄位: {list(categories.columns)[:5]}")
            
            # 保存到資料庫，使用stock_id作為索引
            self.save_dataframe_to_sql(categories, "stock_categories", index_name="stock_id")
            logger.info("已下載並保存股票類別資料")
        except Exception as e:
            logger.error(f"下載股票類別資料時發生錯誤: {e}") 
# 將此方法添加到 stock_data_downloader.py 檔案中的 StockDataDownloader 類

    def download_world_indices(self):
        """下載國際指數資料"""
        logger.info("開始下載國際指數資料...")
        
        # 國際指數列表
        indices = [
            "^GSPC",   # S&P 500
            "^DJI",    # 道瓊工業指數
            "^IXIC",   # 納斯達克指數
            "^TWII",   # 台灣加權指數
            "^HSI",    # 恆生指數
            "^N225",   # 日經225指數
            "^KS11"    # 韓國KOSPI指數
        ]
        
        for index_code in tqdm(indices, desc="下載國際指數"):
            try:
                # 嘗試下載各種指數數據
                indicators = {
                    "close": "world_index:close",
                    "open": "world_index:open",
                    "high": "world_index:high",
                    "low": "world_index:low",
                    "volume": "world_index:vol"
                }
                
                for indicator_name, data_key in indicators.items():
                    try:
                        # 使用特定指數代碼下載數據
                        logger.info(f"下載 {index_code} 的 {indicator_name} 資料...")
                        df = data.get(data_key, index_code)
                        
                        # 保存到資料庫
                        table_name = f"world_index_{index_code.replace('^', '')}_{indicator_name}"
                        self.save_dataframe_to_sql(df, table_name, index_name="date")
                        logger.info(f"已下載並保存 {index_code} 的 {indicator_name} 資料")
                    except Exception as e:
                        logger.error(f"下載 {index_code} 的 {indicator_name} 資料時發生錯誤: {e}")
                
                time.sleep(1)  # 避免API請求過快
            except Exception as e:
                logger.error(f"下載 {index_code} 指數時發生錯誤: {e}")
                # 繼續處理下一個項目，不要中斷整個過程
    # 修改 download_price_data 方法來處理特殊情況
    def download_price_data(self):
        """下載所有價格相關資料"""
        logger.info("開始下載價格相關資料...")
        # 設定更長的歷史範圍
        start_date = "2007-01-01"  # 增加至少10年的歷史數據
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')    
        # 從FinLab下載的價格資料
        data_items = {
            "price_close": "price:收盤價",
            "price_open": "price:開盤價",
            "price_high": "price:最高價",
            "price_low": "price:最低價",
            "price_volume": "price:成交股數",
            "price_value": "price:成交金額",
            "price_transactions": "price:成交筆數",
            "price_ask": "price:最後揭示買價",
            "price_bid": "price:最後揭示賣價"
        }
        
        for table_name, data_key in tqdm(data_items.items(), desc="下載價格資料"):
            try:
                logger.info(f"開始下載 {data_key} 資料...")
                # 使用指定日期範圍下載資料
                df = data.get(data_key, start_date=start_date, end_date=end_date)
                
                # 顯示一些資訊以便偵錯
                logger.info(f"{data_key} 資料形狀: {df.shape}, 列數: {len(df.columns)}")
                logger.info(f"時間範圍: {df.index.min()} 到 {df.index.max()}")
                
                 # 轉換為長格式保存
                self.save_dataframe_to_sql(df, table_name, index_name="date")
                
                # 建立索引以加速查詢
                try:
                    self.conn.execute(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_stock_id ON {table_name} (stock_id)")
                    self.conn.execute(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_date ON {table_name} (date)")
                except Exception as e:
                    logger.warning(f"建立索引時出錯: {e}")
                    
                logger.info(f"已下載並保存 {table_name}")
                time.sleep(1)  # 避免API請求過快
            except Exception as e:
                logger.error(f"下載 {table_name} 時發生錯誤: {e}")
    
    def download_financial_data(self):
        """下載財務相關資料"""
        logger.info("開始下載財務報表資料...")
        
        # 財務報表數據項目
        financial_items = {
            "financial_cash": "financial_statement:現金及約當現金",
            "financial_assets": "financial_statement:資產總額",
            "financial_liability": "financial_statement:負債總額",
            "financial_equity": "financial_statement:股東權益總額",
            "financial_revenue": "financial_statement:營業收入淨額",
            "financial_gross_profit": "financial_statement:營業毛利",
            "financial_operating_profit": "financial_statement:營業利益",
            "financial_net_income": "financial_statement:歸屬母公司淨利_損",
            "financial_eps": "financial_statement:每股盈餘"
        }
        
        for table_name, data_key in tqdm(financial_items.items(), desc="下載財務資料"):
            try:
                df = data.get(data_key)
                self.save_dataframe_to_sql(df, table_name, index_name="date")
                logger.info(f"已下載並保存 {table_name}")
                time.sleep(1)  # 避免API請求過快
            except Exception as e:
                logger.error(f"下載 {table_name} 時發生錯誤: {e}")
    
    def download_technical_indicators(self):
        """下載技術指標資料"""
        logger.info("開始下載技術指標資料...")
        
        # 計算與獲取技術指標
        indicators = {
            "technical_pe_ratio": "price_earning_ratio:本益比",
            "technical_dividend_yield": "price_earning_ratio:殖利率(%)",
            "technical_pb_ratio": "price_earning_ratio:股價淨值比",
            "technical_adj_close": "etl:adj_close",
            "technical_adj_open": "etl:adj_open",
            "technical_adj_high": "etl:adj_high",
            "technical_adj_low": "etl:adj_low"
        }
        
        for table_name, data_key in tqdm(indicators.items(), desc="下載技術指標"):
            try:
                df = data.get(data_key)
                self.save_dataframe_to_sql(df, table_name, index_name="date")
                logger.info(f"已下載並保存 {table_name}")
                time.sleep(1)  # 避免API請求過快
            except Exception as e:
                logger.error(f"下載 {table_name} 時發生錯誤: {e}")
    
    def download_institutional_investors_data(self):
        """下載三大法人資料"""
        logger.info("開始下載三大法人資料...")
        
        # 三大法人買賣資料
        institutional_items = {
            "institutional_foreign_buy": "institutional_investors_trading_summary:外陸資買進股數(不含外資自營商)",
            "institutional_foreign_sell": "institutional_investors_trading_summary:外陸資賣出股數(不含外資自營商)",
            "institutional_foreign_net": "institutional_investors_trading_summary:外陸資買賣超股數(不含外資自營商)",
            "institutional_investment_trust_buy": "institutional_investors_trading_summary:投信買進股數",
            "institutional_investment_trust_sell": "institutional_investors_trading_summary:投信賣出股數",
            "institutional_investment_trust_net": "institutional_investors_trading_summary:投信買賣超股數",
            "institutional_dealer_buy": "institutional_investors_trading_summary:自營商買進股數(自行買賣)",
            "institutional_dealer_sell": "institutional_investors_trading_summary:自營商賣出股數(自行買賣)",
            "institutional_dealer_net": "institutional_investors_trading_summary:自營商買賣超股數(自行買賣)"
        }
        
        for table_name, data_key in tqdm(institutional_items.items(), desc="下載三大法人資料"):
            try:
                df = data.get(data_key)
                self.save_dataframe_to_sql(df, table_name, index_name="date")
                logger.info(f"已下載並保存 {table_name}")
                time.sleep(1)  # 避免API請求過快
            except Exception as e:
                logger.error(f"下載 {table_name} 時發生錯誤: {e}")
            # 下載完成後，自動補充從證交所獲取的詳細資料
        try:
            self.download_institutional_trading_details()
        except Exception as e:
            logger.warning(f"下載證交所主力資料時發生錯誤: {e}")
    
    def download_margin_trading_data(self):
        """下載融資融券資料"""
        logger.info("開始下載融資融券資料...")
        
        # 融資融券資料
        margin_items = {
            "margin_trading_buy": "margin_transactions:融資買進",
            "margin_trading_sell": "margin_transactions:融資賣出",
            "margin_trading_balance": "margin_transactions:融資今日餘額",
            "short_selling_buy": "margin_transactions:融券買進",
            "short_selling_sell": "margin_transactions:融券賣出",
            "short_selling_balance": "margin_transactions:融券今日餘額"
        }
        
        for table_name, data_key in tqdm(margin_items.items(), desc="下載融資融券資料"):
            try:
                df = data.get(data_key)
                self.save_dataframe_to_sql(df, table_name, index_name="date")
                logger.info(f"已下載並保存 {table_name}")
                time.sleep(1)  # 避免API請求過快
            except Exception as e:
                logger.error(f"下載 {table_name} 時發生錯誤: {e}")
    
    def download_monthly_revenue(self):
        """下載月營收資料"""
        logger.info("開始下載月營收資料...")
        
        # 月營收資料
        revenue_items = {
            "monthly_revenue": "monthly_revenue:當月營收",
            "monthly_revenue_yoy": "monthly_revenue:去年同月增減(%)",
            "monthly_revenue_cumulative": "monthly_revenue:當月累計營收"
        }
        
        for table_name, data_key in tqdm(revenue_items.items(), desc="下載月營收資料"):
            try:
                df = data.get(data_key)
                self.save_dataframe_to_sql(df, table_name, index_name="date")
                logger.info(f"已下載並保存 {table_name}")
                time.sleep(1)  # 避免API請求過快
            except Exception as e:
                logger.error(f"下載 {table_name} 時發生錯誤: {e}")
    
    def download_company_info(self):
        """下載公司基本資料"""
        logger.info("開始下載公司基本資料...")
        
        try:
            # 公司基本資料
            company_info = data.get("company_basic_info")
            self.save_dataframe_to_sql(company_info, "company_info", index_name="stock_id")
            logger.info("已下載並保存公司基本資料")
        except Exception as e:
            logger.error(f"下載公司基本資料時發生錯誤: {e}")
    
    def download_economic_data(self):
        """下載經濟指標資料"""
        logger.info("開始下載經濟指標資料...")
        
        # 經濟指標資料
        economic_items = {
            "economic_business_indicators": "tw_business_indicators:景氣對策信號(分)",
            "economic_leading_index": "tw_business_indicators:領先指標綜合指數(點)",
            "economic_coincident_index": "tw_business_indicators:同時指標綜合指數(點)",
            "economic_lagging_index": "tw_business_indicators:落後指標綜合指數(點)",
            "economic_manufacturing_pmi": "tw_total_pmi:製造業PMI",
            "economic_non_manufacturing_nmi": "tw_total_nmi:臺灣非製造業NMI"
        }
        
        for table_name, data_key in tqdm(economic_items.items(), desc="下載經濟指標資料"):
            try:
                df = data.get(data_key)
                self.save_dataframe_to_sql(df, table_name, index_name="date")
                logger.info(f"已下載並保存 {table_name}")
                time.sleep(1)  # 避免API請求過快
            except Exception as e:
                logger.error(f"下載 {table_name} 時發生錯誤: {e}")
    
    # 修改 download_all_data 方法以更加穩健

# 修改 download_all_data 方法以更加穩健地處理缺少的方法

    def download_all_data(self):
        """下載所有可用資料"""
        logger.info("開始下載所有資料...")
        
        # 確認登入狀態
        if not self.api_key:
            logger.error("尚未登入FinLab API，無法下載資料")
            return
        
        # 用一個字典來保存下載函數及其描述
        download_functions = [
            ("公司基本資料", "download_company_info"),
            ("股票類別資料", "download_stock_categories"),
            ("價格相關資料", "download_price_data"),
            ("財務資料", "download_financial_data"),
            ("技術指標", "download_technical_indicators"),
            ("三大法人資料", "download_institutional_investors_data"),
            ("融資融券資料", "download_margin_trading_data"),
            ("月營收資料", "download_monthly_revenue"),
            ("經濟指標資料", "download_economic_data"),
            ("國際指數資料", "download_world_indices")
        ]
        
        # 逐一執行下載函數，任何一個失敗不會影響其他的下載
        for description, method_name in download_functions:
            try:
                logger.info(f"開始下載{description}...")
                # 檢查方法是否存在
                if hasattr(self, method_name):
                    method = getattr(self, method_name)
                    method()
                    logger.info(f"{description}下載完成")
                else:
                    logger.error(f"下載{description}的方法 {method_name} 不存在")
            except Exception as e:
                logger.error(f"下載{description}時發生錯誤: {e}")
                # 繼續下一個下載任務，不中斷整個過程
        
        logger.info("所有資料下載完成！")
    # 修改 stock_data_downloader.py 檔案中的 save_dataframe_to_sql 方法
    # 修改 save_dataframe_to_sql 方法以更好地處理主鍵衝突

    def save_dataframe_to_sql(self, df, table_name, index_name="date"):
        """將DataFrame保存為SQL資料表

        Args:
            df (DataFrame): 要保存的資料框
            table_name (str): 資料表名稱
            index_name (str, optional): 索引名稱. Defaults to "date".
        """
        try:
            # 確保索引被加入到DataFrame中
            df_copy = df.copy()
            if not df_copy.index.name and index_name:
                df_copy.index.name = index_name
            
            df_copy.reset_index(inplace=True)
            
            # 如果索引是時間戳，轉換為日期字符串
            if index_name == "date" and hasattr(df_copy, index_name) and len(df_copy) > 0:
                if hasattr(df_copy[index_name].iloc[0], 'strftime'):  # 檢查是否為日期型別
                    df_copy[index_name] = df_copy[index_name].dt.strftime('%Y-%m-%d')
            
            # 解決 too many columns 問題 - 處理寬格式資料
            # 將所有股票代碼作為欄位的資料轉換為長格式資料
            if df_copy.shape[1] > 2000:  # 欄位數量過多的閾值
                logger.info(f"處理寬格式資料: {table_name} 有 {df_copy.shape[1]} 個欄位，轉換為長格式")
                
                # 識別索引欄位
                id_vars = [col for col in df_copy.columns if col == index_name]
                
                # 將寬格式轉換為長格式
                df_long = pd.melt(df_copy, id_vars=id_vars, var_name='stock_id', value_name='value')
                
                # 去除可能的空值
                df_long = df_long.dropna()
                
                # 保存到資料庫 - 長格式
                try:
                    # 先嘗試直接保存
                    df_long.to_sql(table_name, self.conn, if_exists='replace', index=False)
                    logger.info(f"成功以長格式保存資料表 {table_name}，共 {len(df_long)} 筆資料")
                except Exception as e:
                    # 如果直接保存失敗，則分批保存
                    logger.error(f"保存長格式資料表 {table_name} 時發生錯誤: {e}")
                    logger.info(f"嘗試分批保存 {table_name}")
                    batch_size = 10000
                    for i in range(0, len(df_long), batch_size):
                        batch = df_long.iloc[i:i+batch_size]
                        batch.to_sql(f"{table_name}_batch{i//batch_size}", self.conn, if_exists='replace', index=False)
                    logger.info(f"成功使用批次方式保存長格式資料表 {table_name}")
            else:
                # 將None值轉換為NULL
                df_copy = df_copy.where(pd.notnull(df_copy), None)
                
                # 檢查是否是公司資訊相關表格，可能需要特殊處理
                if table_name in ['company_info', 'stock_categories']:
                    try:
                        # 先嘗試直接保存，如果表不存在就會創建
                        df_copy.to_sql(table_name, self.conn, if_exists='replace', index=False)
                        logger.info(f"成功保存資料表 {table_name}，共 {len(df_copy)} 筆資料")
                    except Exception as e:
                        # 如果出現主鍵衝突或其他錯誤，嘗試更新現有記錄
                        logger.error(f"保存資料表 {table_name} 時發生錯誤: {e}")
                        
                        # 建立臨時表
                        temp_table = f"{table_name}_temp"
                        df_copy.to_sql(temp_table, self.conn, if_exists='replace', index=False)
                        
                        # 從臨時表更新主表
                        cursor = self.conn.cursor()
                        if table_name == 'company_info':
                            primary_key = 'stock_id'
                        else:
                            primary_key = index_name
                        
                        # 獲取所有列名
                        cursor.execute(f"PRAGMA table_info({temp_table})")
                        columns = [row[1] for row in cursor.fetchall()]
                        
                        # 構建更新SQL
                        update_columns = [c for c in columns if c != primary_key]
                        update_sql = f"UPDATE {table_name} SET " + ", ".join([f"{c} = (SELECT {c} FROM {temp_table} WHERE {temp_table}.{primary_key} = {table_name}.{primary_key})" for c in update_columns]) + f" WHERE EXISTS (SELECT 1 FROM {temp_table} WHERE {temp_table}.{primary_key} = {table_name}.{primary_key})"
                        
                        # 構建插入SQL
                        insert_sql = f"INSERT INTO {table_name} SELECT * FROM {temp_table} WHERE NOT EXISTS (SELECT 1 FROM {table_name} WHERE {table_name}.{primary_key} = {temp_table}.{primary_key})"
                        
                        try:
                            cursor.execute(update_sql)
                            cursor.execute(insert_sql)
                            self.conn.commit()
                            logger.info(f"成功更新資料表 {table_name}")
                        except Exception as e2:
                            logger.error(f"更新資料表 {table_name} 時發生錯誤: {e2}")
                            # 回退到批次保存
                            batch_size = 100
                            for i in range(0, len(df_copy), batch_size):
                                batch = df_copy.iloc[i:i+batch_size]
                                batch.to_sql(f"{table_name}_batch{i//batch_size}", self.conn, if_exists='replace', index=False)
                            logger.info(f"成功使用批次方式保存資料表 {table_name}")
                        
                        # 刪除臨時表
                        cursor.execute(f"DROP TABLE IF EXISTS {temp_table}")
                        self.conn.commit()
                else:
                    # 保存到資料庫 - 原始格式
                    df_copy.to_sql(table_name, self.conn, if_exists='replace', index=False)
                    logger.info(f"成功保存資料表 {table_name}，共 {len(df_copy)} 筆資料")
        except Exception as e:
            logger.error(f"保存資料表 {table_name} 時發生錯誤: {e}")
            
            # 嘗試採用其他方式保存
            try:
                logger.info(f"嘗試使用備用方法保存 {table_name}")
                
                # 備用方法1: 逐批保存
                batch_size = 1000
                for i in range(0, len(df_copy), batch_size):
                    batch = df_copy.iloc[i:i+batch_size]
                    batch.to_sql(f"{table_name}_batch{i//batch_size}", self.conn, if_exists='replace', index=False)
                
                logger.info(f"成功使用批次方式保存資料表 {table_name}")
            except Exception as e2:
                logger.error(f"備用保存方法也失敗: {e2}")
    
    def close(self):
        """關閉資料庫連接"""
        if self.conn:
            self.conn.close()
            logger.info("已關閉資料庫連接")
    def download_institutional_trading_details(self):
        """下載主力與非主力交易明細 (使用證交所API)"""
        logger.info("開始下載主力交易明細資料...")
        
        # 建立資料表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS institutional_trading_details (
            date TEXT,
            stock_id TEXT,
            security_name TEXT,
            foreign_investors_buy REAL,
            foreign_investors_sell REAL,
            foreign_investors_diff REAL,
            investment_trust_buy REAL,
            investment_trust_sell REAL,
            investment_trust_diff REAL,
            dealers_proprietary_buy REAL,
            dealers_proprietary_sell REAL,
            dealers_proprietary_diff REAL,
            dealers_hedge_buy REAL,
            dealers_hedge_sell REAL,
            dealers_hedge_diff REAL,
            total_institutional_buy REAL,
            total_institutional_sell REAL,
            total_institutional_diff REAL,
            non_institutional_buy REAL,
            non_institutional_sell REAL,
            non_institutional_diff REAL,
            PRIMARY KEY (date, stock_id)
        )
        """
        
        self.conn.execute(create_table_sql)
        self.conn.commit()
        
        # 初始日期範圍（最近一年）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        # 查詢是否已有資料，如有則只下載最新的資料
        cursor = self.conn.cursor()
        cursor.execute("SELECT MAX(date) FROM institutional_trading_details")
        result = cursor.fetchone()
        if result[0]:
            latest_date = datetime.strptime(result[0], '%Y-%m-%d')
            # 設定起始日期為最新日期的下一天
            start_date = latest_date + timedelta(days=1)
            if start_date >= end_date:
                logger.info("主力交易資料已是最新，無需下載")
                return
        
        # 逐日下載
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime('%Y%m%d')
            logger.info(f"下載 {date_str} 的主力交易明細...")
            
            try:
                # 呼叫證交所API
                url = f"https://openapi.twse.com.tw/v1/exchangeReport/FMNPTK_ALL?date={date_str}"
                response = requests.get(url)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 轉換並儲存資料
                    records = []
                    for item in data:
                        stock_id = item.get('證券代號', '')
                        if not stock_id:
                            continue
                        
                        # 將逗號分隔的數字字串轉為浮點數
                        def parse_number(value):
                            try:
                                return float(value.replace(',', ''))
                            except:
                                return 0.0
                                
                        # 準備資料記錄
                        record = (
                            current_date.strftime('%Y-%m-%d'),  # 日期
                            stock_id,                          # 股票代碼
                            item.get('證券名稱', ''),           # 股票名稱
                            parse_number(item.get('外陸資買進股數', '0')),
                            parse_number(item.get('外陸資賣出股數', '0')),
                            parse_number(item.get('外陸資買賣超股數', '0')),
                            parse_number(item.get('投信買進股數', '0')),
                            parse_number(item.get('投信賣出股數', '0')),
                            parse_number(item.get('投信買賣超股數', '0')),
                            parse_number(item.get('自營商買進股數(自行)', '0')),
                            parse_number(item.get('自營商賣出股數(自行)', '0')),
                            parse_number(item.get('自營商買賣超股數(自行)', '0')),
                            parse_number(item.get('自營商買進股數(避險)', '0')),
                            parse_number(item.get('自營商賣出股數(避險)', '0')),
                            parse_number(item.get('自營商買賣超股數(避險)', '0')),
                            parse_number(item.get('三大法人買賣合計股數', '0')),
                            0.0,  # 初始化三大法人賣出合計
                            0.0,  # 初始化三大法人買賣超合計
                            0.0,  # 初始化非主力買入
                            0.0,  # 初始化非主力賣出
                            0.0   # 初始化非主力買賣超
                        )
                        records.append(record)
                    
                    # 插入資料庫
                    if records:
                        insert_sql = """
                        INSERT OR REPLACE INTO institutional_trading_details VALUES 
                        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        self.conn.executemany(insert_sql, records)
                        self.conn.commit()
                        
                        logger.info(f"成功下載並儲存 {len(records)} 筆 {date_str} 的主力交易明細")
                    else:
                        logger.info(f"{date_str} 無交易資料或為非交易日")
                else:
                    logger.warning(f"API返回狀態碼 {response.status_code}，無法取得 {date_str} 的資料")
            
            except Exception as e:
                logger.error(f"下載 {date_str} 的主力交易明細時發生錯誤: {e}")
            
            # 前進到下一天
            current_date += timedelta(days=1)
            time.sleep(1)  # 避免請求過於頻繁
        
        # 計算非主力交易資訊
        self.calculate_non_institutional_trading()

    def calculate_non_institutional_trading(self):
        """計算非主力買賣資訊（總成交量減去三大法人）"""
        logger.info("開始計算非主力買賣資訊...")
        
        try:
            # 讀取三大法人資料
            inst_data = pd.read_sql(
                "SELECT date, stock_id, total_institutional_buy, total_institutional_sell FROM institutional_trading_details",
                self.conn
            )
            
            # 讀取成交量資料
            vol_data = pd.read_sql(
                "SELECT date, stock_id, volume FROM price_volume",
                self.conn
            )
            
            # 合併資料
            merged_data = pd.merge(
                inst_data,
                vol_data,
                on=['date', 'stock_id'],
                how='inner'
            )
            
            # 計算非主力買賣
            # 假設買賣各占總成交量的一半
            merged_data['non_institutional_buy'] = (merged_data['volume'] * 0.5 - merged_data['total_institutional_buy']).clip(lower=0)
            merged_data['non_institutional_sell'] = (merged_data['volume'] * 0.5 - merged_data['total_institutional_sell']).clip(lower=0)
            merged_data['non_institutional_diff'] = merged_data['non_institutional_buy'] - merged_data['non_institutional_sell']
            
            # 更新資料庫
            cursor = self.conn.cursor()
            for _, row in merged_data.iterrows():
                update_sql = """
                UPDATE institutional_trading_details 
                SET non_institutional_buy = ?,
                    non_institutional_sell = ?,
                    non_institutional_diff = ?
                WHERE date = ? AND stock_id = ?
                """
                cursor.execute(update_sql, (
                    row['non_institutional_buy'],
                    row['non_institutional_sell'],
                    row['non_institutional_diff'],
                    row['date'],
                    row['stock_id']
                ))
            
            self.conn.commit()
            logger.info("非主力買賣資訊計算完成")
            
        except Exception as e:
            logger.error(f"計算非主力買賣資訊時發生錯誤: {e}")

# 測試用程式碼
if __name__ == "__main__":
    # 初始化下載器，提供API金鑰路徑或直接提供API金鑰
    api_key_path = "/Users/<USER>/python/training/股票發大財/api_key.txt"
    downloader = StockDataDownloader(api_key_path=api_key_path)
    
    # 下載所有資料
    try:
        downloader.download_all_data()
    finally:
        # 確保資料庫連接被正確關閉
        downloader.close()