#!/usr/bin/env python3
"""
檢查缺失股票的腳本
比較FinLab股票清單與資料庫中的股票
"""

import os
import sys
import sqlite3

# 將專案根目錄添加到 Python 路徑
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, PROJECT_ROOT)

def check_finlab_availability():
    """檢查FinLab是否可用"""
    try:
        import finlab
        print("✅ FinLab 模組可用")
        return True
    except ImportError:
        print("❌ FinLab 模組未安裝")
        print("   請執行: pip install finlab")
        return False

def get_finlab_stock_list():
    """獲取FinLab的完整股票清單"""
    try:
        import finlab
        from finlab import data
        
        print("🔍 正在從FinLab獲取股票清單...")
        all_prices = data.get('price:收盤價')
        stock_list = all_prices.columns.tolist()
        
        print(f"📊 FinLab股票總數: {len(stock_list)}")
        
        # 分析股票分布
        distribution = {}
        for stock in stock_list:
            if stock.startswith('00'):
                key = 'ETF/基金'
            elif stock.startswith('1'):
                key = '1000-1999'
            elif stock.startswith('2'):
                key = '2000-2999'
            elif stock.startswith('3'):
                key = '3000-3999'
            elif stock.startswith('4'):
                key = '4000-4999'
            elif stock.startswith('5'):
                key = '5000-5999'
            elif stock.startswith('6'):
                key = '6000-6999'
            elif stock.startswith('8'):
                key = '8000-8999'
            elif stock.startswith('9'):
                key = '9000-9999'
            else:
                key = '其他'
            
            distribution[key] = distribution.get(key, 0) + 1
        
        print("📈 FinLab股票分布:")
        for category, count in sorted(distribution.items()):
            print(f"   {category}: {count} 支")
        
        return stock_list
        
    except Exception as e:
        print(f"❌ 獲取FinLab股票清單失敗: {e}")
        return []

def get_database_stock_list():
    """獲取資料庫中的股票清單"""
    try:
        db_path = os.path.join(PROJECT_ROOT, "database", "tw_stock_data.db")
        
        if not os.path.exists(db_path):
            print("❌ 資料庫文件不存在")
            return []
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT stock_id FROM daily_prices ORDER BY stock_id")
        stocks = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 資料庫股票總數: {len(stocks)}")
        
        # 分析股票分布
        distribution = {}
        for stock in stocks:
            if stock.startswith('00'):
                key = 'ETF/基金'
            elif stock.startswith('1'):
                key = '1000-1999'
            elif stock.startswith('2'):
                key = '2000-2999'
            elif stock.startswith('3'):
                key = '3000-3999'
            elif stock.startswith('4'):
                key = '4000-4999'
            elif stock.startswith('5'):
                key = '5000-5999'
            elif stock.startswith('6'):
                key = '6000-6999'
            elif stock.startswith('8'):
                key = '8000-8999'
            elif stock.startswith('9'):
                key = '9000-9999'
            else:
                key = '其他'
            
            distribution[key] = distribution.get(key, 0) + 1
        
        print("📈 資料庫股票分布:")
        for category, count in sorted(distribution.items()):
            print(f"   {category}: {count} 支")
        
        conn.close()
        return stocks
        
    except Exception as e:
        print(f"❌ 獲取資料庫股票清單失敗: {e}")
        return []

def compare_stock_lists():
    """比較FinLab和資料庫的股票清單"""
    print("🔄 比較股票清單...")
    print("=" * 60)
    
    # 檢查FinLab可用性
    if not check_finlab_availability():
        return
    
    # 獲取兩個清單
    finlab_stocks = set(get_finlab_stock_list())
    db_stocks = set(get_database_stock_list())
    
    if not finlab_stocks or not db_stocks:
        print("❌ 無法獲取完整的股票清單")
        return
    
    print("\n" + "=" * 60)
    print("📊 比較結果:")
    
    # 計算差異
    missing_in_db = finlab_stocks - db_stocks
    extra_in_db = db_stocks - finlab_stocks
    common_stocks = finlab_stocks & db_stocks
    
    print(f"   FinLab總數: {len(finlab_stocks)} 支")
    print(f"   資料庫總數: {len(db_stocks)} 支")
    print(f"   共同股票: {len(common_stocks)} 支")
    print(f"   資料庫缺失: {len(missing_in_db)} 支")
    print(f"   資料庫多餘: {len(extra_in_db)} 支")
    
    # 顯示缺失的股票
    if missing_in_db:
        missing_list = sorted(list(missing_in_db))
        print(f"\n⚠️  資料庫中缺失的股票 ({len(missing_list)} 支):")
        
        # 按類別分組顯示
        categories = {}
        for stock in missing_list:
            if stock.startswith('00'):
                key = 'ETF/基金'
            elif stock.startswith('1'):
                key = '1000-1999'
            elif stock.startswith('2'):
                key = '2000-2999'
            elif stock.startswith('3'):
                key = '3000-3999'
            elif stock.startswith('4'):
                key = '4000-4999'
            elif stock.startswith('5'):
                key = '5000-5999'
            elif stock.startswith('6'):
                key = '6000-6999'
            elif stock.startswith('8'):
                key = '8000-8999'
            elif stock.startswith('9'):
                key = '9000-9999'
            else:
                key = '其他'
            
            if key not in categories:
                categories[key] = []
            categories[key].append(stock)
        
        for category, stocks in sorted(categories.items()):
            print(f"   {category}: {len(stocks)} 支")
            print(f"      範例: {stocks[:10]}...")
        
        print(f"\n💡 建議執行:")
        print(f"   python main.py --download-missing-stocks")
        print(f"   # 這將下載所有 {len(missing_list)} 支缺失的股票")
    
    # 顯示多餘的股票
    if extra_in_db:
        extra_list = sorted(list(extra_in_db))
        print(f"\n📝 資料庫中多餘的股票 ({len(extra_list)} 支):")
        print(f"   範例: {extra_list[:20]}...")
        print(f"   # 這些股票在FinLab中不存在，可能是已下市或代碼變更")

def main():
    """主函數"""
    print("🔍 檢查缺失股票")
    print("=" * 60)
    
    try:
        compare_stock_lists()
        
        print("\n" + "=" * 60)
        print("✨ 檢查完成!")
        print("\n📖 可用命令:")
        print("   python main.py --download-missing-stocks  # 下載缺失股票")
        print("   python main.py --check-missing           # 檢查數據完整性")
        print("   python main.py --download-finlab          # 增量下載")
        
    except Exception as e:
        print(f"❌ 檢查過程發生錯誤: {e}")

if __name__ == "__main__":
    main()
