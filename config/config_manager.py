import os
from configparser import ConfigParser
from pathlib import Path

class ConfigManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.config = ConfigParser()
        config_path = Path(__file__).parent / 'config.ini'
        self.config.read(config_path)
        self._initialized = True
    
    @property
    def app_config(self):
        return {
            'secret_key': self.config.get('app', 'secret_key'),
            'debug': self.config.getboolean('app', 'debug'),
            'port': self.config.getint('app', 'port')
        }
    
    @property
    def api_config(self):
        return {
            'base_url': self.config.get('api', 'base_url')
        }
    
    @property
    def db_config(self):
        return {
            'path': self.config.get('database', 'path')
        }
    
    @property
    def logging_config(self):
        return {
            'level': self.config.get('logging', 'level'),
            'file': self.config.get('logging', 'file'),
            'max_size': self.config.getint('logging', 'max_size'),
            'backup_count': self.config.getint('logging', 'backup_count')
        }
    
    @property
    def security_config(self):
        return {
            'allowed_hosts': self.config.get('security', 'allowed_hosts').split(','),
            'csrf_protection': self.config.getboolean('security', 'csrf_protection')
        } 