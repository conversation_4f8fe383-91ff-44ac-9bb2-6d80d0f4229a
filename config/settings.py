"""應用配置管理模組

使用 pydantic 進行配置驗證和管理
"""

import os
from pathlib import Path
from typing import List, Optional

from pydantic import BaseSettings, Field, validator


class DatabaseSettings(BaseSettings):
    """資料庫配置"""

    url: str = Field(default="sqlite:///database/tw_stock_data.db", env="DATABASE_URL")
    echo: bool = Field(default=False, env="DATABASE_ECHO")
    pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    max_overflow: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")


class LoggingSettings(BaseSettings):
    """日誌配置"""

    level: str = Field(default="INFO", env="LOG_LEVEL")
    file: str = Field(default="logs/app.log", env="LOG_FILE")
    max_bytes: int = Field(default=10485760, env="LOG_MAX_BYTES")  # 10MB
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT"
    )

    @validator("level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"日誌級別必須是 {valid_levels} 之一")
        return v.upper()


class FlaskSettings(BaseSettings):
    """Flask 應用配置"""

    env: str = Field(default="development", env="FLASK_ENV")
    debug: bool = Field(default=True, env="FLASK_DEBUG")
    host: str = Field(default="localhost", env="FLASK_HOST")
    port: int = Field(default=8889, env="FLASK_PORT")
    secret_key: str = Field(default="dev-secret-key", env="SECRET_KEY")
    csrf_secret_key: str = Field(default="dev-csrf-key", env="CSRF_SECRET_KEY")

    @validator("port")
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError("端口號必須在 1-65535 之間")
        return v


class APISettings(BaseSettings):
    """API 配置"""

    finlab_api_key: Optional[str] = Field(default=None, env="FINLAB_API_KEY")
    rate_limit: int = Field(default=100, env="API_RATE_LIMIT")  # 每分鐘
    timeout: int = Field(default=30, env="API_TIMEOUT")  # 秒
    retry_attempts: int = Field(default=3, env="API_RETRY_ATTEMPTS")
    retry_delay: float = Field(default=1.0, env="API_RETRY_DELAY")  # 秒


class CrawlerSettings(BaseSettings):
    """爬蟲配置"""

    news_crawl_interval: int = Field(default=3600, env="NEWS_CRAWL_INTERVAL")  # 秒
    max_news_per_stock: int = Field(default=50, env="MAX_NEWS_PER_STOCK")
    user_agent: str = Field(
        default="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        env="CRAWLER_USER_AGENT",
    )
    request_delay: float = Field(default=1.0, env="CRAWLER_REQUEST_DELAY")  # 秒


class PredictionSettings(BaseSettings):
    """預測模型配置"""

    prediction_days: int = Field(default=30, env="PREDICTION_DAYS")
    model_retrain_interval: int = Field(default=86400, env="MODEL_RETRAIN_INTERVAL")  # 秒
    min_training_data_days: int = Field(default=252, env="MIN_TRAINING_DATA_DAYS")  # 一年交易日
    validation_split: float = Field(default=0.2, env="VALIDATION_SPLIT")

    @validator("validation_split")
    def validate_split(cls, v):
        if not 0 < v < 1:
            raise ValueError("驗證集比例必須在 0 和 1 之間")
        return v


class TechnicalIndicatorSettings(BaseSettings):
    """技術指標配置"""

    ma_periods: List[int] = Field(default=[5, 10, 20, 60], env="MA_PERIODS")
    rsi_period: int = Field(default=14, env="RSI_PERIOD")
    macd_fast: int = Field(default=12, env="MACD_FAST")
    macd_slow: int = Field(default=26, env="MACD_SLOW")
    macd_signal: int = Field(default=9, env="MACD_SIGNAL")
    bollinger_period: int = Field(default=20, env="BOLLINGER_PERIOD")
    bollinger_std: float = Field(default=2.0, env="BOLLINGER_STD")

    @validator("ma_periods", pre=True)
    def parse_ma_periods(cls, v):
        if isinstance(v, str):
            return [int(x.strip()) for x in v.split(",")]
        return v


class CacheSettings(BaseSettings):
    """緩存配置"""

    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    timeout: int = Field(default=300, env="CACHE_TIMEOUT")  # 秒
    enabled: bool = Field(default=False, env="CACHE_ENABLED")


class Settings(BaseSettings):
    """主配置類"""

    # 基本設置
    app_name: str = Field(default="台股預測系統", env="APP_NAME")
    version: str = Field(default="1.0.0", env="APP_VERSION")
    environment: str = Field(default="development", env="ENVIRONMENT")

    # 子配置
    database: DatabaseSettings = DatabaseSettings()
    logging: LoggingSettings = LoggingSettings()
    flask: FlaskSettings = FlaskSettings()
    api: APISettings = APISettings()
    crawler: CrawlerSettings = CrawlerSettings()
    prediction: PredictionSettings = PredictionSettings()
    technical: TechnicalIndicatorSettings = TechnicalIndicatorSettings()
    cache: CacheSettings = CacheSettings()

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()

    def _ensure_directories(self):
        """確保必要的目錄存在"""
        directories = [
            Path(self.logging.file).parent,
            Path(self.database.url.replace("sqlite:///", "")).parent,
            Path("logs"),
            Path("database"),
            Path("static"),
            Path("templates"),
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    @property
    def is_development(self) -> bool:
        """是否為開發環境"""
        return self.environment.lower() == "development"

    @property
    def is_production(self) -> bool:
        """是否為生產環境"""
        return self.environment.lower() == "production"

    def get_database_url(self) -> str:
        """獲取完整的資料庫 URL"""
        if self.database.url.startswith("sqlite:///"):
            # 確保 SQLite 路徑是絕對路徑
            db_path = self.database.url.replace("sqlite:///", "")
            if not os.path.isabs(db_path):
                db_path = os.path.abspath(db_path)
            return f"sqlite:///{db_path}"
        return self.database.url


# 全局設置實例
settings = Settings()


def get_settings() -> Settings:
    """獲取設置實例"""
    return settings


if __name__ == "__main__":
    # 測試配置
    print("=== 配置測試 ===")
    print(f"應用名稱: {settings.app_name}")
    print(f"版本: {settings.version}")
    print(f"環境: {settings.environment}")
    print(f"資料庫 URL: {settings.get_database_url()}")
    print(f"日誌級別: {settings.logging.level}")
    print(f"Flask 端口: {settings.flask.port}")
    print(f"FinLab API Key: {'已設置' if settings.api.finlab_api_key else '未設置'}")
    print(f"移動平均週期: {settings.technical.ma_periods}")
