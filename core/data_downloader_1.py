# core/data_downloader.py
import logging
import os
import sqlite3
import time
import traceback
from datetime import datetime, timedelta

import pandas as pd
import requests


class CoreDataDownloader:
    """核心資料下載模組，同時被桌面版和網頁版使用"""

    def __init__(self, api_key=None, database_path=None):
        """初始化下載器

        Args:
            api_key (str, optional): FinLab API金鑰
            database_path (str): 資料庫路徑
        """
        # 若未提供database_path，則使用預設路徑
        if database_path is None:
            base_dir = "/Users/<USER>/python/training/stock/main-news/"
            database_path = os.path.join(base_dir, "database", "tw_stock_data.db")

        self.database_path = database_path
        self.api_key = api_key  # 正確存儲 API 金鑰
        self.conn = None
        self.logger = logging.getLogger("CoreDataDownloader")

        # 記錄使用的資料庫路徑
        self.logger.info(f"使用資料庫路徑: {self.database_path}")

        # 確保目錄存在並具有寫入權限
        db_dir = os.path.dirname(self.database_path)
        if db_dir:
            if not os.path.exists(db_dir):
                try:
                    os.makedirs(db_dir)
                    self.logger.info(f"已創建資料庫目錄: {db_dir}")
                except Exception as e:
                    self.logger.error(f"創建資料庫目錄失敗: {str(e)}")
            else:
                # 檢查寫入權限
                if not os.access(db_dir, os.W_OK):
                    self.logger.warning(f"警告：資料庫目錄 {db_dir} 沒有寫入權限")

        # 連接資料庫
        self._connect_database()

    def connect_database(self):
        """連接或創建資料庫"""
        try:
            if self.core_downloader is None:
                # 加入更多日誌
                self.logger.info(f"正在初始化 CoreDataDownloader，資料庫路徑: {self.database_path}")
                # 檢查資料庫目錄是否存在
                db_dir = os.path.dirname(self.database_path)
                self.logger.info(f"資料庫目錄: {db_dir}, 是否存在: {os.path.exists(db_dir)}")
                # 檢查是否有寫入權限
                if os.path.exists(db_dir):
                    self.logger.info(f"目錄寫入權限: {os.access(db_dir, os.W_OK)}")

                # 嘗試創建資料庫目錄
                os.makedirs(db_dir, exist_ok=True)

                # 確保導入了 CoreDataDownloader
                from core.data_downloader import CoreDataDownloader

                self.core_downloader = CoreDataDownloader(self.api_key, self.database_path)
                self.logger.info(f"成功初始化 CoreDataDownloader")
                return True
            return True
        except Exception as e:
            self.logger.error(f"連接資料庫時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def _create_tables(self):
        """創建必要的資料表"""
        if not self.conn:
            self._connect_database()

        cursor = self.conn.cursor()

        # 股票基本資訊表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS stock_info (
            stock_id TEXT PRIMARY KEY,
            name TEXT,
            industry TEXT,
            market TEXT,
            listed_date DATE,
            capital REAL,
            outstanding_shares REAL
        )
        """
        )

        # 每日股價表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS daily_prices (
            stock_id TEXT,
            date DATE,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume INTEGER,
            PRIMARY KEY (stock_id, date)
        )
        """
        )

        # 技術指標表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS technical_indicators (
            stock_id TEXT,
            date DATE,
            MA5 REAL,
            MA20 REAL,
            MA60 REAL,
            RSI REAL,
            MACD REAL,
            KD REAL,
            PRIMARY KEY (stock_id, date)
        )
        """
        )

        # 財務報表表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS financial_statements (
            stock_id TEXT,
            date DATE,
            revenue REAL,
            operating_profit REAL,
            net_income REAL,
            eps REAL,
            assets REAL,
            liabilities REAL,
            equity REAL,
            PRIMARY KEY (stock_id, date)
        )
        """
        )

        # 三大法人資料表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS institutional_investors (
            stock_id TEXT,
            date DATE,
            foreign_buy INTEGER,
            foreign_sell INTEGER,
            foreign_net INTEGER,
            investment_trust_buy INTEGER,
            investment_trust_sell INTEGER,
            investment_trust_net INTEGER,
            dealer_buy INTEGER,
            dealer_sell INTEGER,
            dealer_net INTEGER,
            total_institutional_net INTEGER,
            PRIMARY KEY (stock_id, date)
        )
        """
        )

        # 新聞資料表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS stock_news (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,
            content TEXT,
            url TEXT,
            publish_date DATE,
            source TEXT
        )
        """
        )

        # 新聞情緒分析表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS news_sentiment (
            news_id INTEGER,
            sentiment_score REAL,
            positive_score REAL,
            negative_score REAL,
            neutral_score REAL,
            keywords TEXT,
            summary TEXT,
            PRIMARY KEY (news_id),
            FOREIGN KEY (news_id) REFERENCES stock_news(id)
        )
        """
        )

        # 新聞-股票關係表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS news_stock_relation (
            news_id INTEGER,
            stock_id TEXT,
            relevance REAL,
            PRIMARY KEY (news_id, stock_id),
            FOREIGN KEY (news_id) REFERENCES stock_news(id),
            FOREIGN KEY (stock_id) REFERENCES stock_info(stock_id)
        )
        """
        )

        self.conn.commit()

    def download_price_data(self):
        """下載股價資料"""
        self.logger.info("開始下載價格相關資料...")

        try:
            # 實際使用FinLab API下載資料
            import finlab
            from finlab.data import Data

            # 登入API
            finlab.login(self.api_key)

            # 創建資料物件
            data = Data()

            # 下載股價資料
            price_types = {
                "price_close": "price:收盤價",
                "price_open": "price:開盤價",
                "price_high": "price:最高價",
                "price_low": "price:最低價",
                "price_volume": "price:成交股數",
            }

            for table_name, data_type in price_types.items():
                try:
                    self.logger.info(f"開始下載 {data_type} 資料...")
                    df = data.get(data_type)
                    if df is not None and not df.empty:
                        self._save_price_data_to_db(table_name, df)
                        self.logger.info(f"已下載並保存 {data_type}")
                    else:
                        self.logger.warning(f"下載 {data_type} 時獲取的資料為空")
                except Exception as e:
                    self.logger.error(f"下載 {table_name} 時發生錯誤: {str(e)}")

            return True

        except Exception as e:
            self.logger.error(f"下載股價資料時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def _save_price_data_to_db(self, table_name, df):
        """將價格資料保存到資料庫

        Args:
            table_name (str): 資料表名稱
            df (DataFrame): 股價資料
        """
        if df is None or df.empty:
            self.logger.warning(f"無法保存 {table_name}：資料為空")
            return

        try:
            # 處理寬格式資料，轉換為長格式
            if len(df.columns) > 10:  # 假設列數多於10，判定為寬格式資料
                self.logger.info(
                    f"處理寬格式資料: {table_name} 有 {len(df.columns)} 個欄位，轉換為長格式"
                )

                # 重設索引，確保日期列存在
                df_reset = df.reset_index()

                # 將寬格式轉為長格式
                long_format = []
                for col in df.columns:
                    for idx, row in df_reset.iterrows():
                        date = row["index"] if "index" in row else row["date"]
                        value = row[col]
                        if pd.notna(value):
                            long_format.append({"date": date, "stock_id": col, "value": value})

                # 轉換為 DataFrame
                long_df = pd.DataFrame(long_format)

                # 保存到資料庫
                long_df.to_sql(table_name, self.conn, if_exists="append", index=False)
                self.logger.info(f"成功以長格式保存資料表 {table_name}，共 {len(long_df)} 筆資料")
            else:
                # 一般資料直接保存
                df.to_sql(table_name, self.conn, if_exists="append", index=False)
                self.logger.info(f"成功保存資料表 {table_name}，共 {len(df)} 筆資料")

            self.conn.commit()
        except Exception as e:
            self.logger.error(f"保存資料表 {table_name} 時發生錯誤: {str(e)}")
            # 嘗試使用批次方式保存
            self.logger.info(f"嘗試使用備用方法保存 {table_name}")
            try:
                # 分批保存
                batch_size = 1000
                for i in range(0, len(df), batch_size):
                    batch_df = df.iloc[i : i + batch_size]
                    batch_df.to_sql(
                        table_name,
                        self.conn,
                        if_exists="append" if i > 0 else "replace",
                        index=False,
                    )

                self.conn.commit()
                self.logger.info(f"成功使用批次方式保存資料表 {table_name}")
            except Exception as batch_error:
                self.logger.error(f"批次保存資料表 {table_name} 時發生錯誤: {str(batch_error)}")

    def download_technical_indicators(self):
        """下載技術指標資料"""
        self.logger.info("開始下載技術指標資料...")

        try:
            # 使用FinLab API下載技術指標
            import finlab
            from finlab.data import Data

            # 登入API
            finlab.login(self.api_key)

            # 創建資料物件
            data = Data()

            # 下載技術指標
            indicator_types = {
                "technical_pe_ratio": "technical:本益比",
                "technical_dividend_yield": "technical:殖利率",
                "technical_pb_ratio": "technical:股價淨值比",
                "technical_adj_close": "price_adj:收盤價",
                "technical_adj_open": "price_adj:開盤價",
                "technical_adj_high": "price_adj:最高價",
                "technical_adj_low": "price_adj:最低價",
            }

            for table_name, data_type in indicator_types.items():
                try:
                    self.logger.info(f"開始下載 {data_type} 資料...")
                    df = data.get(data_type)
                    if df is not None and not df.empty:
                        self._save_price_data_to_db(table_name, df)
                        self.logger.info(f"已下載並保存 {data_type}")
                    else:
                        self.logger.warning(f"下載 {data_type} 時獲取的資料為空")
                except Exception as e:
                    self.logger.error(f"下載 {table_name} 時發生錯誤: {str(e)}")

            return True

        except Exception as e:
            self.logger.error(f"下載技術指標資料時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def download_financial_data(self):
        """下載財務資料"""
        self.logger.info("開始下載財務報表資料...")

        try:
            # 使用FinLab API下載財務資料
            import finlab
            from finlab.data import Data

            # 登入API
            finlab.login(self.api_key)

            # 創建資料物件
            data = Data()

            # 下載財務資料
            financial_types = {
                "financial_cash": "financial_statement:現金",
                "financial_assets": "financial_statement:資產",
                "financial_liability": "financial_statement:負債",
                "financial_equity": "financial_statement:權益",
                "financial_revenue": "financial_statement:營收",
                "financial_gross_profit": "financial_statement:毛利",
                "financial_operating_profit": "financial_statement:營業利益",
                "financial_net_income": "financial_statement:稅後淨利",
                "financial_eps": "financial_statement:EPS",
            }

            for table_name, data_type in financial_types.items():
                try:
                    self.logger.info(f"開始下載 {data_type} 資料...")
                    df = data.get(data_type)
                    if df is not None and not df.empty:
                        self._save_price_data_to_db(table_name, df)
                        self.logger.info(f"已下載並保存 {data_type}")
                    else:
                        self.logger.warning(f"下載 {data_type} 時獲取的資料為空")
                except Exception as e:
                    self.logger.error(f"下載 {table_name} 時發生錯誤: {str(e)}")

            return True

        except Exception as e:
            self.logger.error(f"下載財務資料時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def download_institutional_investors_data(self):
        """下載三大法人資料"""
        self.logger.info("開始下載三大法人資料...")

        try:
            # 使用FinLab API下載三大法人資料
            import finlab
            from finlab.data import Data

            # 登入API
            finlab.login(self.api_key)

            # 創建資料物件
            data = Data()

            # 下載三大法人資料
            investor_types = {
                "institutional_foreign_buy": "institutional:外資買進金額",
                "institutional_foreign_sell": "institutional:外資賣出金額",
                "institutional_foreign_net": "institutional:外資買賣超",
                "institutional_investment_trust_buy": "institutional:投信買進金額",
                "institutional_investment_trust_sell": "institutional:投信賣出金額",
                "institutional_investment_trust_net": "institutional:投信買賣超",
                "institutional_dealer_buy": "institutional:自營商買進金額",
                "institutional_dealer_sell": "institutional:自營商賣出金額",
                "institutional_dealer_net": "institutional:自營商買賣超",
            }

            for table_name, data_type in investor_types.items():
                try:
                    self.logger.info(f"開始下載 {data_type} 資料...")
                    df = data.get(data_type)
                    if df is not None and not df.empty:
                        self._save_price_data_to_db(table_name, df)
                        self.logger.info(f"已下載並保存 {data_type}")
                    else:
                        self.logger.warning(f"下載 {data_type} 時獲取的資料為空")
                except Exception as e:
                    self.logger.error(f"下載 {table_name} 時發生錯誤: {str(e)}")

            return True

        except Exception as e:
            self.logger.error(f"下載三大法人資料時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def download_margin_trading_data(self):
        """下載融資融券資料"""
        self.logger.info("開始下載融資融券資料...")

        try:
            # 使用FinLab API下載融資融券資料
            import finlab
            from finlab.data import Data

            # 登入API
            finlab.login(self.api_key)

            # 創建資料物件
            data = Data()

            # 下載融資融券資料
            margin_types = {
                "margin_trading_buy": "margin:融資買進",
                "margin_trading_sell": "margin:融資賣出",
                "margin_trading_balance": "margin:融資餘額",
                "short_selling_buy": "margin:融券買進",
                "short_selling_sell": "margin:融券賣出",
                "short_selling_balance": "margin:融券餘額",
            }

            for table_name, data_type in margin_types.items():
                try:
                    self.logger.info(f"開始下載 {data_type} 資料...")
                    df = data.get(data_type)
                    if df is not None and not df.empty:
                        self._save_price_data_to_db(table_name, df)
                        self.logger.info(f"已下載並保存 {data_type}")
                    else:
                        self.logger.warning(f"下載 {data_type} 時獲取的資料為空")
                except Exception as e:
                    self.logger.error(f"下載 {table_name} 時發生錯誤: {str(e)}")

            return True

        except Exception as e:
            self.logger.error(f"下載融資融券資料時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def download_company_info(self):
        """下載公司基本資訊"""
        self.logger.info("開始下載公司基本資料...")

        try:
            # 使用FinLab API下載公司資訊
            import finlab
            from finlab.data import Data

            # 登入API
            finlab.login(self.api_key)

            # 創建資料物件
            data = Data()

            # 下載公司資訊
            try:
                company_info = data.get("company_info")
                if company_info is not None and not company_info.empty:
                    # 保存到資料庫
                    try:
                        company_info.to_sql(
                            "company_info", self.conn, if_exists="replace", index=False
                        )
                        self.logger.info(f"已保存公司基本資料，共 {len(company_info)} 筆")
                    except Exception as e:
                        self.logger.error(f"保存資料表 company_info 時發生錯誤: {str(e)}")
                        # 嘗試使用備用方法保存
                        self.logger.info(f"嘗試使用備用方法保存 company_info")
                        try:
                            # 分批保存
                            batch_size = 100
                            for i in range(0, len(company_info), batch_size):
                                batch_df = company_info.iloc[i : i + batch_size]
                                batch_df.to_sql(
                                    "company_info",
                                    self.conn,
                                    if_exists="append" if i > 0 else "replace",
                                    index=False,
                                )

                            self.conn.commit()
                            self.logger.info(f"成功使用批次方式保存資料表 company_info")
                        except Exception as batch_error:
                            self.logger.error(
                                f"批次保存資料表 company_info 時發生錯誤: {str(batch_error)}"
                            )
                else:
                    self.logger.warning(f"下載公司資訊時獲取的資料為空")
            except Exception as e:
                self.logger.error(f"下載公司資訊時發生錯誤: {str(e)}")

            self.logger.info("已下載並保存公司基本資料")
            return True

        except Exception as e:
            self.logger.error(f"下載公司基本資料時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def download_stock_categories(self):
        """下載股票類別資訊"""
        self.logger.info("開始下載股票類別資料...")

        try:
            # 使用FinLab API下載股票類別
            import finlab
            from finlab.data import Data

            # 登入API
            finlab.login(self.api_key)

            # 創建資料物件
            data = Data()

            # 下載股票類別
            try:
                stock_categories = data.get("security_categories")
                if stock_categories is not None and not stock_categories.empty:
                    # 顯示數據資訊
                    self.logger.info(f"security_categories 資料形狀: {stock_categories.shape}")

                    # 保存到資料庫
                    try:
                        stock_categories.to_sql(
                            "stock_categories", self.conn, if_exists="replace", index=False
                        )
                        self.logger.info(f"已保存股票類別資料，共 {len(stock_categories)} 筆")
                    except Exception as e:
                        self.logger.error(f"保存資料表 stock_categories 時發生錯誤: {str(e)}")
                        # 嘗試使用備用方法保存
                        self.logger.info(f"嘗試使用備用方法保存 stock_categories")
                        try:
                            # 分批保存
                            batch_size = 100
                            for i in range(0, len(stock_categories), batch_size):
                                batch_df = stock_categories.iloc[i : i + batch_size]
                                batch_df.to_sql(
                                    "stock_categories",
                                    self.conn,
                                    if_exists="append" if i > 0 else "replace",
                                    index=False,
                                )

                            self.conn.commit()
                            self.logger.info(f"成功使用批次方式保存資料表 stock_categories")
                        except Exception as batch_error:
                            self.logger.error(
                                f"批次保存資料表 stock_categories 時發生錯誤: {str(batch_error)}"
                            )
                else:
                    self.logger.warning(f"下載股票類別時獲取的資料為空")
            except Exception as e:
                self.logger.error(f"下載股票類別時發生錯誤: {str(e)}")

            self.logger.info("已下載並保存股票類別資料")
            return True

        except Exception as e:
            self.logger.error(f"下載股票類別資料時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def test_api_key(self):
        """測試 API 金鑰是否有效

        Returns:
            bool: 金鑰是否有效
        """
        try:
            import finlab

            finlab.login(self.api_key)

            # 嘗試獲取一個簡單的資料，測試金鑰是否有效
            from finlab.data import Data

            data = Data()
            df = data.get("tw_index:收盤價")  # 嘗試獲取台灣加權指數

            return df is not None and not df.empty
        except Exception as e:
            self.logger.error(f"API 金鑰測試失敗: {str(e)}")
            return False

    def close(self):
        """關閉資料庫連接"""
        if self.conn:
            self.conn.close()
            self.logger.info("已關閉資料庫連接")
