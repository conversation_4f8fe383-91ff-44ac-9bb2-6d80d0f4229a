# core/stock_data_downloader.py
import logging
import os

from core.data_downloader import CoreDataDownloader


class StockDataDownloader:
    """桌面版股票資料下載器"""

    def __init__(self, api_key=None, database_path=None):
        """初始化下載器

        Args:
            api_key (str, optional): FinLab API金鑰
            database_path (str): 資料庫路徑
        """
        current_dir = os.path.dirname(os.path.abspath(__file__))
        base_dir = os.path.dirname(os.path.dirname(current_dir))
        self.database_path = os.path.join(base_dir, "database", "tw_stock_data.db")
        self.logger = logging.getLogger("StockDataDownloader")
        self.core_downloader = CoreDataDownloader(api_key, database_path)

    def download_price_data(self):
        """下載股價資料"""
        return self.core_downloader.download_price_data()

    def download_technical_indicators(self):
        """下載技術指標"""
        return self.core_downloader.download_technical_indicators()

    def download_financial_data(self):
        """下載財務資料"""
        return self.core_downloader.download_financial_data()

    def download_institutional_investors_data(self):
        """下載三大法人資料"""
        return self.core_downloader.download_institutional_investors_data()

    def download_margin_trading_data(self):
        """下載融資融券資料"""
        return self.core_downloader.download_margin_trading_data()

    def download_company_info(self):
        """下載公司基本資訊"""
        return self.core_downloader.download_company_info()

    def download_stock_categories(self):
        """下載股票類別"""
        return self.core_downloader.download_stock_categories()

    def close(self):
        """關閉資料庫連接"""
        self.core_downloader.close()
