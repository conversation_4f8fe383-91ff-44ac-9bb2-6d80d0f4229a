import pandas as pd
import numpy as np
import sqlite3
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from datetime import datetime, timedelta
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.pipeline import Pipeline
import joblib
import talib
import warnings
import os

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("stock_prediction.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("股票漲跌預測")
warnings.filterwarnings('ignore')

class StockPricePredictor:
    def __init__(self, database_path=None, model_dir="models"):
        """初始化股票價格預測器

        Args:
            database_path (str, optional): SQLite資料庫路徑. Defaults to "tw_stock_data.db".
            model_dir (str, optional): 模型保存目錄. Defaults to "models".
        """
        """初始化資料庫管理器"""
        if database_path is None:
            # 指定固定的絕對路徑，放在data子目錄下
            current_dir = os.path.dirname(os.path.abspath(__file__))
            base_dir = os.path.dirname(os.path.dirname(current_dir))
            self.database_path = os.path.join(base_dir, "database", "tw_stock_data.db")
            
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
        else:
            self.database_path = database_path
                    
        self.database_path = database_path
        self.model_dir = model_dir
        self.conn = None
        
        # 建立模型目錄
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)
            logger.info(f"已建立模型目錄: {model_dir}")
        
        try:
            self.conn = sqlite3.connect(self.database_path)
            logger.info(f"已連接到資料庫: {self.database_path}")
        except Exception as e:
            logger.error(f"連接資料庫時發生錯誤: {e}")
    
    def load_data(self, stock_id, start_date=None, end_date=None):
        """從資料庫載入特定股票的資料，優化資料載入"""
        try:
            # 嘗試取得股票清單以檢查資料庫連接狀態
            cursor = self.conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            logger.info(f"資料庫中的表格數量: {len(tables)}")
            
            # 擴大日期範圍，確保有足夠資料進行技術指標計算
            if start_date:
                # 往前多查詢90天的資料用於計算技術指標
                extended_start = (pd.to_datetime(start_date) - pd.Timedelta(days=90)).strftime('%Y-%m-%d')
            else:
                # 預設查詢三年資料
                end_date_obj = pd.to_datetime(end_date) if end_date else pd.Timestamp.now()
                extended_start = (end_date_obj - pd.Timedelta(days=365*3)).strftime('%Y-%m-%d')
                
            logger.info(f"查詢資料時間範圍: {extended_start} 至 {end_date or '今天'}")
            
            # 建立查詢
            # 修改 base_query 以從 'value' 欄位讀取數據並使用 AS 重命名
            base_query = """
                SELECT date, value AS {}
                FROM {} 
                WHERE stock_id = '{}'
            """
            
            # 添加日期條件
            if extended_start:
                base_query += f" AND date >= '{extended_start}'"
            if end_date:
                base_query += f" AND date <= '{end_date}'"
            
            base_query += " ORDER BY date"
            
            # 查詢各種價格數據
            data_items = {
                "close": "price_close",
                "open": "price_open",
                "high": "price_high",
                "low": "price_low",
                "volume": "price_volume"
            }
            
            merged_df = None # 初始化 merged_df

            for col_name, table_name in data_items.items():
                # 使用 col_name (如 'close') 作為 AS 後的欄位名，同時也用於 base_query.format 的第一個參數
                query = base_query.format(col_name, table_name, stock_id)
                logger.info(f"執行查詢: {query}")
                
                df_temp = pd.read_sql(query, self.conn) # 從資料庫讀取，欄位名會是 date, {col_name}
                
                if df_temp.empty:
                    logger.warning(f"查詢 {table_name} 未返回任何數據 for stock_id {stock_id}")
                    continue
                    
                logger.info(f"從 {table_name} 查詢到 {len(df_temp)} 筆資料 for stock_id {stock_id}")
                
                # 確保日期格式正確並設為索引
                df_temp['date'] = pd.to_datetime(df_temp['date'])
                df_temp.set_index('date', inplace=True)

                if merged_df is None:
                    merged_df = df_temp
                else:
                    # 合併，確保基於索引 'date'
                    merged_df = merged_df.join(df_temp, how='outer', lsuffix='_left') # 添加 lsuffix 以避免潛在的重複欄位名問題
                    # 如果 join 產生了重複的欄位（例如 col_name_left），需要處理，但理論上不應該，因為我們每次查詢不同的 col_name
            
            if merged_df is None or merged_df.empty:
                logger.error(f"未找到 {stock_id} 的任何數據")
                return None
            
            # 移除在 join過程中可能產生的多餘 'stock_id_left' (如果之前 df_temp 有 stock_id)
            # merged_df = merged_df.loc[:,~merged_df.columns.duplicated()] # 移除重複列
            # 由於我們現在只查詢 date 和 value AS col_name，理論上不會有重複的 col_name
            
            merged_df.sort_index(inplace=True)
            
            # 輸出詳細的資料統計信息
            logger.info(f"成功載入 {stock_id} 的資料，資料筆數: {len(merged_df)}")
            logger.info(f"資料日期範圍: {merged_df.index.min()} - {merged_df.index.max()}")
            logger.info(f"資料欄位: {list(merged_df.columns)}")
            
            # 如果是擴展的日期範圍，最後再裁剪回使用者請求的範圍
            if start_date and pd.to_datetime(extended_start) < pd.to_datetime(start_date):
                merged_df = merged_df[merged_df.index >= pd.to_datetime(start_date)]
                logger.info(f"裁剪回原始請求日期範圍後，資料筆數: {len(merged_df)}")
            
            return merged_df
                
        except Exception as e:
            logger.error(f"載入 {stock_id} 資料時發生錯誤: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def load_institutional_data(self, stock_id, start_date=None, end_date=None):
        """載入三大法人資料
        
        Args:
            stock_id (str): 股票代碼
            start_date (str, optional): 起始日期 (YYYY-MM-DD)
            end_date (str, optional): 結束日期 (YYYY-MM-DD)
            
        Returns:
            DataFrame: 三大法人資料
        """
        try:
            # 三大法人資料
            foreign_net = pd.read_sql(f"SELECT * FROM institutional_foreign_net WHERE stock_id='{stock_id}'", self.conn)
            investment_trust_net = pd.read_sql(f"SELECT * FROM institutional_investment_trust_net WHERE stock_id='{stock_id}'", self.conn)
            dealer_net = pd.read_sql(f"SELECT * FROM institutional_dealer_net WHERE stock_id='{stock_id}'", self.conn)
            
            # 確保所有DataFrame都有date和stock_id列
            dfs = [foreign_net, investment_trust_net, dealer_net]
            for df in dfs:
                df.set_index(['date', 'stock_id'], inplace=True)
            
            # 合併資料
            merged_df = pd.concat([
                foreign_net.rename(columns={stock_id: 'foreign_net'}),
                investment_trust_net.rename(columns={stock_id: 'investment_trust_net'}),
                dealer_net.rename(columns={stock_id: 'dealer_net'})
            ], axis=1)
            
            merged_df = merged_df.reset_index()
            
            # 應用日期過濾
            if start_date:
                merged_df = merged_df[merged_df['date'] >= start_date]
            if end_date:
                merged_df = merged_df[merged_df['date'] <= end_date]
            
            # 按日期排序
            merged_df = merged_df.sort_values('date').reset_index(drop=True)
            
            return merged_df
            
        except Exception as e:
            logger.error(f"載入 {stock_id} 三大法人資料時發生錯誤: {e}")
            return None
    
    def load_margin_data(self, stock_id, start_date=None, end_date=None):
        """載入融資融券資料
        
        Args:
            stock_id (str): 股票代碼
            start_date (str, optional): 起始日期 (YYYY-MM-DD)
            end_date (str, optional): 結束日期 (YYYY-MM-DD)
            
        Returns:
            DataFrame: 融資融券資料
        """
        try:
            # 融資融券資料
            margin_balance = pd.read_sql(f"SELECT * FROM margin_trading_balance WHERE stock_id='{stock_id}'", self.conn)
            short_balance = pd.read_sql(f"SELECT * FROM short_selling_balance WHERE stock_id='{stock_id}'", self.conn)
            
            # 確保所有DataFrame都有date和stock_id列
            dfs = [margin_balance, short_balance]
            for df in dfs:
                df.set_index(['date', 'stock_id'], inplace=True)
            
            # 合併資料
            merged_df = pd.concat([
                margin_balance.rename(columns={stock_id: 'margin_balance'}),
                short_balance.rename(columns={stock_id: 'short_balance'})
            ], axis=1)
            
            merged_df = merged_df.reset_index()
            
            # 應用日期過濾
            if start_date:
                merged_df = merged_df[merged_df['date'] >= start_date]
            if end_date:
                merged_df = merged_df[merged_df['date'] <= end_date]
            
            # 按日期排序
            merged_df = merged_df.sort_values('date').reset_index(drop=True)
            
            return merged_df
            
        except Exception as e:
            logger.error(f"載入 {stock_id} 融資融券資料時發生錯誤: {e}")
            return None
    
    def add_technical_indicators(self, df):
        """添加技術指標，適應少量數據的情況
        
        Args:
            df (DataFrame): 股票價格資料DataFrame
                
        Returns:
            DataFrame: 添加了技術指標的DataFrame
        """
        try:
            # 檢查資料筆數
            data_len = len(df)
            logger.info(f"原始資料筆數: {data_len}")
            if data_len < 10:
                logger.warning(f"資料筆數過少 ({data_len}筆)，可能無法計算部分指標")
            
            # 確保數據為數值型
            ohlcv_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in ohlcv_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 根據資料量動態調整計算的指標
            df_with_indicators = df.copy()
            
            # 未來1天的價格變動（必須計算，作為目標變數）
            df_with_indicators['next_1d_change'] = df['close'].shift(-1) / df['close'] - 1
            df_with_indicators['next_1d_up'] = (df_with_indicators['next_1d_change'] > 0).astype(int)
            
            # 價格變動
            df_with_indicators['price_change'] = df['close'].pct_change() * 100
            
            # 成交量變動
            if 'volume' in df.columns:
                df_with_indicators['volume_change'] = df['volume'].pct_change() * 100
            
            # 移動平均線（根據資料量動態選擇）
            if data_len >= 5:
                df_with_indicators['ma5'] = df['close'].rolling(window=5).mean()
                if data_len >= 10:
                    df_with_indicators['ma10'] = df['close'].rolling(window=10).mean()
                    if data_len >= 20:
                        df_with_indicators['ma20'] = df['close'].rolling(window=20).mean()
                        if data_len >= 60:
                            df_with_indicators['ma60'] = df['close'].rolling(window=60).mean()
            
            # 相對強弱指標(RSI)
            if data_len >= 7:  # 降低RSI的需求天數
                df_with_indicators['rsi_6'] = talib.RSI(df['close'].values, timeperiod=6)
                if data_len >= 14:
                    df_with_indicators['rsi_12'] = talib.RSI(df['close'].values, timeperiod=12)
                    if data_len >= 24:
                        df_with_indicators['rsi_24'] = talib.RSI(df['close'].values, timeperiod=24)
            
            # MACD
            if data_len >= 30:  # 降低MACD所需的天數條件
                macd, macd_signal, macd_hist = talib.MACD(
                    df['close'].values, 
                    fastperiod=12, 
                    slowperiod=26, 
                    signalperiod=9
                )
                df_with_indicators['macd'] = macd
                df_with_indicators['macd_signal'] = macd_signal
                df_with_indicators['macd_hist'] = macd_hist
            
            # 布林帶
            if data_len >= 22:  # 確保有足夠的資料計算布林帶
                upper, middle, lower = talib.BBANDS(
                    df['close'].values,
                    timeperiod=20,
                    nbdevup=2,
                    nbdevdn=2,
                    matype=0
                )
                df_with_indicators['bb_upper'] = upper
                df_with_indicators['bb_middle'] = middle
                df_with_indicators['bb_lower'] = lower
                    
                # 布林帶寬度與%B指標
                df_with_indicators['bb_width'] = (df_with_indicators['bb_upper'] - df_with_indicators['bb_lower']) / df_with_indicators['bb_middle']
                df_with_indicators['bb_b'] = (df_with_indicators['close'] - df_with_indicators['bb_lower']) / (df_with_indicators['bb_upper'] - df_with_indicators['bb_lower'])
            
            # 計算價格與已有移動平均線的差距百分比
            if 'ma5' in df_with_indicators.columns:
                df_with_indicators['ma5_gap'] = (df['close'] - df_with_indicators['ma5']) / df_with_indicators['ma5'] * 100
            if 'ma10' in df_with_indicators.columns:
                df_with_indicators['ma10_gap'] = (df['close'] - df_with_indicators['ma10']) / df_with_indicators['ma10'] * 100
            if 'ma20' in df_with_indicators.columns:
                df_with_indicators['ma20_gap'] = (df['close'] - df_with_indicators['ma20']) / df_with_indicators['ma20'] * 100
            if 'ma60' in df_with_indicators.columns:
                df_with_indicators['ma60_gap'] = (df['close'] - df_with_indicators['ma60']) / df_with_indicators['ma60'] * 100
            
            # 前後對比
            remaining_count = df_with_indicators.dropna(subset=['next_1d_up']).shape[0]
            logger.info(f"添加了技術指標後，剩餘有效資料筆數: {remaining_count}")
            
            # 只清除目標變量的NaN值
            df_final = df_with_indicators.dropna(subset=['next_1d_up'])
            logger.info(f"清除目標變數NaN值後，最終資料筆數: {len(df_final)}")
            
            # 檢查剩餘特徵的NaN比例
            na_stats = df_final.isna().sum() / len(df_final) * 100
            high_na_cols = na_stats[na_stats > 30].index.tolist()
            if high_na_cols:
                logger.warning(f"以下特徵缺失值較多(>30%): {high_na_cols}")
            
            return df_final
                
        except Exception as e:
            logger.error(f"添加技術指標時發生錯誤: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 發生錯誤時，至少確保返回具有目標變數的DataFrame
            if 'next_1d_change' not in df.columns:
                df['next_1d_change'] = df['close'].shift(-1) / df['close'] - 1
                df['next_1d_up'] = (df['next_1d_change'] > 0).astype(int)
            
            return df.dropna(subset=['next_1d_up'])
    
    def prepare_features(self, stock_id, start_date=None, end_date=None, prediction_days=1):
        """準備模型訓練用的特徵資料，整合主力買賣資訊"""
        try:
            # 取得價格資料
            price_df = self.load_data(stock_id, start_date, end_date)
            if price_df is None or len(price_df) == 0:
                logger.error(f"無法取得 {stock_id} 的價格資料")
                return None, None
            
            # 添加技術指標
            price_df = self.add_technical_indicators(price_df)
            if price_df is None or len(price_df) == 0:
                logger.error(f"添加技術指標後，{stock_id} 的資料筆數為0")
                return None, None
            
            # 添加股票代碼欄位
            price_df['stock_id'] = stock_id
            price_df = price_df.reset_index()  # 將日期從索引轉為列
            
            # 載入主力交易明細
            inst_df = self.load_institutional_trading_details(stock_id, start_date, end_date)
            if inst_df is not None and len(inst_df) > 0:
                logger.info(f"成功載入 {stock_id} 的主力資訊，開始整合為特徵...")
                
                # 合併資料
                price_df = pd.merge(
                    price_df, 
                    inst_df, 
                    on=['date', 'stock_id'], 
                    how='left'
                )
                
                # 定義主力相關欄位
                institutional_cols = [
                    'foreign_investors_diff', 'investment_trust_diff', 
                    'dealers_proprietary_diff', 'dealers_hedge_diff',
                    'total_institutional_diff', 'non_institutional_diff'
                ]
                
                # 填補空值
                for col in institutional_cols:
                    if col in price_df.columns:
                        price_df[col] = price_df[col].fillna(0)
                        
                        # 計算相對成交量比率
                        price_df[f'{col}_ratio'] = price_df[col] / price_df['volume']
                        
                        # 計算5日累積值
                        price_df[f'{col}_5d'] = price_df[col].rolling(5).sum()
                        
                        # 計算10日累積值
                        price_df[f'{col}_10d'] = price_df[col].rolling(10).sum()
                        
                        # 計算變化幅度
                        price_df[f'{col}_change'] = price_df[col].diff()
                
                logger.info(f"已成功添加主力資訊特徵，當前特徵數: {len(price_df.columns)}")
            
            # 重新設置日期為索引，以便於後續處理
            price_df.set_index('date', inplace=True)
            
            # 根據prediction_days調整目標變數
            target_col = f'next_{prediction_days}d_up'
            if f'next_{prediction_days}d_change' not in price_df.columns:
                price_df[f'next_{prediction_days}d_change'] = price_df['close'].shift(-prediction_days) / price_df['close'] - 1
                price_df[target_col] = (price_df[f'next_{prediction_days}d_change'] > 0).astype(int)
            
            # 移除不需要的列
            drop_cols = []
            for day in [1, 3, 5]:
                if day != prediction_days and f'next_{day}d_up' in price_df.columns:
                    drop_cols.append(f'next_{day}d_up')
                if day != prediction_days and f'next_{day}d_change' in price_df.columns:
                    drop_cols.append(f'next_{day}d_change')
            
            # 確保所有要移除的列真的存在
            cols_to_drop = [col for col in drop_cols if col in price_df.columns]
            
            # 移除非數值欄位
            non_numeric_cols = ['stock_id', 'security_name']
            cols_to_drop.extend([col for col in non_numeric_cols if col in price_df.columns])
            
            # 提取特徵與標籤
            X = price_df.drop(columns=cols_to_drop + [target_col])
            y = price_df[target_col]
            
            # 確保只保留數值型特徵
            X = X.select_dtypes(include=[np.number])
            
            # 當資料量少時，進行特徵篩選
            if len(X) < 100 and X.shape[1] > 20:
                logger.info(f"資料量較少 ({len(X)} 筆)，但特徵較多 ({X.shape[1]} 個)，進行特徵篩選")
                
                # 移除缺失值比例高的特徵
                missing_ratio = X.isna().mean()
                high_missing_cols = missing_ratio[missing_ratio > 0.3].index.tolist()
                if high_missing_cols:
                    logger.info(f"移除缺失值比例高的特徵: {high_missing_cols}")
                    X = X.drop(columns=high_missing_cols)
                
                # 移除高度相關的特徵
                if X.shape[1] > 10:
                    corr_matrix = X.corr().abs()
                    upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
                    high_corr_cols = [col for col in upper.columns if any(upper[col] > 0.9)]
                    if high_corr_cols:
                        logger.info(f"移除高度相關的特徵: {high_corr_cols}")
                        X = X.drop(columns=high_corr_cols)
            
            # 填補剩餘缺失值
            X = X.fillna(X.mean())
            
            logger.info(f"已準備 {stock_id} 的特徵資料，特徵數: {X.shape[1]}，樣本數: {X.shape[0]}")
            return X, y
                
        except Exception as e:
            logger.error(f"準備 {stock_id} 特徵資料時發生錯誤: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None, None
    
    def train_model(self, stock_id, start_date=None, end_date=None, prediction_days=1, test_size=0.2):
        """訓練股票漲跌預測模型，優化小數據集處理
        
        Args:
            stock_id (str): 股票代碼
            start_date (str, optional): 訓練資料起始日期. Defaults to None.
            end_date (str, optional): 訓練資料結束日期. Defaults to None.
            prediction_days (int, optional): 預測未來幾天的漲跌. Defaults to 1.
            test_size (float, optional): 測試集比例. Defaults to 0.2.
                
        Returns:
            dict: 訓練結果，包含模型、準確率等
        """
        try:
            # 準備特徵資料
            X, y = self.prepare_features(stock_id, start_date, end_date, prediction_days)
            if X is None or y is None or len(X) == 0:
                logger.error("無法取得特徵資料，訓練失敗")
                return None
            
            # 檢查資料數量
            data_size = len(X)
            logger.info(f"訓練資料筆數: {data_size}")
            
            if data_size < 30:
                logger.warning(f"資料量過少 ({data_size}筆)，模型準確性可能受到影響")
                # 調整測試集大小
                test_size = min(0.2, 5 / data_size)
                logger.info(f"調整測試集比例為 {test_size:.4f}")
            
            # 分割訓練/測試集 - 時間序列分割，不進行隨機打亂
            split_idx = int(len(X) * (1 - test_size))
            X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
            y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
            
            logger.info(f"訓練集筆數: {len(X_train)}, 測試集筆數: {len(X_test)}")
            
            # 檢查特徵數量 - 如果特徵數量多於資料筆數，使用更簡單的模型
            n_features = X.shape[1]
            logger.info(f"特徵數量: {n_features}")
            
            # 處理缺失值
            X_train = X_train.fillna(X_train.mean())
            X_test = X_test.fillna(X_train.mean())  # 使用訓練集的平均值填充測試集
            
            # 建立分類器
            if data_size < 100:
                # 資料量較少時，使用簡單模型，避免過擬合
                logger.info("使用簡化模型訓練...")
                pipeline = Pipeline([
                    ('scaler', RobustScaler()),
                    ('classifier', RandomForestClassifier(
                        n_estimators=50,
                        max_depth=5,
                        min_samples_split=3,
                        random_state=42
                    ))
                ])
                
                # 直接訓練模型，不使用網格搜索
                pipeline.fit(X_train, y_train)
                best_model = pipeline
                best_params = {
                    'classifier__n_estimators': 50,
                    'classifier__max_depth': 5,
                    'classifier__min_samples_split': 3
                }
            else:
                # 資料量足夠時，使用網格搜索尋找最佳參數
                logger.info(f"使用網格搜索優化模型參數...")
                
                pipeline = Pipeline([
                    ('scaler', RobustScaler()),
                    ('classifier', RandomForestClassifier(random_state=42))
                ])
                
                # 根據資料量調整參數搜索範圍
                param_grid = {
                    'classifier__n_estimators': [50, 100, 200],
                    'classifier__max_depth': [None, 10, 20],
                    'classifier__min_samples_split': [2, 5]
                }
                
                cv_folds = min(5, max(3, data_size // 20))
                logger.info(f"使用 {cv_folds} 折交叉驗證")
                
                grid_search = GridSearchCV(
                    pipeline, param_grid, cv=cv_folds, scoring='accuracy', n_jobs=-1
                )
                
                try:
                    grid_search.fit(X_train, y_train)
                    best_model = grid_search.best_estimator_
                    best_params = grid_search.best_params_
                    logger.info(f"最佳參數: {best_params}")
                except Exception as e:
                    logger.error(f"網格搜索過程中發生錯誤: {e}")
                    # 回退到直接訓練
                    pipeline = Pipeline([
                        ('scaler', RobustScaler()),
                        ('classifier', RandomForestClassifier(
                            n_estimators=100,
                            random_state=42
                        ))
                    ])
                    pipeline.fit(X_train, y_train)
                    best_model = pipeline
                    best_params = {'使用預設參數': True}
            
            # 評估模型
            y_pred = best_model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            logger.info(f"{stock_id} 模型訓練完成，測試集準確率: {accuracy:.4f}")
            
            # 保存模型
            model_path = os.path.join(self.model_dir, f"{stock_id}_pred{prediction_days}d_model.pkl")
            joblib.dump(best_model, model_path)
            logger.info(f"模型已保存至: {model_path}")
            
            # 計算特徵重要性
            if hasattr(best_model[-1], 'feature_importances_'):
                feature_importances = pd.DataFrame({
                    'feature': X.columns,
                    'importance': best_model[-1].feature_importances_
                }).sort_values('importance', ascending=False)
                
                logger.info("特徵重要性 (前10):")
                for i, row in feature_importances.head(10).iterrows():
                    logger.info(f"{row['feature']}: {row['importance']:.4f}")
            else:
                feature_importances = None
            
            # 返回結果
            result = {
                'model': best_model,
                'model_path': model_path,
                'accuracy': accuracy,
                'best_params': best_params,
                'classification_report': classification_report(y_test, y_pred),
                'confusion_matrix': confusion_matrix(y_test, y_pred),
                'feature_importances': feature_importances,
                'data_size': data_size
            }
            
            return result
                
        except Exception as e:
            logger.error(f"訓練 {stock_id} 模型時發生錯誤: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    def predict_price_movement(self, stock_id, date=None, prediction_days=1, model_path=None):
        """預測特定日期的股票漲跌（修改版）"""
        try:
            # 確定模型路徑
            if model_path is None:
                model_path = os.path.join(self.model_dir, f"{stock_id}_pred{prediction_days}d_model.pkl")
            
            if not os.path.exists(model_path):
                logger.error(f"模型文件不存在: {model_path}")
                return None
            
            # 載入模型
            model = joblib.load(model_path)
            logger.info(f"已載入模型: {model_path}")
            
            # 擴大時間範圍以確保有足夠資料計算技術指標
            if date is None:
                end_date = datetime.now().strftime('%Y-%m-%d')
                # 拉長到至少1年，確保所有技術指標都能計算
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
            else:
                end_date = date
                start_date = (datetime.strptime(date, '%Y-%m-%d') - timedelta(days=365)).strftime('%Y-%m-%d')
            
            # 載入價格資料，並設定更長的歷史時間段
            price_df = self.load_data(stock_id, start_date, end_date)
            if price_df is None or len(price_df) == 0:
                logger.error(f"無法取得 {stock_id} 的價格資料")
                return None
            
            # 添加技術指標
            price_df = self.add_technical_indicators(price_df)
            if price_df is None or len(price_df) == 0:
                logger.error(f"添加技術指標後資料筆數為 0，無法進行預測")
                return None
            
            # 只保留最後一筆資料用於預測
            latest_data = price_df.iloc[-1:].copy().reset_index()
            
            # 將date欄位轉換為字符串
            latest_data['date'] = latest_data['date'].dt.strftime('%Y-%m-%d')
            latest_data['stock_id'] = stock_id
            
            # 只選擇數值型特徵
            features = latest_data.select_dtypes(include=[np.number])
            
            # 移除不需要的列
            cols_to_drop = ['stock_id']
            for day in [1, 3, 5, 10]:
                col1 = f'next_{day}d_change'
                col2 = f'next_{day}d_up'
                if col1 in features.columns:
                    cols_to_drop.append(col1)
                if col2 in features.columns:
                    cols_to_drop.append(col2)
            
            features = features.drop(columns=[col for col in cols_to_drop if col in features.columns])
            
            # 取得預測日期
            prediction_date = latest_data['date'].values[0]
            
            # 檢查模型所需特徵
            if hasattr(model, 'feature_names_in_'):
                required_features = model.feature_names_in_
                
                # 檢查缺少的特徵
                missing_features = set(required_features) - set(features.columns)
                if missing_features:
                    logger.warning(f"缺少特徵: {missing_features}")
                    for feature in missing_features:
                        # 根據特徵名稱智能填充
                        if 'close' in features.columns:
                            base_value = features['close'].iloc[0]
                        else:
                            base_value = 0
                            
                        if feature.startswith('ma'):
                            features[feature] = base_value
                        elif feature.startswith('rsi'):
                            features[feature] = 50
                        elif feature.startswith('macd'):
                            features[feature] = 0
                        elif feature.endswith('_gap'):
                            features[feature] = 0
                        else:
                            features[feature] = 0
                
                # 檢查多餘的特徵
                extra_features = set(features.columns) - set(required_features)
                if extra_features:
                    features = features.drop(columns=list(extra_features))
                
                # 確保特徵順序與模型一致
                features = features[required_features]
            
            # 進行預測
            prediction_prob = model.predict_proba(features)[0]
            prediction = model.predict(features)[0]
            
            # 整理預測結果
            target_date = (datetime.strptime(prediction_date, '%Y-%m-%d') + timedelta(days=prediction_days)).strftime('%Y-%m-%d')
            
            result = {
                'stock_id': stock_id,
                'prediction_date': prediction_date,
                'target_date': target_date,
                'prediction_days': prediction_days,
                'prediction': 'up' if prediction == 1 else 'down',
                'probability': prediction_prob[1] if prediction == 1 else prediction_prob[0],
                'current_price': latest_data['close'].values[0] if 'close' in latest_data.columns else 0
            }
            
            logger.info(f"{stock_id} {prediction_days}天後 ({target_date}) 預測結果: {result['prediction']}, 機率: {result['probability']:.4f}")
            return result
            
        except Exception as e:
            logger.error(f"預測 {stock_id} 漲跌時發生錯誤: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def batch_predict(self, stock_ids, prediction_days=1):
        """批次預測多檔股票的漲跌
        
        Args:
            stock_ids (list): 股票代碼列表
            prediction_days (int, optional): 預測未來幾天. Defaults to 1.
            
        Returns:
            DataFrame: 預測結果
        """
        results = []
        
        for stock_id in stock_ids:
            result = self.predict_price_movement(stock_id, prediction_days=prediction_days)
            if result:
                results.append(result)
        
        if results:
            results_df = pd.DataFrame(results)
            results_df = results_df.sort_values('probability', ascending=False)
            
            return results_df
        else:
            return None
    
    def plot_predictions(self, stock_id, days=30, prediction_days=1):
        """繪製股價走勢與預測結果
        
        Args:
            stock_id (str): 股票代碼
            days (int, optional): 顯示過去幾天. Defaults to 30.
            prediction_days (int, optional): 預測未來幾天. Defaults to 1.
            
        Returns:
            matplotlib.figure: 圖表
        """
        try:
            # 取得最近的資料
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            # 載入價格資料
            price_df = self.load_data(stock_id, start_date, end_date)
            if price_df is None or len(price_df) == 0:
                logger.error(f"無法取得 {stock_id} 的價格資料")
                return None
            
            # 預測最近5個交易日
            predictions = []
            for i in range(min(5, len(price_df))):
                date = price_df.iloc[-(i+1)]['date']
                result = self.predict_price_movement(stock_id, date=date, prediction_days=prediction_days)
                if result:
                    predictions.append(result)
            
            # 繪製股價走勢
            plt.figure(figsize=(12, 8))
            
            # 股價走勢子圖
            ax1 = plt.subplot(2, 1, 1)
            ax1.plot(price_df['date'], price_df['close'], 'b-', label='收盤價')
            ax1.plot(price_df['date'], price_df['ma5'], 'r--', label='5日均線')
            ax1.plot(price_df['date'], price_df['ma20'], 'g--', label='20日均線')
            ax1.set_title(f'{stock_id} 股價走勢與預測', fontsize=15)
            ax1.set_ylabel('價格')
            ax1.grid(True)
            ax1.legend()
            
            # 為預測結果添加標記
            for pred in predictions:
                idx = price_df[price_df['date'] == pred['prediction_date']].index[0]
                price = price_df.iloc[idx]['close']
                color = 'red' if pred['prediction'] == 'up' else 'green'
                ax1.scatter(pred['prediction_date'], price, color=color, s=100, zorder=5)
                ax1.annotate(
                    f"{pred['prediction']} ({pred['probability']:.2f})",
                    (pred['prediction_date'], price),
                    xytext=(0, 10),
                    textcoords='offset points',
                    ha='center',
                    color=color,
                    fontweight='bold'
                )
            
            # 成交量子圖
            ax2 = plt.subplot(2, 1, 2, sharex=ax1)
            ax2.bar(price_df['date'], price_df['volume'], color='blue', alpha=0.6)
            ax2.set_ylabel('成交量')
            ax2.grid(True)
            
            plt.tight_layout()
            plt.savefig(f"{stock_id}_prediction.png")
            logger.info(f"股價走勢與預測圖已保存為: {stock_id}_prediction.png")
            
            return plt.gcf()
            
        except Exception as e:
            logger.error(f"繪製 {stock_id} 預測圖時發生錯誤: {e}")
            return None
    
    def close(self):
        """關閉資料庫連接"""
        if self.conn:
            self.conn.close()
            logger.info("已關閉資料庫連接")
    def load_institutional_trading_details(self, stock_id, start_date=None, end_date=None):
        """載入主力與非主力交易明細
        
        Args:
            stock_id (str): 股票代碼
            start_date (str, optional): 起始日期 (YYYY-MM-DD)
            end_date (str, optional): 結束日期 (YYYY-MM-DD)
            
        Returns:
            DataFrame: 主力與非主力交易明細
        """
        try:
            # 檢查資料表是否存在
            cursor = self.conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='institutional_trading_details'")
            table_exists = cursor.fetchone() is not None
            
            if not table_exists:
                logger.warning("資料庫中不存在主力交易明細資料表")
                return None
            
            # 建立查詢
            query = f"""
                SELECT * FROM institutional_trading_details 
                WHERE stock_id = '{stock_id}'
            """
            
            if start_date:
                query += f" AND date >= '{start_date}'"
            if end_date:
                query += f" AND date <= '{end_date}'"
            
            query += " ORDER BY date"
            
            # 執行查詢
            df = pd.read_sql(query, self.conn)
            
            if len(df) == 0:
                logger.warning(f"未找到 {stock_id} 的主力交易明細資料")
                return None
            
            # 確保日期格式正確
            df['date'] = pd.to_datetime(df['date'])
            
            logger.info(f"成功載入 {stock_id} 的主力交易明細，資料筆數: {len(df)}")
            return df
            
        except Exception as e:
            logger.error(f"載入 {stock_id} 主力交易明細時發生錯誤: {e}")
            return None

# 使用範例
if __name__ == "__main__":
    # 初始化預測器
    predictor = StockPricePredictor()
    
    try:
        # 訓練模型 (以台積電為例)
        stock_id = "2330"
        result = predictor.train_model(stock_id, prediction_days=1)
        
        if result:
            # 預測未來漲跌
            prediction = predictor.predict_price_movement(stock_id)
            if prediction:
                print(f"預測 {stock_id} 未來1天的走勢: {prediction['prediction']}")
                print(f"預測機率: {prediction['probability']:.4f}")
            
            # 繪製股價走勢與預測
            predictor.plot_predictions(stock_id)
    finally:
        # 確保資料庫連接被正確關閉
        predictor.close()
