#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建台灣加權指數表並插入示例數據
"""

import logging
import os
import sqlite3
from datetime import datetime, timedelta

# 設置日誌
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

logger = logging.getLogger("CreateTWIITable")


def create_market_index_table(db_path):
    """
    創建 market_index_history 表
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 創建表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS market_index_history (
            date DATE,
            index_code TEXT,
            close REAL,
            PRIMARY KEY (date, index_code)
        )
        """
        )

        conn.commit()
        conn.close()
        logger.info("✅ market_index_history 表創建成功")
        return True

    except Exception as e:
        logger.error(f"創建表時發生錯誤: {e}")
        return False


def insert_sample_twii_data(db_path):
    """
    插入一些示例的台灣加權指數數據
    這些是模擬數據，僅用於演示
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 生成最近30天的示例數據
        base_price = 17500  # 台灣加權指數基準價格
        sample_data = []

        for i in range(30, 0, -1):
            date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            # 模擬價格波動 (±2%)
            import random

            price_change = random.uniform(-0.02, 0.02)
            price = base_price * (1 + price_change)
            sample_data.append((date, "TWII", round(price, 2)))
            base_price = price  # 下一天的基準價格

        # 插入數據
        cursor.executemany(
            "INSERT OR REPLACE INTO market_index_history (date, index_code, close) VALUES (?, ?, ?)",
            sample_data,
        )

        conn.commit()
        conn.close()

        logger.info(f"✅ 插入了 {len(sample_data)} 筆示例台灣加權指數數據")
        logger.info(
            f"價格範圍: {min(data[2] for data in sample_data):.2f} - {max(data[2] for data in sample_data):.2f}"
        )
        return True

    except Exception as e:
        logger.error(f"插入示例數據時發生錯誤: {e}")
        return False


def verify_data(db_path):
    """
    驗證插入的數據
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 查詢總行數
        cursor.execute("SELECT COUNT(*) FROM market_index_history WHERE index_code = 'TWII'")
        total_count = cursor.fetchone()[0]
        logger.info(f"資料庫中 TWII 數據總數: {total_count} 筆")

        if total_count > 0:
            # 查詢最新的 5 筆數據
            cursor.execute(
                """
                SELECT date, close 
                FROM market_index_history 
                WHERE index_code = 'TWII' 
                ORDER BY date DESC 
                LIMIT 5
            """
            )

            recent_data = cursor.fetchall()
            logger.info("最新 5 筆台灣加權指數數據:")
            for date, price in recent_data:
                logger.info(f"  {date}: {price:,.2f}")

        conn.close()
        return True

    except Exception as e:
        logger.error(f"驗證數據時發生錯誤: {e}")
        return False


def main():
    """
    主函數
    """
    logger.info("=" * 50)
    logger.info("創建台灣加權指數表並插入示例數據")
    logger.info("=" * 50)

    # 設置資料庫路徑
    db_path = os.path.join(os.path.dirname(__file__), "tw_stock_data.db")
    logger.info(f"使用資料庫: {db_path}")

    # 創建表
    if not create_market_index_table(db_path):
        logger.error("❌ 創建表失敗")
        return

    # 插入示例數據
    if not insert_sample_twii_data(db_path):
        logger.error("❌ 插入示例數據失敗")
        return

    # 驗證數據
    if not verify_data(db_path):
        logger.error("❌ 驗證數據失敗")
        return

    logger.info("\n🎉 完成！")
    logger.info("\n📝 注意事項:")
    logger.info("1. 這些是示例數據，不是真實的台灣加權指數")
    logger.info("2. 要獲取真實數據，請:")
    logger.info("   - 設置 FinLab API 金鑰: export FINLAB_API_KEY='your_api_key'")
    logger.info("   - 執行: python3 download_twii_index.py")
    logger.info("3. 現在可以重新啟動 web_app.py 查看效果")


if __name__ == "__main__":
    main()
