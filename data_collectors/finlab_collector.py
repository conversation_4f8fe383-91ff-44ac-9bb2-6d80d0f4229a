import pandas as pd
import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import os

class FinLabCollector:
    """
    使用FinLab快速獲取台股歷史數據的收集器
    """
    
    def __init__(self, database_path: str = None):
        """
        初始化FinLab收集器
        
        Args:
            database_path (str): 資料庫路徑
        """
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s: %(message)s'
        )
        self.logger = logging.getLogger("FinLabCollector")
        
        if database_path is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            base_dir = os.path.dirname(current_dir)
            self.database_path = os.path.join(base_dir, "database", "tw_stock_data.db")
            os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
        else:
            self.database_path = database_path
            
        # 資料庫連接
        self.conn = sqlite3.connect(self.database_path)
        
        # 檢查FinLab是否可用
        self.finlab_available = self._check_finlab_availability()
    
    def _check_finlab_availability(self) -> bool:
        """檢查FinLab是否可用"""
        try:
            import finlab
            self.logger.info("FinLab 模組可用")
            return True
        except ImportError:
            self.logger.warning("FinLab 模組未安裝，請執行: pip install finlab")
            return False
    
    def download_historical_data_finlab(self, stock_ids: List[str] = None, years: int = 15, incremental: bool = True) -> bool:
        """
        使用FinLab快速下載歷史數據（支持增量更新）

        Args:
            stock_ids: 股票代碼列表，None表示下載所有股票
            years: 下載幾年的歷史數據
            incremental: 是否只下載資料庫中沒有的數據（默認True）

        Returns:
            bool: 是否成功
        """
        if not self.finlab_available:
            self.logger.error("FinLab 不可用，無法下載歷史數據")
            return False

        try:
            import finlab
            from finlab import data

            if incremental:
                self.logger.info(f"開始使用FinLab增量下載{years}年歷史數據（僅下載缺失數據）...")
            else:
                self.logger.info(f"開始使用FinLab完整下載{years}年歷史數據...")

            # 設定日期範圍
            end_date = datetime.now()
            start_date = end_date - timedelta(days=years*365)

            # 獲取股價數據
            if stock_ids:
                self.logger.info(f"處理指定的 {len(stock_ids)} 支股票")
                # 下載指定股票
                for stock_id in stock_ids:
                    try:
                        if incremental:
                            self._download_single_stock_incremental(stock_id, start_date, end_date)
                        else:
                            self._download_single_stock_finlab(stock_id, start_date, end_date)
                    except Exception as e:
                        self.logger.error(f"下載股票 {stock_id} 失敗: {e}")
            else:
                self.logger.info("處理所有台股數據...")
                # 下載所有股票數據
                if incremental:
                    self._download_all_stocks_incremental(start_date, end_date)
                else:
                    self._download_all_stocks_finlab(start_date, end_date)

            self.logger.info("FinLab歷史數據下載完成")
            return True

        except Exception as e:
            self.logger.error(f"FinLab下載過程發生錯誤: {e}")
            return False
    
    def _download_single_stock_finlab(self, stock_id: str, start_date: datetime, end_date: datetime):
        """使用FinLab下載單支股票數據"""
        try:
            import finlab
            from finlab import data
            
            # 獲取股價數據
            price_data = data.get('price:收盤價')[stock_id]
            volume_data = data.get('price:成交股數')[stock_id]
            
            # 篩選日期範圍
            mask = (price_data.index >= start_date) & (price_data.index <= end_date)
            price_data = price_data[mask]
            volume_data = volume_data[mask]
            
            if len(price_data) == 0:
                self.logger.warning(f"股票 {stock_id} 沒有數據")
                return
            
            # 構建DataFrame
            df = pd.DataFrame({
                'stock_id': stock_id,
                'date': price_data.index.strftime('%Y-%m-%d'),
                'close': price_data.values,
                'volume': volume_data.values,
                'open': price_data.values,  # FinLab可能需要額外獲取
                'high': price_data.values,  # 簡化處理，實際應該獲取真實的開高低
                'low': price_data.values
            })
            
            # 保存到資料庫
            df.to_sql('daily_prices', self.conn, if_exists='append', index=False)
            self.logger.info(f"成功保存股票 {stock_id} 的 {len(df)} 筆數據")
            
        except Exception as e:
            self.logger.error(f"下載股票 {stock_id} 時發生錯誤: {e}")

    def _download_single_stock_incremental(self, stock_id: str, start_date: datetime, end_date: datetime):
        """增量下載單支股票數據（只下載缺失的數據）"""
        try:
            import finlab
            from finlab import data

            # 檢查資料庫中已有的數據範圍
            existing_dates = self._get_existing_dates(stock_id)

            if not existing_dates:
                self.logger.info(f"股票 {stock_id} 無現有數據，下載完整歷史數據")
                self._download_single_stock_finlab(stock_id, start_date, end_date)
                return

            # 找出缺失的日期範圍
            missing_ranges = self._find_missing_date_ranges(stock_id, start_date, end_date, existing_dates)

            if not missing_ranges:
                self.logger.info(f"股票 {stock_id} 數據已完整，跳過下載")
                return

            self.logger.info(f"股票 {stock_id} 發現 {len(missing_ranges)} 個缺失日期範圍")

            # 獲取FinLab數據
            price_data = data.get('price:收盤價')[stock_id]
            volume_data = data.get('price:成交股數')[stock_id]

            # 只下載缺失範圍的數據
            total_new_records = 0
            for range_start, range_end in missing_ranges:
                mask = (price_data.index >= range_start) & (price_data.index <= range_end)
                range_price_data = price_data[mask]
                range_volume_data = volume_data[mask]

                if len(range_price_data) == 0:
                    continue

                # 構建DataFrame
                df = pd.DataFrame({
                    'stock_id': stock_id,
                    'date': range_price_data.index.strftime('%Y-%m-%d'),
                    'close': range_price_data.values,
                    'volume': range_volume_data.values,
                    'open': range_price_data.values,  # 簡化處理
                    'high': range_price_data.values,
                    'low': range_price_data.values
                })

                # 保存到資料庫
                df.to_sql('daily_prices', self.conn, if_exists='append', index=False)
                total_new_records += len(df)

                self.logger.info(f"股票 {stock_id} 補充 {range_start.strftime('%Y-%m-%d')} 到 {range_end.strftime('%Y-%m-%d')} 的 {len(df)} 筆數據")

            self.logger.info(f"股票 {stock_id} 增量下載完成，新增 {total_new_records} 筆數據")

        except Exception as e:
            self.logger.error(f"增量下載股票 {stock_id} 時發生錯誤: {e}")

    def _get_existing_dates(self, stock_id: str) -> set:
        """獲取資料庫中已存在的日期"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(
                "SELECT date FROM daily_prices WHERE stock_id = ? ORDER BY date",
                (stock_id,)
            )
            dates = cursor.fetchall()
            return set(pd.to_datetime([date[0] for date in dates]).date)
        except Exception as e:
            self.logger.warning(f"獲取股票 {stock_id} 現有日期時發生錯誤: {e}")
            return set()

    def _find_missing_date_ranges(self, stock_id: str, start_date: datetime, end_date: datetime, existing_dates: set) -> List[tuple]:
        """找出缺失的日期範圍"""
        try:
            # 生成完整的日期範圍（只包含工作日）
            all_dates = pd.bdate_range(start=start_date, end=end_date)
            all_dates_set = set(all_dates.date)

            # 找出缺失的日期
            missing_dates = sorted(all_dates_set - existing_dates)

            if not missing_dates:
                return []

            # 將連續的缺失日期合併為範圍
            ranges = []
            range_start = missing_dates[0]
            range_end = missing_dates[0]

            for i in range(1, len(missing_dates)):
                current_date = missing_dates[i]
                prev_date = missing_dates[i-1]

                # 如果日期連續（考慮週末），繼續當前範圍
                if (current_date - prev_date).days <= 3:  # 允許週末間隔
                    range_end = current_date
                else:
                    # 開始新範圍
                    ranges.append((pd.Timestamp(range_start), pd.Timestamp(range_end)))
                    range_start = current_date
                    range_end = current_date

            # 添加最後一個範圍
            ranges.append((pd.Timestamp(range_start), pd.Timestamp(range_end)))

            return ranges

        except Exception as e:
            self.logger.error(f"計算缺失日期範圍時發生錯誤: {e}")
            return []

    def _download_all_stocks_incremental(self, start_date: datetime, end_date: datetime):
        """增量下載所有股票數據"""
        try:
            import finlab
            from finlab import data

            # 獲取所有股票的收盤價數據
            all_prices = data.get('price:收盤價')
            stock_list = all_prices.columns.tolist()

            self.logger.info(f"開始增量處理 {len(stock_list)} 支股票")

            # 批量處理
            batch_size = 50
            for i in range(0, len(stock_list), batch_size):
                batch_stocks = stock_list[i:i+batch_size]
                self.logger.info(f"處理批次 {i//batch_size + 1}: 股票 {i+1}-{min(i+batch_size, len(stock_list))}")

                for stock_id in batch_stocks:
                    try:
                        self._download_single_stock_incremental(stock_id, start_date, end_date)
                    except Exception as e:
                        self.logger.warning(f"處理股票 {stock_id} 時發生錯誤: {e}")

        except Exception as e:
            self.logger.error(f"批量增量下載過程發生錯誤: {e}")

    def _download_all_stocks_finlab(self, start_date: datetime, end_date: datetime):
        """使用FinLab下載所有股票數據"""
        try:
            import finlab
            from finlab import data
            
            # 獲取所有股票的收盤價數據
            all_prices = data.get('price:收盤價')
            all_volumes = data.get('price:成交股數')
            
            # 篩選日期範圍
            mask = (all_prices.index >= start_date) & (all_prices.index <= end_date)
            all_prices = all_prices[mask]
            all_volumes = all_volumes[mask]
            
            # 批量處理並保存
            batch_size = 100
            stock_list = all_prices.columns.tolist()
            
            for i in range(0, len(stock_list), batch_size):
                batch_stocks = stock_list[i:i+batch_size]
                self.logger.info(f"處理批次 {i//batch_size + 1}: 股票 {i+1}-{min(i+batch_size, len(stock_list))}")
                
                batch_data = []
                for stock_id in batch_stocks:
                    try:
                        price_series = all_prices[stock_id].dropna()
                        volume_series = all_volumes[stock_id].dropna()
                        
                        if len(price_series) == 0:
                            continue
                        
                        # 構建數據
                        for date, price in price_series.items():
                            volume = volume_series.get(date, 0)
                            batch_data.append({
                                'stock_id': stock_id,
                                'date': date.strftime('%Y-%m-%d'),
                                'close': price,
                                'volume': volume,
                                'open': price,  # 簡化處理
                                'high': price,
                                'low': price
                            })
                    except Exception as e:
                        self.logger.warning(f"處理股票 {stock_id} 時發生錯誤: {e}")
                
                # 批量保存
                if batch_data:
                    df = pd.DataFrame(batch_data)
                    df.to_sql('daily_prices', self.conn, if_exists='append', index=False)
                    self.logger.info(f"批次保存完成，共 {len(batch_data)} 筆數據")
            
        except Exception as e:
            self.logger.error(f"批量下載過程發生錯誤: {e}")
    
    def get_stock_list_finlab(self) -> List[str]:
        """使用FinLab獲取股票清單"""
        try:
            import finlab
            from finlab import data
            
            # 獲取所有股票代碼
            all_prices = data.get('price:收盤價')
            stock_list = all_prices.columns.tolist()
            
            self.logger.info(f"從FinLab獲取到 {len(stock_list)} 支股票")
            return stock_list
            
        except Exception as e:
            self.logger.error(f"獲取FinLab股票清單失敗: {e}")
            return []
    
    def get_database_status(self) -> Dict:
        """獲取資料庫狀態信息"""
        try:
            cursor = self.conn.cursor()

            # 獲取股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM daily_prices")
            stock_count = cursor.fetchone()[0]

            # 獲取總記錄數
            cursor.execute("SELECT COUNT(*) FROM daily_prices")
            total_records = cursor.fetchone()[0]

            # 獲取日期範圍
            cursor.execute("SELECT MIN(date), MAX(date) FROM daily_prices")
            date_range = cursor.fetchone()

            # 獲取每支股票的數據統計
            cursor.execute("""
                SELECT stock_id, COUNT(*) as records, MIN(date) as start_date, MAX(date) as end_date
                FROM daily_prices
                GROUP BY stock_id
                ORDER BY records DESC
                LIMIT 10
            """)
            top_stocks = cursor.fetchall()

            # 找出數據不完整的股票（記錄數少於預期）
            cursor.execute("""
                SELECT stock_id, COUNT(*) as records
                FROM daily_prices
                GROUP BY stock_id
                HAVING records < 1000
                ORDER BY records ASC
                LIMIT 10
            """)
            incomplete_stocks = cursor.fetchall()

            return {
                'stock_count': stock_count,
                'total_records': total_records,
                'date_range': date_range,
                'top_stocks': top_stocks,
                'incomplete_stocks': incomplete_stocks
            }

        except Exception as e:
            self.logger.error(f"獲取資料庫狀態時發生錯誤: {e}")
            return {}

    def suggest_missing_data(self) -> List[str]:
        """建議需要補充數據的股票"""
        try:
            cursor = self.conn.cursor()

            # 找出數據記錄較少的股票
            cursor.execute("""
                SELECT stock_id, COUNT(*) as records
                FROM daily_prices
                GROUP BY stock_id
                HAVING records < 2000
                ORDER BY records ASC
            """)

            incomplete_stocks = cursor.fetchall()
            suggestions = [stock[0] for stock in incomplete_stocks]

            self.logger.info(f"發現 {len(suggestions)} 支股票可能需要補充數據")
            return suggestions

        except Exception as e:
            self.logger.error(f"分析缺失數據時發生錯誤: {e}")
            return []

    def close(self):
        """關閉資料庫連接"""
        if self.conn:
            self.conn.close()

def main():
    """測試FinLab收集器"""
    collector = FinLabCollector()
    try:
        # 測試下載少量股票的歷史數據
        test_stocks = ['2330', '2317', '2454']
        success = collector.download_historical_data_finlab(test_stocks, years=1)
        
        if success:
            print("FinLab數據下載測試成功")
        else:
            print("FinLab數據下載測試失敗")
            
    finally:
        collector.close()

if __name__ == "__main__":
    main()
