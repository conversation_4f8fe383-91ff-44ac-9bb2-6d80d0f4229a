import pandas as pd
import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import os

class FinLabCollector:
    """
    使用FinLab快速獲取台股歷史數據的收集器
    """
    
    def __init__(self, database_path: str = None):
        """
        初始化FinLab收集器
        
        Args:
            database_path (str): 資料庫路徑
        """
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s: %(message)s'
        )
        self.logger = logging.getLogger("FinLabCollector")
        
        if database_path is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            base_dir = os.path.dirname(current_dir)
            self.database_path = os.path.join(base_dir, "database", "tw_stock_data.db")
            os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
        else:
            self.database_path = database_path
            
        # 資料庫連接
        self.conn = sqlite3.connect(self.database_path)
        
        # 檢查FinLab是否可用
        self.finlab_available = self._check_finlab_availability()
    
    def _check_finlab_availability(self) -> bool:
        """檢查FinLab是否可用"""
        try:
            import finlab
            self.logger.info("FinLab 模組可用")
            return True
        except ImportError:
            self.logger.warning("FinLab 模組未安裝，請執行: pip install finlab")
            return False
    
    def download_historical_data_finlab(self, stock_ids: List[str] = None, years: int = 15) -> bool:
        """
        使用FinLab快速下載歷史數據
        
        Args:
            stock_ids: 股票代碼列表，None表示下載所有股票
            years: 下載幾年的歷史數據
            
        Returns:
            bool: 是否成功
        """
        if not self.finlab_available:
            self.logger.error("FinLab 不可用，無法下載歷史數據")
            return False
        
        try:
            import finlab
            from finlab import data
            
            self.logger.info(f"開始使用FinLab下載{years}年歷史數據...")
            
            # 設定日期範圍
            end_date = datetime.now()
            start_date = end_date - timedelta(days=years*365)
            
            # 獲取股價數據
            if stock_ids:
                self.logger.info(f"下載指定的 {len(stock_ids)} 支股票")
                # 下載指定股票
                for stock_id in stock_ids:
                    try:
                        self._download_single_stock_finlab(stock_id, start_date, end_date)
                    except Exception as e:
                        self.logger.error(f"下載股票 {stock_id} 失敗: {e}")
            else:
                self.logger.info("下載所有台股數據...")
                # 下載所有股票數據
                self._download_all_stocks_finlab(start_date, end_date)
            
            self.logger.info("FinLab歷史數據下載完成")
            return True
            
        except Exception as e:
            self.logger.error(f"FinLab下載過程發生錯誤: {e}")
            return False
    
    def _download_single_stock_finlab(self, stock_id: str, start_date: datetime, end_date: datetime):
        """使用FinLab下載單支股票數據"""
        try:
            import finlab
            from finlab import data
            
            # 獲取股價數據
            price_data = data.get('price:收盤價')[stock_id]
            volume_data = data.get('price:成交股數')[stock_id]
            
            # 篩選日期範圍
            mask = (price_data.index >= start_date) & (price_data.index <= end_date)
            price_data = price_data[mask]
            volume_data = volume_data[mask]
            
            if len(price_data) == 0:
                self.logger.warning(f"股票 {stock_id} 沒有數據")
                return
            
            # 構建DataFrame
            df = pd.DataFrame({
                'stock_id': stock_id,
                'date': price_data.index.strftime('%Y-%m-%d'),
                'close': price_data.values,
                'volume': volume_data.values,
                'open': price_data.values,  # FinLab可能需要額外獲取
                'high': price_data.values,  # 簡化處理，實際應該獲取真實的開高低
                'low': price_data.values
            })
            
            # 保存到資料庫
            df.to_sql('daily_prices', self.conn, if_exists='append', index=False)
            self.logger.info(f"成功保存股票 {stock_id} 的 {len(df)} 筆數據")
            
        except Exception as e:
            self.logger.error(f"下載股票 {stock_id} 時發生錯誤: {e}")
    
    def _download_all_stocks_finlab(self, start_date: datetime, end_date: datetime):
        """使用FinLab下載所有股票數據"""
        try:
            import finlab
            from finlab import data
            
            # 獲取所有股票的收盤價數據
            all_prices = data.get('price:收盤價')
            all_volumes = data.get('price:成交股數')
            
            # 篩選日期範圍
            mask = (all_prices.index >= start_date) & (all_prices.index <= end_date)
            all_prices = all_prices[mask]
            all_volumes = all_volumes[mask]
            
            # 批量處理並保存
            batch_size = 100
            stock_list = all_prices.columns.tolist()
            
            for i in range(0, len(stock_list), batch_size):
                batch_stocks = stock_list[i:i+batch_size]
                self.logger.info(f"處理批次 {i//batch_size + 1}: 股票 {i+1}-{min(i+batch_size, len(stock_list))}")
                
                batch_data = []
                for stock_id in batch_stocks:
                    try:
                        price_series = all_prices[stock_id].dropna()
                        volume_series = all_volumes[stock_id].dropna()
                        
                        if len(price_series) == 0:
                            continue
                        
                        # 構建數據
                        for date, price in price_series.items():
                            volume = volume_series.get(date, 0)
                            batch_data.append({
                                'stock_id': stock_id,
                                'date': date.strftime('%Y-%m-%d'),
                                'close': price,
                                'volume': volume,
                                'open': price,  # 簡化處理
                                'high': price,
                                'low': price
                            })
                    except Exception as e:
                        self.logger.warning(f"處理股票 {stock_id} 時發生錯誤: {e}")
                
                # 批量保存
                if batch_data:
                    df = pd.DataFrame(batch_data)
                    df.to_sql('daily_prices', self.conn, if_exists='append', index=False)
                    self.logger.info(f"批次保存完成，共 {len(batch_data)} 筆數據")
            
        except Exception as e:
            self.logger.error(f"批量下載過程發生錯誤: {e}")
    
    def get_stock_list_finlab(self) -> List[str]:
        """使用FinLab獲取股票清單"""
        try:
            import finlab
            from finlab import data
            
            # 獲取所有股票代碼
            all_prices = data.get('price:收盤價')
            stock_list = all_prices.columns.tolist()
            
            self.logger.info(f"從FinLab獲取到 {len(stock_list)} 支股票")
            return stock_list
            
        except Exception as e:
            self.logger.error(f"獲取FinLab股票清單失敗: {e}")
            return []
    
    def close(self):
        """關閉資料庫連接"""
        if self.conn:
            self.conn.close()

def main():
    """測試FinLab收集器"""
    collector = FinLabCollector()
    try:
        # 測試下載少量股票的歷史數據
        test_stocks = ['2330', '2317', '2454']
        success = collector.download_historical_data_finlab(test_stocks, years=1)
        
        if success:
            print("FinLab數據下載測試成功")
        else:
            print("FinLab數據下載測試失敗")
            
    finally:
        collector.close()

if __name__ == "__main__":
    main()
