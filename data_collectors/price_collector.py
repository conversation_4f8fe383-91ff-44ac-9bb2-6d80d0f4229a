import logging
import os
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import pandas as pd
import requests
import twstock
import yfinance as yf


class PriceCollector:
    """
    股價數據收集器，用於從多個來源收集股票價格和基本資訊
    支援台灣證券交易所、Yahoo Finance等多個數據源
    """

    def __init__(self, database_path=None):
        """
        初始化股價收集器

        Args:
            database_path (str): 資料庫路徑
        """
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s: %(message)s"
        )
        self.logger = logging.getLogger("PriceCollector")
        if database_path is None:
            # 指定固定的絕對路徑，放在data子目錄下
            current_dir = os.path.dirname(os.path.abspath(__file__))
            base_dir = os.path.dirname(os.path.dirname(current_dir))
            self.database_path = os.path.join(base_dir, "database", "tw_stock_data.db")

            # 確保目錄存在
            os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
        else:
            self.database_path = database_path
        # 資料庫連接
        self.conn = sqlite3.connect(database_path)

        # 創建必要的資料表
        self._create_tables()

    def _create_tables(self):
        """
        創建必要的資料庫表格
        """
        cursor = self.conn.cursor()

        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS company_info (
            stock_id TEXT PRIMARY KEY,
            公司簡稱 TEXT,
            公司名稱 TEXT,
            產業類別 TEXT,
            上市日期 TEXT,
            市值 REAL,
            更新時間 TEXT
        )
        """
        )
        # 股票基本資訊表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS stock_info (
            stock_id TEXT PRIMARY KEY,
            name TEXT,
            industry TEXT,
            market TEXT,
            listed_date DATE,
            capital REAL,
            outstanding_shares REAL
        )
        """
        )

        # 每日股價表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS daily_prices (
            stock_id TEXT,
            date DATE,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume INTEGER,
            PRIMARY KEY (stock_id, date)
        )
        """
        )

        # 技術指標表
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS technical_indicators (
            stock_id TEXT,
            date DATE,
            MA5 REAL,
            MA20 REAL,
            MA60 REAL,
            RSI REAL,
            MACD REAL,
            KD REAL,
            PRIMARY KEY (stock_id, date)
        )
        """
        )

        self.conn.commit()

    def fetch_twse_stock_list(self) -> List[Dict]:
        try:
            # 台灣證券交易所上市公司清單 API - 新API
            url = "https://openapi.twse.com.tw/v1/exchangeReport/STOCK_DAY_ALL"

            response = requests.get(url)

            # 檢查回應
            if response.status_code != 200:
                self.logger.error(f"證交所 API 回應非 200: {response.status_code}")
                return self._get_stock_list_backup()

            # 嘗試解析 JSON
            data = response.json()
            stocks = []

            # 這是一個列表而不是字典，所以不能使用 get 方法
            for item in data:
                # 使用索引操作符而不是 get 方法
                stocks.append(
                    {
                        "stock_id": (
                            item.get("Code", "") if isinstance(item, dict) else str(item[0]).strip()
                        ),
                        "name": (
                            item.get("Name", "") if isinstance(item, dict) else str(item[1]).strip()
                        ),
                        "market": "上市",
                    }
                )

            return stocks

        except Exception as e:
            self.logger.error(f"獲取證交所股票清單時發生錯誤: {e}")
            return self._get_stock_list_backup()

    def _get_stock_list_backup(self) -> List[Dict]:
        """當證交所 API 失敗時的備用方法"""
        try:
            # 使用 twstock 套件作為備用
            import twstock

            stocks = []
            for stock_id, stock_info in twstock.codes.items():
                if stock_info.type == "股票":  # 只選擇一般股票
                    stocks.append(
                        {
                            "stock_id": stock_id,
                            "name": stock_info.name,
                            "market": "上市" if stock_info.market == "上市" else "上櫃",
                        }
                    )
            return stocks
        except Exception as e:
            self.logger.error(f"備用股票清單方法也失敗: {e}")
            # 最後回傳幾個常用的股票
            return [
                {"stock_id": "2330", "name": "台積電", "market": "上市"},
                {"stock_id": "2317", "name": "鴻海", "market": "上市"},
                {"stock_id": "2454", "name": "聯發科", "market": "上市"},
                {"stock_id": "0050", "name": "元大台灣50", "market": "上市"},
                {"stock_id": "2308", "name": "台達電", "market": "上市"},
                {"stock_id": "2881", "name": "富邦金", "market": "上市"},
            ]

    def fetch_stock_prices_from_twse(self, stock_id: str) -> pd.DataFrame:
        """使用台灣證券交易所API獲取即時股價數據"""
        import json
        import random
        import time

        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                # 判斷是上市還是上櫃股票
                if stock_id.startswith("6"):
                    ex_ch = f"otc_{stock_id}.tw"
                else:
                    ex_ch = f"tse_{stock_id}.tw"

                # 構建API URL
                url = f"http://mis.twse.com.tw/stock/api/getStockInfo.jsp?ex_ch={ex_ch}&json=1&delay=0"

                # 添加延遲避免API限制
                if attempt > 0:
                    delay = retry_delay * (2**attempt) + random.uniform(1, 3)
                    self.logger.info(f"第 {attempt + 1} 次嘗試獲取 {stock_id}，等待 {delay:.2f} 秒")
                    time.sleep(delay)
                else:
                    time.sleep(random.uniform(0.5, 1.5))

                # 發送請求
                response = requests.get(url, timeout=10)

                if response.status_code != 200:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"API回應狀態碼 {response.status_code}，將重試")
                        continue
                    else:
                        self.logger.error(
                            f"獲取 {stock_id} 股價失敗，狀態碼: {response.status_code}"
                        )
                        return pd.DataFrame()

                # 解析JSON數據
                data = response.json()

                if "msgArray" not in data or not data["msgArray"]:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"第 {attempt + 1} 次嘗試：{stock_id} 沒有數據，將重試")
                        continue
                    else:
                        self.logger.warning(f"股票 {stock_id} 沒有數據")
                        return pd.DataFrame()

                stock_data = data["msgArray"][0]

                # 構建DataFrame
                df_data = {
                    "date": [datetime.now().strftime("%Y-%m-%d")],
                    "open": [float(stock_data.get("o", 0)) if stock_data.get("o") else 0],
                    "high": [float(stock_data.get("h", 0)) if stock_data.get("h") else 0],
                    "low": [float(stock_data.get("l", 0)) if stock_data.get("l") else 0],
                    "close": [float(stock_data.get("z", 0)) if stock_data.get("z") else 0],
                    "volume": [int(stock_data.get("v", 0)) if stock_data.get("v") else 0],
                }

                df = pd.DataFrame(df_data)

                # 檢查數據有效性
                if df["close"].iloc[0] == 0:
                    if attempt < max_retries - 1:
                        self.logger.warning(
                            f"第 {attempt + 1} 次嘗試：{stock_id} 收盤價為0，將重試"
                        )
                        continue
                    else:
                        self.logger.warning(f"股票 {stock_id} 收盤價為0")
                        return pd.DataFrame()

                self.logger.info(f"成功獲取 {stock_id} 即時股價數據")
                return df

            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"獲取 {stock_id} 股價時發生錯誤: {e}，將重試")
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                else:
                    self.logger.error(f"獲取 {stock_id} 股價時發生錯誤: {e}")
                    return pd.DataFrame()

        return pd.DataFrame()

    def fetch_stock_prices_historical_twse(
        self, stock_id: str, start_date: str, end_date: str
    ) -> pd.DataFrame:
        """使用台灣證券交易所網站獲取歷史股價數據"""
        import random
        import time
        from datetime import datetime, timedelta

        all_data = []
        current_date = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # 按月份獲取數據
        while current_date <= end_dt:
            try:
                year = current_date.year
                month = current_date.month

                # 使用證交所網站的API格式
                date_str = f"{year}{month:02d}01"  # 每月第一天
                url = f"https://www.twse.com.tw/exchangeReport/STOCK_DAY?response=json&date={date_str}&stockNo={stock_id}"

                self.logger.info(f"正在獲取 {stock_id} {year}年{month}月的數據...")

                # 添加延遲避免API限制
                time.sleep(random.uniform(3, 6))

                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                }

                response = requests.get(url, headers=headers, timeout=15)

                if response.status_code == 200:
                    try:
                        data = response.json()

                        # 檢查是否有數據
                        if "data" in data and data["data"]:
                            for row in data["data"]:
                                try:
                                    # 解析日期 (格式: 113/06/21 -> 2024/06/21)
                                    date_parts = row[0].split("/")
                                    if len(date_parts) == 3:
                                        # 民國年轉西元年
                                        year_ad = int(date_parts[0]) + 1911
                                        month_num = int(date_parts[1])
                                        day_num = int(date_parts[2])

                                        record_date = datetime(year_ad, month_num, day_num)

                                        # 檢查日期是否在範圍內
                                        if current_date <= record_date <= end_dt:
                                            # 解析股價數據 (移除逗號並轉換為數字)
                                            volume = (
                                                int(row[1].replace(",", ""))
                                                if row[1] != "--"
                                                else 0
                                            )
                                            open_price = (
                                                float(row[3].replace(",", ""))
                                                if row[3] != "--"
                                                else 0
                                            )
                                            high_price = (
                                                float(row[4].replace(",", ""))
                                                if row[4] != "--"
                                                else 0
                                            )
                                            low_price = (
                                                float(row[5].replace(",", ""))
                                                if row[5] != "--"
                                                else 0
                                            )
                                            close_price = (
                                                float(row[6].replace(",", ""))
                                                if row[6] != "--"
                                                else 0
                                            )

                                            stock_data = {
                                                "date": record_date.strftime("%Y-%m-%d"),
                                                "open": open_price,
                                                "high": high_price,
                                                "low": low_price,
                                                "close": close_price,
                                                "volume": volume,
                                            }
                                            all_data.append(stock_data)
                                except (ValueError, IndexError) as e:
                                    self.logger.warning(f"解析數據時發生錯誤: {e}, 數據: {row}")
                                    continue
                        else:
                            self.logger.warning(f"股票 {stock_id} 在 {year}年{month}月 沒有數據")
                    except Exception as json_error:
                        self.logger.warning(
                            f"JSON解析錯誤: {json_error}, 響應內容: {response.text[:200]}"
                        )
                else:
                    self.logger.warning(
                        f"API請求失敗，狀態碼: {response.status_code}, 響應: {response.text[:200]}"
                    )

                # 移動到下個月
                if month == 12:
                    current_date = datetime(year + 1, 1, 1)
                else:
                    current_date = datetime(year, month + 1, 1)

            except Exception as e:
                self.logger.error(
                    f"獲取 {stock_id} 在 {current_date.strftime('%Y-%m')} 的數據時發生錯誤: {e}"
                )
                # 移動到下個月
                if current_date.month == 12:
                    current_date = datetime(current_date.year + 1, 1, 1)
                else:
                    current_date = datetime(current_date.year, current_date.month + 1, 1)
                continue

        if all_data:
            df = pd.DataFrame(all_data)
            # 按日期排序
            df["date"] = pd.to_datetime(df["date"])
            df = df.sort_values("date")
            df["date"] = df["date"].dt.strftime("%Y-%m-%d")

            self.logger.info(f"成功獲取 {stock_id} 歷史數據，共 {len(df)} 筆記錄")
            return df
        else:
            self.logger.warning(f"未獲取到 {stock_id} 的歷史數據")
            return pd.DataFrame()

    def fetch_stock_prices(
        self, stock_id: str, start_date: Optional[str] = None, end_date: Optional[str] = None
    ) -> pd.DataFrame:
        """獲取股票價格數據 - 使用台灣證券交易所API"""

        # 如果沒有指定日期範圍，獲取即時數據
        if not start_date and not end_date:
            return self.fetch_stock_prices_from_twse(stock_id)

        # 如果指定了日期範圍，嘗試獲取歷史數據
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")

        # 對於歷史數據，先嘗試使用TWSE API，如果失敗則回退到即時數據
        try:
            df = self.fetch_stock_prices_historical_twse(stock_id, start_date, end_date)
            if not df.empty:
                return df
        except Exception as e:
            self.logger.warning(f"獲取 {stock_id} 歷史數據失敗: {e}，改用即時數據")

        # 回退到即時數據
        return self.fetch_stock_prices_from_twse(stock_id)

    def save_stock_prices(self, stock_id: str, prices_df: pd.DataFrame):
        """
        將股價資料寫入 daily_prices 資料表（包含成交量）
        """
        try:
            if prices_df.empty:
                self.logger.warning(f"{stock_id} 沒有股價資料可寫入")
                return

            # 確保日期格式正確
            prices_df["date"] = pd.to_datetime(prices_df["date"]).dt.strftime("%Y-%m-%d")
            prices_df["stock_id"] = stock_id

            # 選擇需要的欄位，確保包含成交量
            required_columns = ["stock_id", "date", "open", "high", "low", "close", "volume"]
            available_columns = [col for col in required_columns if col in prices_df.columns]

            if len(available_columns) < 6:  # 至少要有stock_id, date, close, volume
                self.logger.warning(f"{stock_id} 缺少必要的股價欄位: {available_columns}")
                return

            # 準備要寫入的數據
            df_to_save = prices_df[available_columns].copy()

            # 移除重複的記錄（基於stock_id和date）
            df_to_save = df_to_save.drop_duplicates(subset=["stock_id", "date"])

            # 寫入daily_prices表
            df_to_save.to_sql("daily_prices", self.conn, if_exists="append", index=False)
            self.logger.info(
                f"{stock_id} 寫入 daily_prices 成功，共 {len(df_to_save)} 筆資料（包含成交量）"
            )

        except Exception as e:
            self.logger.error(f"寫入 {stock_id} 股價資料時錯誤: {e}")

    def save_technical_indicators(self, stock_id: str, indicators_df: pd.DataFrame):
        """
        將技術指標寫入 technical_indicators 表格
        """
        try:
            if indicators_df.empty:
                self.logger.warning(f"{stock_id} 沒有技術指標資料可寫入")
                return

            indicators_df["date"] = pd.to_datetime(indicators_df["date"]).dt.strftime("%Y-%m-%d")
            indicators_df["stock_id"] = stock_id

            # 移除重複的記錄
            indicators_df = indicators_df.drop_duplicates(subset=["stock_id", "date"])

            # 寫入technical_indicators表
            indicators_df.to_sql("technical_indicators", self.conn, if_exists="append", index=False)
            self.logger.info(
                f"{stock_id} 寫入 technical_indicators 成功，共 {len(indicators_df)} 筆"
            )

        except Exception as e:
            self.logger.error(f"寫入技術指標資料時錯誤: {e}")

    def update_stock_info(self, stocks: List[Dict]):
        """
        更新股票基本資訊

        Args:
            stocks (List[Dict]): 股票基本資訊列表
        """
        try:
            cursor = self.conn.cursor()

            for stock in stocks:
                cursor.execute(
                    """
                INSERT OR REPLACE INTO stock_info 
                (stock_id, name, market) 
                VALUES (?, ?, ?)
                """,
                    (stock.get("stock_id", ""), stock.get("name", ""), stock.get("market", "")),
                )

            self.conn.commit()
            self.logger.info(f"成功更新 {len(stocks)} 支股票基本資訊")

        except Exception as e:
            self.logger.error(f"更新股票基本資訊時發生錯誤: {e}")

    def batch_update_stock_prices(
        self,
        stock_ids: List[str],
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        daily_only: bool = True,
    ):
        """
        批量更新多支股票的股價

        Args:
            stock_ids: 股票代碼列表
            start_date: 開始日期
            end_date: 結束日期
            daily_only: 是否只更新當日數據（默認True，大幅提升速度）
        """
        import random
        import time

        success_count = 0
        total_count = len(stock_ids)

        # 如果是日常更新，只獲取最近1天的數據
        if daily_only and not start_date:
            start_date = datetime.now().strftime("%Y-%m-%d")
            end_date = datetime.now().strftime("%Y-%m-%d")
            self.logger.info(f"日常更新模式：只下載 {start_date} 的數據")

        for i, stock_id in enumerate(stock_ids, 1):
            try:
                self.logger.info(f"正在更新股票 {stock_id} ({i}/{total_count})")

                # 獲取股價數據
                prices_df = self.fetch_stock_prices(stock_id, start_date, end_date)

                if not prices_df.empty:
                    # 保存股價數據
                    self.save_stock_prices(stock_id, prices_df)

                    # TODO: 技術指標計算可以後續添加
                    # 目前專注於快速數據更新

                    success_count += 1
                    self.logger.info(f"成功更新股票 {stock_id}")
                else:
                    self.logger.warning(f"股票 {stock_id} 沒有獲取到數據")

                # 日常更新時減少延遲時間
                if i < total_count:
                    if daily_only:
                        delay = random.uniform(0.5, 1.5)  # 日常更新：0.5-1.5秒
                    else:
                        delay = random.uniform(3, 6)  # 歷史數據：3-6秒
                    time.sleep(delay)

            except Exception as e:
                self.logger.error(f"更新股票 {stock_id} 時發生錯誤: {e}")
                # 如果出錯也要延遲，避免連續錯誤
                if i < total_count:
                    time.sleep(random.uniform(1, 2))

        self.logger.info(f"批量更新完成，成功: {success_count}/{total_count}")

    def collect_and_update_data(self):
        """
        執行完整的數據收集和更新流程
        """
        try:
            # 獲取股票清單
            stock_list = self.fetch_twse_stock_list()

            # 更新股票基本資訊
            self.update_stock_info(stock_list)

            # 批次更新股價（限制數量，避免過度請求）
            batch_size = 50
            for i in range(0, len(stock_list), batch_size):
                batch_stocks = [stock["stock_id"] for stock in stock_list[i : i + batch_size]]
                self.batch_update_stock_prices(batch_stocks)

        except Exception as e:
            self.logger.error(f"數據收集過程中發生錯誤: {e}")

    def close(self):
        """關閉資料庫連接"""
        if self.conn:
            self.conn.close()


# 使用範例
def main():
    collector = PriceCollector()
    try:
        # 執行完整的數據收集
        collector.collect_and_update_data()

        # 單獨獲取特定股票的股價
        prices_df = collector.fetch_stock_prices("2330")
        print("台積電最近股價:")
        print(prices_df.head())

    finally:
        collector.close()


if __name__ == "__main__":
    main()
