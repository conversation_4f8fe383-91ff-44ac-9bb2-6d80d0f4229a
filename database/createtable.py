# createtable.py
import sqlite3
import os

# 資料庫檔案路徑 (建議從設定檔讀取或統一管理)
# 為了演示，這裡直接定義，實際項目中應更靈活
# current_dir = os.path.dirname(os.path.abspath(__file__))
# base_dir = os.path.dirname(current_dir) # 假設 createtable.py 在專案根目錄下的 database 或 utils 資料夾
# DB_PATH = os.path.join(base_dir, "database", "tw_stock_data.db")
# 獲取 createtable.py 腳本所在的目錄
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 將資料庫檔案路徑定義在 SCRIPT_DIR 下
DB_PATH = os.path.join(SCRIPT_DIR, "tw_stock_data.db")

def create_tables(conn):
    cursor = conn.cursor()

    # 1. 公司基本資訊 (整合 stock_info 和 company_info)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS company_info (
        stock_id TEXT PRIMARY KEY,
        name TEXT,                      -- 公司簡稱 (來自 news_collector, watchlist_routes)
        full_name TEXT,                 -- 公司全名
        industry TEXT,                  -- 產業類別
        market TEXT,                    -- 上市/上櫃
        listed_date DATE,               -- 上市日期
        capital REAL,                   -- 股本 (億)
        outstanding_shares REAL,        -- 在外流通股數 (張)
        description TEXT,               -- 公司簡介 (可選)
        chairman TEXT,                  -- 董事長 (可選)
        ceo TEXT,                       -- 總經理 (可選)
        address TEXT,                   -- 公司地址 (可選)
        website TEXT,                   -- 公司網站 (可選)
        last_updated_time DATETIME      -- 最後更新時間
    )
    ''')
    print("Table 'company_info' created or already exists.")

    # 2. 每日股價 (寬表)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS daily_prices (
        stock_id TEXT,
        date DATE,
        open REAL,
        high REAL,
        low REAL,
        close REAL,
        adj_close REAL,                 -- 還原收盤價 (可選)
        volume INTEGER,                 -- 成交股數
        turnover REAL,                  -- 成交金額 (可選)
        change REAL,                    -- 漲跌價
        change_percent REAL,            -- 漲跌幅
        PRIMARY KEY (stock_id, date),
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id)
    )
    ''')
    print("Table 'daily_prices' created or already exists.")

    # 3. 每日技術指標 (寬表)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS daily_technical_indicators (
        stock_id TEXT,
        date DATE,
        MA5 REAL,
        MA10 REAL,
        MA20 REAL,
        MA60 REAL,
        MA120 REAL,                     -- 可根據需要增減
        MA240 REAL,                     -- 可根據需要增減
        RSI6 REAL,
        RSI12 REAL,                     -- 常用 RSI 週期
        RSI24 REAL,
        MACD_diff REAL,                 -- MACD快線 - MACD慢線
        MACD_dea REAL,                  -- MACD訊號線 (MACD_signal)
        MACD_hist REAL,                 -- MACD柱狀體 (MACD_diff - MACD_dea)
        KD_K REAL,
        KD_D REAL,
        BBANDS_upper REAL,              -- 布林通道上軌
        BBANDS_middle REAL,             -- 布林通道中軌
        BBANDS_lower REAL,              -- 布林通道下軌
        PE_ratio REAL,                  -- 本益比 (若每日更新)
        PB_ratio REAL,                  -- 股價淨值比 (若每日更新)
        Dividend_yield REAL,            -- 殖利率 (若每日更新)
        -- 可以根據需要添加更多指標欄位
        PRIMARY KEY (stock_id, date),
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id)
    )
    ''')
    print("Table 'daily_technical_indicators' created or already exists.")

    # 4. 財務報告 (來自 financial_collector.py 和 CoreDataDownloader 的 financial_statements)
    # 統一使用 financial_reports，並整合欄位
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS financial_reports (
        stock_id TEXT,
        report_date DATE,               -- 財報截止日期 (例如: 2023-03-31, 2023-06-30)
        report_type TEXT,               -- 財報類型 (例如: Q1, Q2, Q3, Q4, Annual)
        publish_date DATE,              -- 財報公布日期 (可選)
        currency TEXT DEFAULT 'TWD',    -- 幣別
        
        revenue REAL,                   -- 營業收入
        cost_of_goods_sold REAL,        -- 營業成本 (可選)
        gross_profit REAL,              -- 營業毛利
        operating_expenses REAL,        -- 營業費用 (可選)
        operating_income REAL,          -- 營業利益 (operating_profit)
        non_operating_income REAL,      -- 營業外收入及支出 (可選)
        income_before_tax REAL,         -- 稅前淨利 (可選)
        income_tax_expense REAL,        -- 所得稅費用 (可選)
        net_income REAL,                -- 稅後淨利 (net_profit)
        eps REAL,                       -- 每股盈餘 (基本)
        
        total_assets REAL,              -- 資產總額
        current_assets REAL,            -- 流動資產 (可選)
        non_current_assets REAL,        -- 非流動資產 (可選)
        total_liabilities REAL,         -- 負債總額
        current_liabilities REAL,       -- 流動負債 (可選)
        non_current_liabilities REAL,   -- 非流動負債 (可選)
        equity REAL,                    -- 權益總額 (股東權益)
        
        cash_flow_from_operating REAL,  -- 營業活動現金流量 (可選)
        cash_flow_from_investing REAL,  -- 投資活動現金流量 (可選)
        cash_flow_from_financing REAL,  -- 籌資活動現金流量 (可選)
        
        gross_profit_margin REAL,       -- 毛利率
        operating_profit_margin REAL,   -- 營業利益率
        net_profit_margin REAL,         -- 稅後淨利率
        return_on_equity REAL,          -- 股東權益報酬率 (ROE) (可選)
        debt_to_equity_ratio REAL,      -- 負債權益比 (可選)
        
        PRIMARY KEY (stock_id, report_date, report_type),
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id)
    )
    ''')
    print("Table 'financial_reports' created or already exists.")

    # 5. 股票新聞 (來自 news_collector.py, CoreDataDownloader)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS stock_news (
        news_id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        content TEXT,
        url TEXT UNIQUE,                -- 將 URL 設為 UNIQUE，避免重複新聞
        publish_datetime DATETIME,      -- 發布時間 (包含時分秒，更精確)
        source TEXT,                    -- 新聞來源
        fetch_datetime DATETIME DEFAULT CURRENT_TIMESTAMP -- 抓取時間
    )
    ''')
    # publish_date 被改為 publish_datetime 以儲存更精確的時間
    # fetch_date 被改為 fetch_datetime
    # 移除了 UNIQUE(url, publish_date) 因為 url 應該是主要唯一識別符
    print("Table 'stock_news' created or already exists.")

    # 6. 新聞與股票關聯表 (來自 news_collector.py, CoreDataDownloader)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS news_stock_relation (
        news_id INTEGER,
        stock_id TEXT,
        relevance REAL,                 -- 關聯度評分 (0-1)
        PRIMARY KEY (news_id, stock_id),
        FOREIGN KEY (news_id) REFERENCES stock_news(news_id) ON DELETE CASCADE,
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id) ON DELETE CASCADE
    )
    ''')
    print("Table 'news_stock_relation' created or already exists.")

    # 7. 新聞情緒分析表 (來自 news_collector.py, CoreDataDownloader)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS news_sentiment (
        news_id INTEGER PRIMARY KEY,
        sentiment_score REAL,           -- 情緒評分 (-1 負面, 0 中性, 1 正面)
        positive_score REAL,            -- 正面評分 (0-1) (可選，若有更細緻模型)
        negative_score REAL,            -- 負面評分 (0-1) (可選)
        neutral_score REAL,             -- 中性評分 (0-1) (可選)
        keywords TEXT,                  -- 關鍵字，以逗號分隔
        summary TEXT,                   -- 新聞摘要
        analyzed_datetime DATETIME DEFAULT CURRENT_TIMESTAMP, -- 分析時間
        FOREIGN KEY (news_id) REFERENCES stock_news(news_id) ON DELETE CASCADE
    )
    ''')
    print("Table 'news_sentiment' created or already exists.")

    # 8. 三大法人及主力買賣超 (來自 CoreDataDownloader 的 institutional_investors 和 price_model/stock_price_predictor 的 institutional_trading_details)
    # 整合為一張表，欄位可能需要根據實際能獲取的資料調整
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS institutional_trades (
        stock_id TEXT,
        date DATE,
        foreign_buy_shares INTEGER,         -- 外資買進股數
        foreign_sell_shares INTEGER,        -- 外資賣出股數
        foreign_net_shares INTEGER,         -- 外資買賣超股數
        foreign_buy_value REAL,             -- 外資買進金額 (可選)
        foreign_sell_value REAL,            -- 外資賣出金額 (可選)
        foreign_net_value REAL,             -- 外資買賣超金額
        
        investment_trust_buy_shares INTEGER, -- 投信買進股數
        investment_trust_sell_shares INTEGER,-- 投信賣出股數
        investment_trust_net_shares INTEGER, -- 投信買賣超股數
        investment_trust_net_value REAL,    -- 投信買賣超金額

        dealer_proprietary_buy_shares INTEGER, -- 自營商(自營)買進股數
        dealer_proprietary_sell_shares INTEGER,-- 自營商(自營)賣出股數
        dealer_proprietary_net_shares INTEGER, -- 自營商(自營)買賣超股數
        dealer_proprietary_net_value REAL,  -- 自營商(自營)買賣超金額
        
        dealer_hedge_buy_shares INTEGER,    -- 自營商(避險)買進股數 (可選)
        dealer_hedge_sell_shares INTEGER,   -- 自營商(避險)賣出股數 (可選)
        dealer_hedge_net_shares INTEGER,    -- 自營商(避險)買賣超股數 (可選)
        dealer_hedge_net_value REAL,        -- 自營商(避險)買賣超金額 (可選)
        
        total_institutional_net_shares INTEGER, -- 三大法人合計買賣超股數
        total_institutional_net_value REAL, -- 三大法人合計買賣超金額
        
        margin_balance_shares INTEGER,      -- 融資餘額(股)
        margin_change_shares INTEGER,       -- 融資增減(股)
        short_balance_shares INTEGER,       -- 融券餘額(股)
        short_change_shares INTEGER,        -- 融券增減(股)
        margin_short_ratio REAL,            -- 券資比 (%) (可選)
        
        PRIMARY KEY (stock_id, date),
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id)
    )
    ''')
    # 'institutional_investors' 和 'institutional_trading_details' 被整合成 'institutional_trades'
    # 也整合了融資融券資料 (來自 stock_price_predictor.py 的 load_margin_data)
    print("Table 'institutional_trades' created or already exists.")

    # 9. 股東權益與股利分配 (來自 financial_collector.py)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS dividend_and_equity (
        stock_id TEXT,
        record_date DATE,               -- 記錄日期 (可以是財報日或除權息日)
        item_type TEXT,                 -- 項目類型 (例如: 'EquityInfo', 'Dividend')
        
        -- 股東權益相關 (當 item_type = 'EquityInfo')
        total_shareholders INTEGER,     -- 總股東人數
        foreign_ownership_ratio REAL,   -- 外資持股比例
        trust_ownership_ratio REAL,     -- 投信持股比例
        
        -- 股利分配相關 (當 item_type = 'Dividend')
        fiscal_year INTEGER,            -- 股利所屬年度
        cash_dividend_per_share REAL,   -- 每股現金股利
        stock_dividend_per_share REAL,  -- 每股股票股利 (來自盈餘)
        stock_dividend_from_capital_reserve_per_share REAL, -- 每股股票股利 (來自公積) (可選)
        total_dividend_per_share REAL,  -- 合計股利
        ex_dividend_date DATE,          -- 除息日 (可選)
        ex_right_date DATE,             -- 除權日 (可選)
        dividend_payment_date DATE,     -- 現金股利發放日 (可選)
        
        PRIMARY KEY (stock_id, record_date, item_type),
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id)
    )
    ''')
    # 'shareholders_equity' 和 'dividend_distribution' 被整合成 'dividend_and_equity'
    print("Table 'dividend_and_equity' created or already exists.")

    # 10. 市場指數歷史 (來自 CoreDataDownloader)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS market_index_history (
        index_code TEXT,                -- 指數代碼 (例如: 'TWII', 'TPEX')
        date DATE,
        open REAL,                      -- (可選)
        high REAL,                      -- (可選)
        low REAL,                       -- (可選)
        close REAL,
        volume REAL,                    -- (可選, 例如成交金額)
        PRIMARY KEY (index_code, date)
    )
    ''')
    print("Table 'market_index_history' created or already exists.")

    # 11. 股票類別 (來自 CoreDataDownloader 的 stock_categories)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS stock_categories (
        category_name TEXT,             -- 類別名稱 (例如: '水泥工業', '半導體業')
        stock_id TEXT,
        source TEXT,                    -- 資料來源 (例如: 'FinLab', 'TWSE')
        PRIMARY KEY (category_name, stock_id),
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id)
    )
    ''')
    print("Table 'stock_categories' created or already exists.")

    # 12. 使用者觀察清單主表 (來自 watchlist_routes.py)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user_watchlists (
        watchlist_id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT,                   -- 若有多使用者系統，否則可省略或預設
        name TEXT NOT NULL UNIQUE,      -- 清單名稱
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_default BOOLEAN DEFAULT 0    -- 是否為預設清單
    )
    ''')
    print("Table 'user_watchlists' created or already exists.")

    # 13. 使用者觀察清單股票表 (來自 watchlist_routes.py，原 'watchlist' 表)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user_watchlist_stocks (
        item_id INTEGER PRIMARY KEY AUTOINCREMENT,
        watchlist_id INTEGER,
        stock_id TEXT NOT NULL,
        notes TEXT,
        added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (watchlist_id) REFERENCES user_watchlists(watchlist_id) ON DELETE CASCADE,
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id) ON DELETE CASCADE,
        UNIQUE (watchlist_id, stock_id) -- 同一個清單內股票不重複
    )
    ''')
    print("Table 'user_watchlist_stocks' created or already exists.")

    # 14. 股票預測結果儲存表 (來自 watchlist_routes.py 間接需求)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS stock_predictions_log (
        prediction_id INTEGER PRIMARY KEY AUTOINCREMENT,
        stock_id TEXT NOT NULL,
        prediction_datetime DATETIME DEFAULT CURRENT_TIMESTAMP, -- 執行預測的時間
        model_type TEXT,                -- 使用的模型類型 (e.g., 'combined', 'price_only')
        target_date DATE,               -- 預測的目標日期
        prediction_days INTEGER,        -- 預測未來幾天
        predicted_direction TEXT,       -- 'up' or 'down'
        predicted_probability REAL,     -- 預測機率
        actual_outcome TEXT,            -- 實際結果 (之後回填, 'up', 'down', 'neutral') (可選)
        current_price_at_prediction REAL, -- 預測時的股價
        FOREIGN KEY (stock_id) REFERENCES company_info(stock_id)
    )
    ''')
    print("Table 'stock_predictions_log' created or already exists.")

    conn.commit()
    print("All tables checked/created successfully.")

def main(db_path_from_config: str): # 修改這裡，接受傳入的路徑
    # 確保 database 目錄存在
    db_dir = os.path.dirname(db_path_from_config)
    if db_dir and not os.path.exists(db_dir):
        try:
            os.makedirs(db_dir)
            print(f"Database directory '{db_dir}' created by createtable.")
        except OSError as e:
            print(f"Error creating directory {db_dir}: {e}")
            return # 如果目錄創建失敗，可能無法繼續

    print(f"Attempting to connect to database at: {db_path_from_config}")
    try:
        conn = sqlite3.connect(db_path_from_config)
        print(f"Successfully connected to database at: {db_path_from_config}")
        try:
            create_tables(conn) # create_tables 函數保持不變
        finally:
            conn.close()
            print(f"Database connection to '{db_path_from_config}' closed.")
    except sqlite3.Error as e:
        print(f"Error connecting to or creating database at {db_path_from_config}: {e}")


if __name__ == "__main__":
    # 當此腳本被直接執行時，需要一種方式來獲取 db_path
    # 這裡可以保持一個預設行為，例如從固定的相對位置讀取設定檔
    # 但更推薦的做法是，這個腳本主要被其他主程式 (如 main.py) 調用其 main 函數
    print("createtable.py is intended to be called by a main application script with a db_path argument.")
    # 例如，如果您真的想單獨執行它來初始化資料庫：
    # temp_project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # main-news/
    # sys.path.insert(0, temp_project_root)
    # from utils.config import ConfigManager
    # temp_config_manager = ConfigManager(config_dir='config') # 指定設定檔目錄相對於根目錄
    # temp_app_config = temp_config_manager.load_config('app_config.json')
    # configured_db_path = temp_config_manager.get_config_value(temp_app_config, 'database.path')
    # if configured_db_path:
    #     main(configured_db_path)
    # else:
    #     print("Error: Could not determine database path from config.")
    pass