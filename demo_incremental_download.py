#!/usr/bin/env python3
"""
增量下載功能演示腳本
展示智能增量下載的核心功能
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime, timedelta

# 將專案根目錄添加到 Python 路徑
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, PROJECT_ROOT)

def analyze_database_gaps():
    """分析資料庫中的數據缺口"""
    db_path = os.path.join(PROJECT_ROOT, "database", "tw_stock_data.db")
    
    if not os.path.exists(db_path):
        print("❌ 資料庫文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        
        print("🔍 分析資料庫數據缺口...")
        
        # 獲取基本統計
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM daily_prices")
        stock_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM daily_prices")
        total_records = cursor.fetchone()[0]
        
        print(f"📊 基本統計:")
        print(f"   股票數量: {stock_count} 支")
        print(f"   總記錄數: {total_records:,} 筆")
        
        # 分析每支股票的數據完整性
        cursor.execute("""
            SELECT stock_id, COUNT(*) as records, MIN(date) as start_date, MAX(date) as end_date
            FROM daily_prices 
            GROUP BY stock_id 
            ORDER BY records ASC
            LIMIT 20
        """)
        
        incomplete_stocks = cursor.fetchall()
        
        print(f"\n📈 數據最少的20支股票:")
        for stock_id, records, start_date, end_date in incomplete_stocks:
            print(f"   {stock_id}: {records} 筆記錄 ({start_date} ~ {end_date})")
        
        # 分析特定股票的缺失日期
        test_stock = "2330"
        print(f"\n🔍 分析股票 {test_stock} 的數據缺口:")
        
        gaps = find_missing_dates(conn, test_stock)
        if gaps:
            print(f"   發現 {len(gaps)} 個缺失日期範圍:")
            for i, (start, end) in enumerate(gaps[:10]):  # 只顯示前10個
                days = (end - start).days + 1
                print(f"   {i+1}. {start} ~ {end} ({days} 天)")
            if len(gaps) > 10:
                print(f"   ... 還有 {len(gaps) - 10} 個範圍")
        else:
            print(f"   ✅ 股票 {test_stock} 數據完整")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析過程發生錯誤: {e}")

def find_missing_dates(conn, stock_id):
    """找出特定股票的缺失日期範圍"""
    try:
        # 獲取該股票的所有日期
        cursor = conn.cursor()
        cursor.execute(
            "SELECT date FROM daily_prices WHERE stock_id = ? ORDER BY date",
            (stock_id,)
        )
        
        existing_dates = [row[0] for row in cursor.fetchall()]
        if not existing_dates:
            return []
        
        # 轉換為日期對象
        existing_dates = [pd.to_datetime(date).date() for date in existing_dates]
        existing_dates_set = set(existing_dates)
        
        # 生成完整的工作日範圍
        start_date = min(existing_dates)
        end_date = max(existing_dates)
        
        # 生成工作日序列
        all_business_days = pd.bdate_range(start=start_date, end=end_date)
        all_business_days_set = set(all_business_days.date)
        
        # 找出缺失的日期
        missing_dates = sorted(all_business_days_set - existing_dates_set)
        
        if not missing_dates:
            return []
        
        # 將連續的缺失日期合併為範圍
        ranges = []
        range_start = missing_dates[0]
        range_end = missing_dates[0]
        
        for i in range(1, len(missing_dates)):
            current_date = missing_dates[i]
            prev_date = missing_dates[i-1]
            
            # 如果日期連續（考慮週末），繼續當前範圍
            if (current_date - prev_date).days <= 3:
                range_end = current_date
            else:
                # 開始新範圍
                ranges.append((range_start, range_end))
                range_start = current_date
                range_end = current_date
        
        # 添加最後一個範圍
        ranges.append((range_start, range_end))
        
        return ranges
        
    except Exception as e:
        print(f"❌ 分析股票 {stock_id} 缺失日期時發生錯誤: {e}")
        return []

def demonstrate_incremental_logic():
    """演示增量下載邏輯"""
    print("\n🚀 增量下載邏輯演示:")
    print("\n1. 檢測現有數據範圍")
    print("   - 掃描資料庫中每支股票的日期範圍")
    print("   - 識別數據記錄較少的股票")
    
    print("\n2. 生成完整日期序列")
    print("   - 基於工作日生成完整的日期序列")
    print("   - 排除週末和假日")
    
    print("\n3. 計算缺失範圍")
    print("   - 比較現有日期與完整序列")
    print("   - 將連續缺失日期合併為下載範圍")
    
    print("\n4. 智能下載策略")
    print("   - 優先處理數據最不完整的股票")
    print("   - 批量下載缺失範圍的數據")
    print("   - 避免重複下載已存在的數據")
    
    print("\n5. 狀態監控")
    print("   - 下載前後狀態對比")
    print("   - 提供詳細的進度報告")

def show_optimization_benefits():
    """展示優化效益"""
    print("\n💡 增量下載的優勢:")
    
    print("\n🎯 精準性:")
    print("   - 只下載真正缺失的數據")
    print("   - 避免重複下載浪費時間")
    print("   - 智能識別數據缺口")
    
    print("\n⚡ 效率:")
    print("   - 大幅減少下載時間")
    print("   - 降低API請求壓力")
    print("   - 優化資源使用")
    
    print("\n🛡️ 可靠性:")
    print("   - 自動錯誤恢復")
    print("   - 數據完整性檢查")
    print("   - 狀態持續監控")
    
    print("\n📊 智能化:")
    print("   - 自動分析資料庫狀態")
    print("   - 提供個性化建議")
    print("   - 適應不同使用場景")

def main():
    """主演示函數"""
    print("🔄 股票數據增量下載功能演示")
    print("=" * 50)
    
    # 分析當前資料庫狀態
    analyze_database_gaps()
    
    # 演示增量下載邏輯
    demonstrate_incremental_logic()
    
    # 展示優化效益
    show_optimization_benefits()
    
    print("\n" + "=" * 50)
    print("📖 實際使用命令:")
    print("\n1. 檢查數據狀態:")
    print("   python main.py --check-missing")
    
    print("\n2. 智能增量下載:")
    print("   python main.py --download-finlab")
    
    print("\n3. 指定股票補充:")
    print("   python main.py --download-finlab --stock-ids 2330 2317")
    
    print("\n4. 完整狀態檢查:")
    print("   python test_fast_download.py")
    
    print("\n✨ 演示完成!")

if __name__ == "__main__":
    main()
