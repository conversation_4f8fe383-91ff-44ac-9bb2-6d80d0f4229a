# Docker Compose 配置文件
# 台灣股票預測系統完整部署配置

version: '3.8'

services:
  # 主應用服務
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tw-stock-web
    ports:
      - "8889:8889"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///data/stock_data.db
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
      - db
    networks:
      - stock-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8889/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis 緩存服務
  redis:
    image: redis:7-alpine
    container_name: tw-stock-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - stock-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL 資料庫服務（可選，用於生產環境）
  db:
    image: postgres:15-alpine
    container_name: tw-stock-db
    environment:
      POSTGRES_DB: stock_prediction
      POSTGRES_USER: stock_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-stock_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - stock-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U stock_user -d stock_prediction"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理（可選）
  nginx:
    image: nginx:alpine
    container_name: tw-stock-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
    depends_on:
      - web
    networks:
      - stock-network
    restart: unless-stopped
    profiles:
      - production

  # 數據收集服務
  data-collector:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tw-stock-collector
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///data/stock_data.db
      - LOG_LEVEL=INFO
      - FINLAB_API_KEY=${FINLAB_API_KEY}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    command: python download_twii_index.py
    depends_on:
      - db
      - redis
    networks:
      - stock-network
    restart: "no"
    profiles:
      - data-collection

  # 新聞爬蟲服務
  news-crawler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tw-stock-crawler
    environment:
      - DATABASE_URL=sqlite:///data/stock_data.db
      - LOG_LEVEL=INFO
      - CRAWLER_DELAY=5
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    command: python news_crawler.py
    depends_on:
      - db
      - redis
    networks:
      - stock-network
    restart: "no"
    profiles:
      - news-collection

  # 監控服務（可選）
  prometheus:
    image: prom/prometheus:latest
    container_name: tw-stock-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - stock-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana 儀表板（可選）
  grafana:
    image: grafana/grafana:latest
    container_name: tw-stock-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - stock-network
    restart: unless-stopped
    profiles:
      - monitoring

# 網絡配置
networks:
  stock-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 數據卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local