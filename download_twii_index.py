#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台灣加權指數數據下載腳本
用於下載真正的台灣加權指數數據到 market_index_history 表
"""

import logging
import os
import sys
from datetime import datetime

# 添加項目根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_downloader import CoreDataDownloader

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("twii_download.log"), logging.StreamHandler()],
)

logger = logging.getLogger("TWIIDownloader")


def download_twii_data(api_key=None):
    """
    下載台灣加權指數數據

    Args:
        api_key: FinLab API 金鑰，如果為 None 則從環境變數讀取

    Returns:
        bool: 下載是否成功
    """
    try:
        # 獲取 API 金鑰
        if not api_key:
            api_key = os.getenv("FINLAB_API_KEY")
            if not api_key:
                logger.error("請設置 FINLAB_API_KEY 環境變數或提供 API 金鑰")
                logger.info("使用方法:")
                logger.info("1. 設置環境變數: export FINLAB_API_KEY='your_api_key'")
                logger.info(
                    "2. 或直接在腳本中提供: python download_twii_index.py --api-key your_api_key"
                )
                return False

        # 設置資料庫路徑
        database_path = os.path.join(os.path.dirname(__file__), "tw_stock_data.db")
        logger.info(f"使用資料庫: {database_path}")

        # 初始化下載器
        logger.info("初始化 CoreDataDownloader...")
        downloader = CoreDataDownloader(database_path, api_key)

        # 測試 API 金鑰
        logger.info("測試 API 金鑰有效性...")
        if not downloader.test_api_key():
            logger.error("API 金鑰無效，請檢查金鑰是否正確")
            return False

        logger.info("API 金鑰驗證成功")

        # 創建 market_index_history 表（如果不存在）
        logger.info("確保 market_index_history 表存在...")
        downloader._create_market_index_table()

        # 下載台灣加權指數數據
        logger.info("開始下載台灣加權指數數據...")
        success = downloader.download_market_index_data(
            index_finlab_id="benchmark_asset:收盤價",  # FinLab 的台灣加權指數 ID
            db_index_code="TWII",  # 資料庫中的指數代碼
        )

        if success:
            logger.info("✅ 台灣加權指數數據下載成功！")

            # 驗證數據
            logger.info("驗證下載的數據...")
            verify_data(database_path)

            return True
        else:
            logger.error("❌ 台灣加權指數數據下載失敗")
            return False

    except Exception as e:
        logger.error(f"下載過程中發生錯誤: {e}")
        import traceback

        logger.error(traceback.format_exc())
        return False


def verify_data(database_path):
    """
    驗證下載的數據

    Args:
        database_path: 資料庫路徑
    """
    try:
        import sqlite3

        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # 查詢總行數
        cursor.execute("SELECT COUNT(*) FROM market_index_history WHERE index_code = 'TWII'")
        total_count = cursor.fetchone()[0]
        logger.info(f"資料庫中 TWII 數據總數: {total_count} 筆")

        if total_count > 0:
            # 查詢最新的 5 筆數據
            cursor.execute(
                """
                SELECT date, close_price 
                FROM market_index_history 
                WHERE index_code = 'TWII' 
                ORDER BY date DESC 
                LIMIT 5
            """
            )

            recent_data = cursor.fetchall()
            logger.info("最新 5 筆台灣加權指數數據:")
            for date, price in recent_data:
                logger.info(f"  {date}: {price:,.2f}")
        else:
            logger.warning("未找到 TWII 數據")

        conn.close()

    except Exception as e:
        logger.error(f"驗證數據時發生錯誤: {e}")


def main():
    """
    主函數
    """
    import argparse

    parser = argparse.ArgumentParser(description="下載台灣加權指數數據")
    parser.add_argument("--api-key", help="FinLab API 金鑰")
    parser.add_argument("--verify-only", action="store_true", help="僅驗證現有數據，不下載")

    args = parser.parse_args()

    if args.verify_only:
        # 僅驗證數據
        database_path = os.path.join(os.path.dirname(__file__), "tw_stock_data.db")
        logger.info("驗證現有數據...")
        verify_data(database_path)
        return

    # 下載數據
    logger.info("=" * 50)
    logger.info("台灣加權指數數據下載器")
    logger.info("=" * 50)

    success = download_twii_data(args.api_key)

    if success:
        logger.info("\n🎉 下載完成！現在您的網站應該能顯示真正的台灣加權指數了。")
        logger.info("請重新啟動 web_app.py 來查看效果。")
    else:
        logger.error("\n❌ 下載失敗，請檢查錯誤訊息並重試。")
        sys.exit(1)


if __name__ == "__main__":
    main()
