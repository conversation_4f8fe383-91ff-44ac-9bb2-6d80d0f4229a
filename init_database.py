#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據庫初始化腳本
用於創建所有必要的資料表和初始化資料庫結構
"""

import os
import sqlite3
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.createtable import create_tables
from utils.config import ConfigManager
from utils.logger import setup_logger


def create_database():
    """
    創建並初始化資料庫
    """
    # 設置日誌
    logger = setup_logger("DatabaseInit")

    try:
        # 載入配置
        config_manager = ConfigManager()
        config = config_manager.get_config()

        # 獲取資料庫路徑
        db_path = config.get("database", {}).get("path", "database/tw_stock_data.db")

        # 確保資料庫目錄存在
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            logger.info(f"創建資料庫目錄: {db_dir}")

        # 連接資料庫
        logger.info(f"初始化資料庫: {db_path}")
        conn = sqlite3.connect(db_path)

        # 創建資料表
        create_tables(conn)

        # 關閉連接
        conn.close()

        logger.info("資料庫初始化完成！")
        print("✅ 資料庫初始化成功！")
        print(f"📁 資料庫位置: {os.path.abspath(db_path)}")

        return True

    except Exception as e:
        logger.error(f"資料庫初始化失敗: {e}")
        print(f"❌ 資料庫初始化失敗: {e}")
        return False


def check_database_status():
    """
    檢查資料庫狀態
    """
    try:
        # 載入配置
        config_manager = ConfigManager()
        config = config_manager.get_config()

        # 獲取資料庫路徑
        db_path = config.get("database", {}).get("path", "database/tw_stock_data.db")

        if not os.path.exists(db_path):
            print(f"❌ 資料庫文件不存在: {db_path}")
            return False

        # 連接資料庫並檢查表格
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 獲取所有表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()

        print(f"📊 資料庫狀態檢查")
        print(f"📁 資料庫位置: {os.path.abspath(db_path)}")
        print(f"📋 資料表數量: {len(tables)}")

        if tables:
            print("\n📋 現有資料表:")
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  - {table_name}: {count} 筆記錄")
        else:
            print("⚠️  未找到任何資料表")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ 檢查資料庫狀態時發生錯誤: {e}")
        return False


def main():
    """
    主函數
    """
    import argparse

    parser = argparse.ArgumentParser(description="資料庫初始化工具")
    parser.add_argument("--check", action="store_true", help="檢查資料庫狀態")
    parser.add_argument("--force", action="store_true", help="強制重新創建資料庫")

    args = parser.parse_args()

    if args.check:
        check_database_status()
    else:
        # 檢查是否需要強制重新創建
        if args.force:
            print("🔄 強制重新創建資料庫...")

        # 創建資料庫
        success = create_database()

        if success:
            print("\n🎉 資料庫初始化完成！")
            print("\n📝 接下來您可以：")
            print("1. 下載股票數據: python main.py --download-all")
            print("2. 更新最新數據: python main.py --update-data")
            print("3. 分析特定股票: python main.py --analyze --stock-id 2330")
        else:
            print("\n❌ 初始化失敗，請檢查錯誤訊息")
            sys.exit(1)


if __name__ == "__main__":
    main()
