[2025-06-21 00:18:13] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 00:18:14] INFO: 数据库表结构初始化完成
[2025-06-21 00:18:14] INFO: Web应用启动于 http://localhost:8888
[2025-06-21 00:20:11] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 00:20:12] INFO: 数据库表结构初始化完成
[2025-06-21 00:20:12] INFO: Web应用启动于 http://localhost:8888
[2025-06-21 00:20:15] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 00:20:15] INFO: 数据库表结构初始化完成
[2025-06-21 00:20:15] INFO: Web应用启动于 http://localhost:8888
[2025-06-21 11:18:42] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 11:19:15] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 11:19:46] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 11:20:16] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 11:21:19] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 22:55:23] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 22:56:05] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 22:56:20] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 22:57:10] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 22:59:40] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 22:59:41] INFO: 数据库表结构初始化完成
[2025-06-21 22:59:41] INFO: Web应用启动于 http://localhost:8888
[2025-06-21 22:59:42] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 22:59:42] INFO: 数据库表结构初始化完成
[2025-06-21 22:59:42] INFO: Web应用启动于 http://localhost:8888
[2025-06-21 23:00:23] WARNING: 404错误: /apple-touch-icon-precomposed.png
[2025-06-21 23:00:23] WARNING: 404错误: /apple-touch-icon.png
[2025-06-21 23:23:20] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:23:21] INFO: 数据库表结构初始化完成
[2025-06-21 23:23:21] INFO: Web应用启动于 http://localhost:8888
[2025-06-21 23:23:21] INFO: 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:23:22] INFO: 数据库表结构初始化完成
[2025-06-21 23:23:22] INFO: Web应用启动于 http://localhost:8888
[2025-06-21 23:33:03] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:33:03] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:33:03] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:33:03] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:33:41] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:33:41] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:33:41] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:33:41] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:33:41] INFO: 開始更新股票數據...
[2025-06-21 23:33:42] ERROR: 更新股票數據時發生錯誤: 'DataCollectionManager' object has no attribute 'logger'
[2025-06-21 23:33:42] INFO: 正在關閉應用程式資源...
[2025-06-21 23:33:42] ERROR: 關閉資源時發生錯誤: 'DataCollectionManager' object has no attribute 'logger'
[2025-06-21 23:34:04] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:34:04] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:34:04] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:34:04] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:34:04] INFO: 開始更新股票數據...
[2025-06-21 23:34:27] INFO: 股票數據更新完成
[2025-06-21 23:34:27] INFO: 正在關閉應用程式資源...
[2025-06-21 23:34:27] INFO: 應用程式資源已關閉。
[2025-06-21 23:34:39] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:34:39] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:34:39] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:34:39] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:34:40] ERROR: 分析股票 2330 時發生錯誤: Execution failed on sql '
        SELECT date, open, high, low, close, volume
        FROM price_close
        WHERE stock_id = '2330'
        ORDER BY date DESC
        LIMIT 365
        ': no such table: price_close
[2025-06-21 23:34:40] INFO: 正在關閉應用程式資源...
[2025-06-21 23:34:40] INFO: 應用程式資源已關閉。
[2025-06-21 23:35:19] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:35:19] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:35:19] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:35:19] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:35:22] ERROR: 分析股票 2330 時發生錯誤: Execution failed on sql '
        SELECT date, open, high, low, close, volume
        FROM daily_prices
        WHERE stock_id = '2330'
        ORDER BY date DESC
        LIMIT 365
        ': no such table: daily_prices
[2025-06-21 23:35:22] INFO: 正在關閉應用程式資源...
[2025-06-21 23:35:22] INFO: 應用程式資源已關閉。
[2025-06-21 23:36:13] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:36:13] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:36:13] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:36:13] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:36:15] ERROR: 分析股票 2330 時發生錯誤: Execution failed on sql '
        SELECT 
            pc.date,
            po.value as open,
            ph.value as high,
            pl.value as low,
            pc.value as close,
            pv.value as volume
        FROM price_close pc
        LEFT JOIN price_open po ON pc.stock_id = po.stock_id AND pc.date = po.date
        LEFT JOIN price_high ph ON pc.stock_id = ph.stock_id AND pc.date = ph.date
        LEFT JOIN price_low pl ON pc.stock_id = pl.stock_id AND pc.date = pl.date
        LEFT JOIN price_volume pv ON pc.stock_id = pv.stock_id AND pc.date = pv.date
        WHERE pc.stock_id = '2330'
        ORDER BY pc.date DESC
        LIMIT 365
        ': no such table: price_close
[2025-06-21 23:36:15] INFO: 正在關閉應用程式資源...
[2025-06-21 23:36:15] INFO: 應用程式資源已關閉。
[2025-06-21 23:36:54] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:36:54] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:36:54] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:36:54] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:36:56] ERROR: 分析股票 2330 時發生錯誤: 'NewsAnalyzer' object has no attribute 'get_sentiment_summary'
[2025-06-21 23:36:56] INFO: 正在關閉應用程式資源...
[2025-06-21 23:36:56] INFO: 應用程式資源已關閉。
[2025-06-21 23:37:21] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:37:21] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:37:21] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:37:21] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:37:23] ERROR: 分析股票 2330 時發生錯誤: Execution failed on sql '
        SELECT n.id, n.title, n.publish_date, n.source, 
               s.sentiment_score, s.positive_score, s.negative_score, s.neutral_score, s.keywords,
               r.relevance
        FROM stock_news n
        JOIN news_stock_relation r ON n.id = r.news_id
        JOIN news_sentiment s ON n.id = s.news_id
        WHERE r.stock_id = ?
        AND n.publish_date >= date('now', '-30 days')
        ORDER BY n.publish_date DESC, r.relevance DESC
        ': no such column: n.id
[2025-06-21 23:37:23] INFO: 正在關閉應用程式資源...
[2025-06-21 23:37:23] INFO: 應用程式資源已關閉。
[2025-06-21 23:37:48] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:37:48] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:37:48] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:37:48] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:37:51] ERROR: 分析股票 2330 時發生錯誤: Execution failed on sql '
        SELECT *
        FROM financial_reports
        WHERE stock_id = ?
        AND report_type = ?
        ORDER BY report_date DESC
        LIMIT 8  # 最近8個季度/年度報告
        ': unrecognized token: "#"
[2025-06-21 23:37:51] INFO: 正在關閉應用程式資源...
[2025-06-21 23:37:51] INFO: 應用程式資源已關閉。
[2025-06-21 23:40:10] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:10] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:40:10] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:10] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:12] ERROR: 主程式執行時發生嚴重錯誤: name 'json' is not defined
Traceback (most recent call last):
  File "/Users/<USER>/python/training/stock/main-news/main.py", line 279, in main
    print(json.dumps(result, indent=4, ensure_ascii=False)) # 使用 json.dumps 進行格式化輸出
          ^^^^
NameError: name 'json' is not defined

[2025-06-21 23:40:12] INFO: 正在關閉應用程式資源...
[2025-06-21 23:40:12] INFO: 應用程式資源已關閉。
[2025-06-21 23:40:32] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:32] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:40:32] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:32] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:33] ERROR: 主程式執行時發生嚴重錯誤: Object of type DataFrame is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/python/training/stock/main-news/main.py", line 280, in main
    print(json.dumps(result, indent=4, ensure_ascii=False)) # 使用 json.dumps 進行格式化輸出
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.11/json/encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.11/json/encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "/opt/anaconda3/lib/python3.11/json/encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "/opt/anaconda3/lib/python3.11/json/encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "/opt/anaconda3/lib/python3.11/json/encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type DataFrame is not JSON serializable

[2025-06-21 23:40:33] INFO: 正在關閉應用程式資源...
[2025-06-21 23:40:33] INFO: 應用程式資源已關閉。
[2025-06-21 23:40:52] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:52] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:40:52] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:52] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:40:54] INFO: 正在關閉應用程式資源...
[2025-06-21 23:40:54] INFO: 應用程式資源已關閉。
[2025-06-21 23:46:39] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:46:39] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:46:39] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:46:39] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:46:39] INFO: 開始下載15年歷史數據
[2025-06-21 23:46:39] INFO: 將下載指定的 1 支股票的歷史數據
[2025-06-21 23:46:39] INFO: 下載日期範圍: 2010-06-25 到 2025-06-21
[2025-06-21 23:46:39] INFO: 正在下載 2330 的歷史數據 (1/1)
[2025-06-21 23:46:41] WARNING: 股票 2330 沒有獲取到歷史數據
[2025-06-21 23:46:41] INFO: 歷史數據下載完成，成功: 0/1
[2025-06-21 23:46:41] INFO: 正在關閉應用程式資源...
[2025-06-21 23:46:41] INFO: 應用程式資源已關閉。
[2025-06-21 23:47:18] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:47:18] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:47:18] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:47:18] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:47:18] INFO: 開始下載15年歷史數據
[2025-06-21 23:47:18] INFO: 將下載指定的 1 支股票的歷史數據
[2025-06-21 23:47:18] INFO: 下載日期範圍: 2010-06-25 到 2025-06-21
[2025-06-21 23:47:18] INFO: 正在下載 2330 的歷史數據 (1/1)
[2025-06-21 23:47:43] WARNING: 股票 2330 沒有獲取到歷史數據
[2025-06-21 23:47:43] INFO: 歷史數據下載完成，成功: 0/1
[2025-06-21 23:47:43] INFO: 正在關閉應用程式資源...
[2025-06-21 23:47:43] INFO: 應用程式資源已關閉。
[2025-06-21 23:48:33] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:48:33] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:48:33] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:48:33] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:48:33] INFO: 開始下載15年歷史數據
[2025-06-21 23:48:33] INFO: 將下載指定的 1 支股票的歷史數據
[2025-06-21 23:48:33] INFO: 下載日期範圍: 2010-06-25 到 2025-06-21
[2025-06-21 23:48:33] INFO: 正在下載 2330 的歷史數據 (1/1)
[2025-06-21 23:49:31] INFO: 正在關閉應用程式資源...
[2025-06-21 23:49:32] INFO: 應用程式資源已關閉。
[2025-06-21 23:51:08] INFO: 從設定檔讀取的資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:51:08] INFO: 從設定檔讀取的日誌目錄: /Users/<USER>/python/training/stock/main-news/logs
[2025-06-21 23:51:08] INFO: 準備初始化資料庫結構於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:51:08] INFO: 資料庫結構檢查/創建完成於: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
[2025-06-21 23:51:08] INFO: 開始下載15年歷史數據
[2025-06-21 23:51:08] INFO: 將下載指定的 1 支股票的歷史數據
[2025-06-21 23:51:08] INFO: 下載日期範圍: 2010-06-25 到 2025-06-21
[2025-06-21 23:51:08] INFO: 正在下載 2330 的歷史數據 (1/1)
