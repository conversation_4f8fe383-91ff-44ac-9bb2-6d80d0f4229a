2025-04-16 12:15:02,348 - StockAnalysisApp - INFO: 資料庫檔案 tw_stock_data.db 不存在，正在創建...
2025-04-16 12:15:02,348 - StockAnalysisApp - INFO: 已成功創建資料庫檔案 tw_stock_data.db
2025-04-16 12:21:00,617 - StockAnalysisApp - INFO: 資料庫檔案 tw_stock_data.db 不存在，正在創建...
2025-04-16 12:21:00,617 - StockAnalysisApp - INFO: 已成功創建資料庫檔案 tw_stock_data.db
2025-04-16 19:50:35,153 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 19:50:35,652 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: Expecting value: line 1 column 1 (char 0)
2025-04-16 19:50:35,936 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: Expecting value: line 1 column 1 (char 0)
2025-04-16 19:50:35,936 - NewsCollector - INFO: 開始從證交所收集新聞...
2025-04-16 19:50:36,231 - NewsCollector - INFO: 成功從證交所收集 0 則新聞
2025-04-16 19:50:36,231 - NewsCollector - INFO: 開始從經濟日報收集新聞...
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 19:50:36,989 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/5603/8679169?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/5603/8679169?from=edn_newest_index?
2025-04-16 19:50:36,991 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:36,992 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/5603/8679169?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/5603/8679169?from=edn_newest_index?
2025-04-16 19:50:36,992 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:36,992 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/5599/8679168?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/5599/8679168?from=edn_newest_index?
2025-04-16 19:50:36,992 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:36,993 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/122328/8679144?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/122328/8679144?from=edn_newest_index?
2025-04-16 19:50:36,993 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:36,994 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/122328/8679149?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/122328/8679149?from=edn_newest_index?
2025-04-16 19:50:36,995 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:36,995 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/7307/8679146?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/7307/8679146?from=edn_newest_index?
2025-04-16 19:50:37,000 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:37,297 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:37,586 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:37,863 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,185 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,489 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,795 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,796 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 19:50:38,797 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,797 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 19:50:38,797 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,798 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 19:50:38,798 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,799 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 19:50:38,799 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,799 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 19:50:38,800 - NewsCollector - ERROR: 分析新聞中提到的股票時發生錯誤: Execution failed on sql 'SELECT stock_id, 公司簡稱 FROM company_info': no such table: company_info
2025-04-16 19:50:38,800 - NewsCollector - INFO: 成功從經濟日報收集 17 則新聞
2025-04-16 19:50:38,800 - NewsCollector - INFO: 總共收集 11 則唯一新聞
2025-04-16 19:50:38,800 - StockAnalysisApp - ERROR: 更新股票數據時發生錯誤: name 'datetime' is not defined
2025-04-16 19:51:08,847 - StockAnalysisApp - ERROR: 分析股票 2330 時發生錯誤: name 'datetime' is not defined
2025-04-16 20:00:49,352 - StockAnalysisApp - ERROR: 分析股票 2330 時發生錯誤: name 'datetime' is not defined
2025-04-16 20:00:54,849 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 20:00:55,996 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: Expecting value: line 1 column 1 (char 0)
2025-04-16 20:00:56,235 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: Expecting value: line 1 column 1 (char 0)
2025-04-16 20:00:56,235 - NewsCollector - INFO: 開始從證交所收集新聞...
2025-04-16 20:00:56,541 - NewsCollector - INFO: 成功從證交所收集 0 則新聞
2025-04-16 20:00:56,541 - NewsCollector - INFO: 開始從經濟日報收集新聞...
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,569 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 20:00:57,570 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/6710/8679204?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/6710/8679204?from=edn_newest_index?
2025-04-16 20:00:57,573 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/6710/8679204?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/6710/8679204?from=edn_newest_index?
2025-04-16 20:00:57,573 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/7307/8679196?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/7307/8679196?from=edn_newest_index?
2025-04-16 20:00:57,575 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/122328/8679170?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/122328/8679170?from=edn_newest_index?
2025-04-16 20:00:57,576 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/5603/8679181?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/5603/8679181?from=edn_newest_index?
2025-04-16 20:00:57,577 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '/money/story/7307/8679169?from=edn_newest_index': No scheme supplied. Perhaps you meant https:///money/story/7307/8679169?from=edn_newest_index?
2025-04-16 20:01:00,974 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 20:01:00,975 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 20:01:00,976 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 20:01:00,976 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 20:01:00,977 - NewsCollector - WARNING: 抓取新聞內容時發生錯誤: Invalid URL '#': No scheme supplied. Perhaps you meant https://#?
2025-04-16 20:01:00,977 - NewsCollector - INFO: 成功從經濟日報收集 17 則新聞
2025-04-16 20:01:00,978 - NewsCollector - INFO: 總共收集 11 則唯一新聞
2025-04-16 20:01:00,978 - StockAnalysisApp - ERROR: 更新股票數據時發生錯誤: name 'datetime' is not defined
2025-04-16 21:13:40,095 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 21:13:41,485 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: Expecting value: line 1 column 1 (char 0)
2025-04-16 21:13:41,776 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: Expecting value: line 1 column 1 (char 0)
2025-04-16 21:13:41,777 - NewsCollector - INFO: 開始從證交所收集新聞...
2025-04-16 21:13:42,421 - NewsCollector - INFO: 成功從證交所收集 0 則新聞
2025-04-16 21:13:42,422 - NewsCollector - INFO: 開始從經濟日報收集新聞...
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'NoneType' object has no attribute 'text'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,208 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - WARNING: 處理單一新聞項目時發生錯誤: 'str' object has no attribute 'attrs'
2025-04-16 21:13:43,209 - NewsCollector - INFO: 成功從經濟日報收集 0 則新聞
2025-04-16 21:13:43,209 - NewsCollector - INFO: 總共收集 0 則唯一新聞
2025-04-16 21:13:43,209 - StockAnalysisApp - INFO: 股票數據更新完成
2025-04-16 22:14:50,902 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 22:14:59,657 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: Expecting value: line 1 column 1 (char 0)
2025-04-16 22:15:03,317 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: Expecting value: line 1 column 1 (char 0)
2025-04-16 22:15:36,622 - yfinance - ERROR: 404 Client Error: Not Found for url: https://query2.finance.yahoo.com/v10/finance/quoteSummary/70000U.TW?modules=financialData%2CquoteType%2CdefaultKeyStatistics%2CassetProfile%2CsummaryDetail&corsDomain=finance.yahoo.com&formatted=false&symbol=70000U.TW&crumb=N6%2FpKS1k2or
2025-04-16 22:15:36,623 - PriceCollector - ERROR: 獲取 70000U 股價時發生錯誤: 'NoneType' object has no attribute 'update'
2025-04-16 22:15:44,903 - yfinance - ERROR: 404 Client Error: Not Found for url: https://query2.finance.yahoo.com/v10/finance/quoteSummary/70001U.TW?modules=financialData%2CquoteType%2CdefaultKeyStatistics%2CassetProfile%2CsummaryDetail&corsDomain=finance.yahoo.com&formatted=false&symbol=70001U.TW&crumb=N6%2FpKS1k2or
2025-04-16 22:15:44,903 - PriceCollector - ERROR: 獲取 70001U 股價時發生錯誤: 'NoneType' object has no attribute 'update'
2025-04-16 22:15:50,677 - yfinance - ERROR: $70002U.TW: possibly delisted; no timezone found
2025-04-16 22:15:50,679 - PriceCollector - ERROR: 獲取 70002U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:15:55,434 - yfinance - ERROR: $70003U.TW: possibly delisted; no timezone found
2025-04-16 22:15:55,436 - PriceCollector - ERROR: 獲取 70003U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:15:58,573 - yfinance - ERROR: $70004U.TW: possibly delisted; no timezone found
2025-04-16 22:15:58,574 - PriceCollector - ERROR: 獲取 70004U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:16:01,073 - yfinance - ERROR: $70005U.TW: possibly delisted; no timezone found
2025-04-16 22:16:01,073 - PriceCollector - ERROR: 獲取 70005U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:16:03,785 - yfinance - ERROR: $70006U.TW: possibly delisted; no timezone found
2025-04-16 22:16:03,787 - PriceCollector - ERROR: 獲取 70006U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:16:16,274 - yfinance - ERROR: Failed to get ticker '70007U.TW' reason: HTTPSConnectionPool(host='query1.finance.yahoo.com', port=443): Read timed out. (read timeout=10)
2025-04-16 22:16:16,275 - yfinance - ERROR: $70007U.TW: possibly delisted; no timezone found
2025-04-16 22:16:16,276 - PriceCollector - ERROR: 獲取 70007U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:16:28,661 - yfinance - ERROR: Failed to get ticker '70008U.TW' reason: HTTPSConnectionPool(host='query2.finance.yahoo.com', port=443): Read timed out. (read timeout=10)
2025-04-16 22:16:28,664 - yfinance - ERROR: $70008U.TW: possibly delisted; no timezone found
2025-04-16 22:16:28,666 - PriceCollector - ERROR: 獲取 70008U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:17:00,826 - yfinance - ERROR: Failed to get ticker '70009U.TW' reason: HTTPSConnectionPool(host='guce.yahoo.com', port=443): Read timed out. (read timeout=30)
2025-04-16 22:17:00,827 - yfinance - ERROR: $70009U.TW: possibly delisted; no timezone found
2025-04-16 22:17:00,829 - PriceCollector - ERROR: 獲取 70009U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:17:32,190 - yfinance - ERROR: Failed to get ticker '70010U.TW' reason: HTTPSConnectionPool(host='www.yahoo.com', port=443): Read timed out. (read timeout=30)
2025-04-16 22:17:32,192 - yfinance - ERROR: $70010U.TW: possibly delisted; no timezone found
2025-04-16 22:17:32,193 - PriceCollector - ERROR: 獲取 70010U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:05,437 - yfinance - ERROR: Failed to get ticker '70011U.TW' reason: HTTPSConnectionPool(host='tw.yahoo.com', port=443): Read timed out. (read timeout=30)
2025-04-16 22:18:05,440 - yfinance - ERROR: $70011U.TW: possibly delisted; no timezone found
2025-04-16 22:18:05,442 - PriceCollector - ERROR: 獲取 70011U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:17,625 - yfinance - ERROR: $70012U.TW: possibly delisted; no timezone found
2025-04-16 22:18:17,627 - PriceCollector - ERROR: 獲取 70012U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:22,060 - yfinance - ERROR: $70013U.TW: possibly delisted; no timezone found
2025-04-16 22:18:22,062 - PriceCollector - ERROR: 獲取 70013U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:25,751 - yfinance - ERROR: $70014U.TW: possibly delisted; no timezone found
2025-04-16 22:18:25,753 - PriceCollector - ERROR: 獲取 70014U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:29,435 - yfinance - ERROR: $70015U.TW: possibly delisted; no timezone found
2025-04-16 22:18:29,436 - PriceCollector - ERROR: 獲取 70015U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:34,098 - yfinance - ERROR: $70016U.TW: possibly delisted; no timezone found
2025-04-16 22:18:34,099 - PriceCollector - ERROR: 獲取 70016U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:37,897 - yfinance - ERROR: $70017U.TW: possibly delisted; no timezone found
2025-04-16 22:18:37,899 - PriceCollector - ERROR: 獲取 70017U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:41,307 - yfinance - ERROR: $70018U.TW: possibly delisted; no timezone found
2025-04-16 22:18:41,307 - PriceCollector - ERROR: 獲取 70018U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:44,009 - yfinance - ERROR: $70019U.TW: possibly delisted; no timezone found
2025-04-16 22:18:44,010 - PriceCollector - ERROR: 獲取 70019U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:46,409 - yfinance - ERROR: $70020U.TW: possibly delisted; no timezone found
2025-04-16 22:18:46,411 - PriceCollector - ERROR: 獲取 70020U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:48,661 - yfinance - ERROR: $70021U.TW: possibly delisted; no timezone found
2025-04-16 22:18:48,661 - PriceCollector - ERROR: 獲取 70021U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:52,914 - yfinance - ERROR: $70022U.TW: possibly delisted; no timezone found
2025-04-16 22:18:52,915 - PriceCollector - ERROR: 獲取 70022U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:55,864 - yfinance - ERROR: $70023U.TW: possibly delisted; no timezone found
2025-04-16 22:18:55,865 - PriceCollector - ERROR: 獲取 70023U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:18:58,755 - yfinance - ERROR: $70024U.TW: possibly delisted; no timezone found
2025-04-16 22:18:58,756 - PriceCollector - ERROR: 獲取 70024U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:19:01,426 - yfinance - ERROR: $70025U.TW: possibly delisted; no timezone found
2025-04-16 22:19:01,427 - PriceCollector - ERROR: 獲取 70025U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:19:05,048 - yfinance - ERROR: $70026U.TW: possibly delisted; no timezone found
2025-04-16 22:19:05,049 - PriceCollector - ERROR: 獲取 70026U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:24:03,967 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 22:24:04,442 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: 'list' object has no attribute 'get'
2025-04-16 22:24:04,717 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: 'list' object has no attribute 'get'
2025-04-16 22:24:15,180 - yfinance - ERROR: 404 Client Error: Not Found for url: https://query2.finance.yahoo.com/v10/finance/quoteSummary/70000U.TW?modules=financialData%2CquoteType%2CdefaultKeyStatistics%2CassetProfile%2CsummaryDetail&corsDomain=finance.yahoo.com&formatted=false&symbol=70000U.TW&crumb=N6%2FpKS1k2or
2025-04-16 22:24:15,180 - PriceCollector - ERROR: 獲取 70000U 股價時發生錯誤: 'NoneType' object has no attribute 'update'
2025-04-16 22:24:21,285 - yfinance - ERROR: 404 Client Error: Not Found for url: https://query2.finance.yahoo.com/v10/finance/quoteSummary/70001U.TW?modules=financialData%2CquoteType%2CdefaultKeyStatistics%2CassetProfile%2CsummaryDetail&corsDomain=finance.yahoo.com&formatted=false&symbol=70001U.TW&crumb=N6%2FpKS1k2or
2025-04-16 22:24:21,285 - PriceCollector - ERROR: 獲取 70001U 股價時發生錯誤: 'NoneType' object has no attribute 'update'
2025-04-16 22:24:24,762 - yfinance - ERROR: $70002U.TW: possibly delisted; no timezone found
2025-04-16 22:24:24,762 - PriceCollector - ERROR: 獲取 70002U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:24:29,845 - yfinance - ERROR: $70003U.TW: possibly delisted; no timezone found
2025-04-16 22:24:29,848 - PriceCollector - ERROR: 獲取 70003U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:24:36,206 - yfinance - ERROR: $70004U.TW: possibly delisted; no timezone found
2025-04-16 22:24:36,208 - PriceCollector - ERROR: 獲取 70004U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:24:39,256 - yfinance - ERROR: $70005U.TW: possibly delisted; no timezone found
2025-04-16 22:24:39,257 - PriceCollector - ERROR: 獲取 70005U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:24:43,584 - yfinance - ERROR: $70006U.TW: possibly delisted; no timezone found
2025-04-16 22:24:43,587 - PriceCollector - ERROR: 獲取 70006U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:24:47,610 - yfinance - ERROR: $70007U.TW: possibly delisted; no timezone found
2025-04-16 22:24:47,614 - PriceCollector - ERROR: 獲取 70007U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:24:51,951 - yfinance - ERROR: $70008U.TW: possibly delisted; no timezone found
2025-04-16 22:24:51,951 - PriceCollector - ERROR: 獲取 70008U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:24:56,058 - yfinance - ERROR: $70009U.TW: possibly delisted; no timezone found
2025-04-16 22:24:56,060 - PriceCollector - ERROR: 獲取 70009U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:00,598 - yfinance - ERROR: $70010U.TW: possibly delisted; no timezone found
2025-04-16 22:25:00,598 - PriceCollector - ERROR: 獲取 70010U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:03,412 - yfinance - ERROR: $70011U.TW: possibly delisted; no timezone found
2025-04-16 22:25:03,413 - PriceCollector - ERROR: 獲取 70011U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:07,442 - yfinance - ERROR: $70012U.TW: possibly delisted; no timezone found
2025-04-16 22:25:07,442 - PriceCollector - ERROR: 獲取 70012U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:16,242 - yfinance - ERROR: $70013U.TW: possibly delisted; no timezone found
2025-04-16 22:25:16,243 - PriceCollector - ERROR: 獲取 70013U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:24,078 - yfinance - ERROR: $70014U.TW: possibly delisted; no timezone found
2025-04-16 22:25:24,079 - PriceCollector - ERROR: 獲取 70014U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:29,739 - yfinance - ERROR: $70015U.TW: possibly delisted; no timezone found
2025-04-16 22:25:29,741 - PriceCollector - ERROR: 獲取 70015U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:35,941 - yfinance - ERROR: $70016U.TW: possibly delisted; no timezone found
2025-04-16 22:25:35,943 - PriceCollector - ERROR: 獲取 70016U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:41,635 - yfinance - ERROR: $70017U.TW: possibly delisted; no timezone found
2025-04-16 22:25:41,636 - PriceCollector - ERROR: 獲取 70017U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:47,594 - yfinance - ERROR: $70018U.TW: possibly delisted; no timezone found
2025-04-16 22:25:47,596 - PriceCollector - ERROR: 獲取 70018U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:52,484 - yfinance - ERROR: $70019U.TW: possibly delisted; no timezone found
2025-04-16 22:25:52,485 - PriceCollector - ERROR: 獲取 70019U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:25:58,633 - yfinance - ERROR: $70020U.TW: possibly delisted; no timezone found
2025-04-16 22:25:58,635 - PriceCollector - ERROR: 獲取 70020U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:03,971 - yfinance - ERROR: $70021U.TW: possibly delisted; no timezone found
2025-04-16 22:26:03,972 - PriceCollector - ERROR: 獲取 70021U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:10,242 - yfinance - ERROR: $70022U.TW: possibly delisted; no timezone found
2025-04-16 22:26:10,244 - PriceCollector - ERROR: 獲取 70022U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:13,775 - yfinance - ERROR: $70023U.TW: possibly delisted; no timezone found
2025-04-16 22:26:13,777 - PriceCollector - ERROR: 獲取 70023U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:16,291 - yfinance - ERROR: $70024U.TW: possibly delisted; no timezone found
2025-04-16 22:26:16,292 - PriceCollector - ERROR: 獲取 70024U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:20,285 - yfinance - ERROR: $70025U.TW: possibly delisted; no timezone found
2025-04-16 22:26:20,287 - PriceCollector - ERROR: 獲取 70025U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:23,613 - yfinance - ERROR: $70026U.TW: possibly delisted; no timezone found
2025-04-16 22:26:23,614 - PriceCollector - ERROR: 獲取 70026U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:27,460 - yfinance - ERROR: $70027U.TW: possibly delisted; no timezone found
2025-04-16 22:26:27,461 - PriceCollector - ERROR: 獲取 70027U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:30,767 - yfinance - ERROR: $70028U.TW: possibly delisted; no timezone found
2025-04-16 22:26:30,768 - PriceCollector - ERROR: 獲取 70028U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:34,348 - yfinance - ERROR: $70029U.TW: possibly delisted; no timezone found
2025-04-16 22:26:34,349 - PriceCollector - ERROR: 獲取 70029U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:39,189 - yfinance - ERROR: $70030U.TW: possibly delisted; no timezone found
2025-04-16 22:26:39,191 - PriceCollector - ERROR: 獲取 70030U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:44,358 - yfinance - ERROR: $70031U.TW: possibly delisted; no timezone found
2025-04-16 22:26:44,360 - PriceCollector - ERROR: 獲取 70031U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:49,048 - yfinance - ERROR: $70032U.TW: possibly delisted; no timezone found
2025-04-16 22:26:49,049 - PriceCollector - ERROR: 獲取 70032U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:52,051 - yfinance - ERROR: $70033U.TW: possibly delisted; no timezone found
2025-04-16 22:26:52,053 - PriceCollector - ERROR: 獲取 70033U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:56,434 - yfinance - ERROR: $70034U.TW: possibly delisted; no timezone found
2025-04-16 22:26:56,436 - PriceCollector - ERROR: 獲取 70034U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:26:59,616 - yfinance - ERROR: $70035U.TW: possibly delisted; no timezone found
2025-04-16 22:26:59,627 - PriceCollector - ERROR: 獲取 70035U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:05,291 - yfinance - ERROR: $70036U.TW: possibly delisted; no timezone found
2025-04-16 22:27:05,293 - PriceCollector - ERROR: 獲取 70036U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:07,027 - yfinance - ERROR: $70037U.TW: possibly delisted; no timezone found
2025-04-16 22:27:07,028 - PriceCollector - ERROR: 獲取 70037U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:11,654 - yfinance - ERROR: $70038U.TW: possibly delisted; no timezone found
2025-04-16 22:27:11,655 - PriceCollector - ERROR: 獲取 70038U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:13,924 - yfinance - ERROR: $70039U.TW: possibly delisted; no timezone found
2025-04-16 22:27:13,927 - PriceCollector - ERROR: 獲取 70039U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:17,724 - yfinance - ERROR: $70040U.TW: possibly delisted; no timezone found
2025-04-16 22:27:17,725 - PriceCollector - ERROR: 獲取 70040U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:20,917 - yfinance - ERROR: $70041U.TW: possibly delisted; no timezone found
2025-04-16 22:27:20,918 - PriceCollector - ERROR: 獲取 70041U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:25,230 - yfinance - ERROR: $70042U.TW: possibly delisted; no timezone found
2025-04-16 22:27:25,231 - PriceCollector - ERROR: 獲取 70042U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:28,942 - yfinance - ERROR: $70043U.TW: possibly delisted; no timezone found
2025-04-16 22:27:28,944 - PriceCollector - ERROR: 獲取 70043U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:33,756 - yfinance - ERROR: $70044U.TW: possibly delisted; no timezone found
2025-04-16 22:27:33,757 - PriceCollector - ERROR: 獲取 70044U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:39,036 - yfinance - ERROR: $70045U.TW: possibly delisted; no timezone found
2025-04-16 22:27:39,039 - PriceCollector - ERROR: 獲取 70045U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:42,804 - yfinance - ERROR: $70046U.TW: possibly delisted; no timezone found
2025-04-16 22:27:42,804 - PriceCollector - ERROR: 獲取 70046U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:46,653 - yfinance - ERROR: $70047U.TW: possibly delisted; no timezone found
2025-04-16 22:27:46,655 - PriceCollector - ERROR: 獲取 70047U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:50,425 - yfinance - ERROR: $70048U.TW: possibly delisted; no timezone found
2025-04-16 22:27:50,428 - PriceCollector - ERROR: 獲取 70048U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:54,783 - yfinance - ERROR: $70049U.TW: possibly delisted; no timezone found
2025-04-16 22:27:54,786 - PriceCollector - ERROR: 獲取 70049U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:27:58,500 - yfinance - ERROR: $70050U.TW: possibly delisted; no timezone found
2025-04-16 22:27:58,504 - PriceCollector - ERROR: 獲取 70050U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:01,073 - yfinance - ERROR: $70051U.TW: possibly delisted; no timezone found
2025-04-16 22:28:01,075 - PriceCollector - ERROR: 獲取 70051U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:03,729 - yfinance - ERROR: $70052U.TW: possibly delisted; no timezone found
2025-04-16 22:28:03,731 - PriceCollector - ERROR: 獲取 70052U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:05,479 - yfinance - ERROR: $70053U.TW: possibly delisted; no timezone found
2025-04-16 22:28:05,480 - PriceCollector - ERROR: 獲取 70053U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:08,822 - yfinance - ERROR: $70054U.TW: possibly delisted; no timezone found
2025-04-16 22:28:08,824 - PriceCollector - ERROR: 獲取 70054U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:10,329 - yfinance - ERROR: $70055U.TW: possibly delisted; no timezone found
2025-04-16 22:28:10,329 - PriceCollector - ERROR: 獲取 70055U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:11,850 - yfinance - ERROR: $70056U.TW: possibly delisted; no timezone found
2025-04-16 22:28:11,852 - PriceCollector - ERROR: 獲取 70056U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:13,316 - yfinance - ERROR: $70057U.TW: possibly delisted; no timezone found
2025-04-16 22:28:13,317 - PriceCollector - ERROR: 獲取 70057U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:14,578 - yfinance - ERROR: $70058U.TW: possibly delisted; no timezone found
2025-04-16 22:28:14,579 - PriceCollector - ERROR: 獲取 70058U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:19,074 - yfinance - ERROR: $70059U.TW: possibly delisted; no timezone found
2025-04-16 22:28:19,077 - PriceCollector - ERROR: 獲取 70059U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:23,414 - yfinance - ERROR: $70060U.TW: possibly delisted; no timezone found
2025-04-16 22:28:23,414 - PriceCollector - ERROR: 獲取 70060U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:27,000 - yfinance - ERROR: $70061U.TW: possibly delisted; no timezone found
2025-04-16 22:28:27,001 - PriceCollector - ERROR: 獲取 70061U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:30,789 - yfinance - ERROR: $70062U.TW: possibly delisted; no timezone found
2025-04-16 22:28:30,790 - PriceCollector - ERROR: 獲取 70062U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:35,866 - yfinance - ERROR: $70063U.TW: possibly delisted; no timezone found
2025-04-16 22:28:35,868 - PriceCollector - ERROR: 獲取 70063U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:39,287 - yfinance - ERROR: $70064U.TW: possibly delisted; no timezone found
2025-04-16 22:28:39,288 - PriceCollector - ERROR: 獲取 70064U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:43,111 - yfinance - ERROR: $70065U.TW: possibly delisted; no timezone found
2025-04-16 22:28:43,112 - PriceCollector - ERROR: 獲取 70065U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:47,612 - yfinance - ERROR: $70066U.TW: possibly delisted; no timezone found
2025-04-16 22:28:47,612 - PriceCollector - ERROR: 獲取 70066U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:52,375 - yfinance - ERROR: $70067U.TW: possibly delisted; no timezone found
2025-04-16 22:28:52,375 - PriceCollector - ERROR: 獲取 70067U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:28:57,058 - yfinance - ERROR: $70068U.TW: possibly delisted; no timezone found
2025-04-16 22:28:57,058 - PriceCollector - ERROR: 獲取 70068U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:29:01,345 - yfinance - ERROR: $70069U.TW: possibly delisted; no timezone found
2025-04-16 22:29:01,346 - PriceCollector - ERROR: 獲取 70069U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:29:04,447 - yfinance - ERROR: $70070U.TW: possibly delisted; no timezone found
2025-04-16 22:29:04,449 - PriceCollector - ERROR: 獲取 70070U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:29:07,331 - yfinance - ERROR: $70071U.TW: possibly delisted; no timezone found
2025-04-16 22:29:07,331 - PriceCollector - ERROR: 獲取 70071U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:29:10,643 - yfinance - ERROR: $70072U.TW: possibly delisted; no timezone found
2025-04-16 22:29:10,644 - PriceCollector - ERROR: 獲取 70072U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:29:14,630 - yfinance - ERROR: $70073U.TW: possibly delisted; no timezone found
2025-04-16 22:29:14,630 - PriceCollector - ERROR: 獲取 70073U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:29:17,867 - yfinance - ERROR: $70074U.TW: possibly delisted; no timezone found
2025-04-16 22:29:17,868 - PriceCollector - ERROR: 獲取 70074U 股價時發生錯誤: Can only use .dt accessor with datetimelike values
2025-04-16 22:29:25,324 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 22:29:28,949 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: 'list' object has no attribute 'get'
2025-04-16 22:29:30,674 - PriceCollector - ERROR: 獲取證交所股票清單時發生錯誤: 'list' object has no attribute 'get'
2025-04-16 22:29:30,683 - StockAnalysisApp - ERROR: 更新股票數據時發生錯誤: 'DataCollectionManager' object has no attribute 'logger'
2025-04-16 22:31:30,676 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 22:31:33,080 - StockAnalysisApp - ERROR: 更新股票數據時發生錯誤: 'DataCollectionManager' object has no attribute 'logger'
2025-04-16 23:03:04,891 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 23:03:10,586 - StockAnalysisApp - ERROR: 更新股票數據時發生錯誤: 'DataCollectionManager' object has no attribute 'logger'
2025-04-16 23:08:48,343 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 23:08:50,965 - StockAnalysisApp - ERROR: 更新股票數據時發生錯誤: 'DataCollectionManager' object has no attribute 'logger'
2025-04-16 23:17:52,324 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 23:17:55,010 - StockAnalysisApp - ERROR: 更新股票數據時發生錯誤: 'DataCollectionManager' object has no attribute 'logger'
2025-04-16 23:18:05,074 - StockAnalysisApp - INFO: 開始更新股票數據...
2025-04-16 23:18:05,267 - StockAnalysisApp - ERROR: 更新股票數據時發生錯誤: 'DataCollectionManager' object has no attribute 'logger'
