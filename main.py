import os
import sys
import json
import argparse
import logging
from datetime import datetime
import sqlite3

# 將專案根目錄添加到 Python 路徑
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, PROJECT_ROOT)

# 導入各模組
from data_collectors import DataCollectionManager
from models import ModelFactory
from analyzers import AnalyzerFactory
from utils.logger import SimpleLogger
from utils.config import ConfigManager
from database import createtable

class StockAnalysisApp:
    """
    股票分析系統主應用程式
    """
    
    def __init__(self, config_filename: str = 'app_config.json'): # 設定檔名可以從外部傳入
        # 初始化日誌
        # 日誌目錄會從設定檔讀取，所以 StockLogger 的初始化稍微調整
        
        # 載入配置
        self.config_manager = ConfigManager(config_dir='config') # 指定設定檔所在的目錄
        self.config = self.config_manager.load_config(config_filename)
        
        # 從載入的設定中獲取資料庫路徑和日誌路徑
        self.db_path = self.config_manager.get_config_value(self.config, 'database.path')
        log_dir_from_config = self.config_manager.get_config_value(self.config, 'logging.dir')
        log_level_str = self.config_manager.get_config_value(self.config, 'logging.level', 'INFO')
        log_level = getattr(logging, log_level_str.upper(), logging.INFO)

        # 現在可以用設定檔中的路徑初始化 SimpleLogger
        self.logger_instance = SimpleLogger(log_dir=log_dir_from_config)
        self.logger = self.logger_instance
        
        self.logger.info(f"從設定檔讀取的資料庫路徑: {self.db_path}")
        self.logger.info(f"從設定檔讀取的日誌目錄: {log_dir_from_config}")

        # 確保資料庫目錄存在 (createtable.py 的 main 也會做，但這裡也可以先做一次)
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            try:
                os.makedirs(db_dir)
                self.logger.info(f"由 StockAnalysisApp 創建資料庫目錄: {db_dir}")
            except OSError as e:
                self.logger.error(f"創建資料庫目錄 {db_dir} 失敗: {e}")
                # 根據情況，這裡可能需要退出程式或拋出異常
                raise

        # 檢查資料庫檔案是否存在並創建資料表 (如果需要)
        # 這一部分可以通過呼叫 createtable.py 中的 main 函數來完成
        try:
            self.logger.info(f"準備初始化資料庫結構於: {self.db_path}")
            createtable.main(self.db_path) # 將設定的 db_path 傳遞給 createtable
            self.logger.info(f"資料庫結構檢查/創建完成於: {self.db_path}")
        except Exception as e:
            self.logger.error(f"初始化資料庫結構時發生嚴重錯誤: {e}")
            # 根據情況，這裡可能需要退出程式或拋出異常
            raise
        
        # 初始化數據收集管理器 (使用從設定檔讀取的 db_path)
        self.data_collector = DataCollectionManager(
            database_path=self.db_path
        )
        
        # 初始化模型工廠 (模型也可能需要 db_path 和 model_dir จาก config)
        model_dir_from_config = self.config_manager.get_config_value(self.config, 'models.dir', os.path.join(PROJECT_ROOT, 'models')) # 假設模型目錄也在設定檔中
        self.model_factory = ModelFactory()
        self.model_dir = model_dir_from_config
        
        # 初始化分析器工廠 (分析器也可能需要 db_path)
        self.analyzer_factory = AnalyzerFactory()

    # ... (StockAnalysisApp 的其餘方法 update_data, analyze_stock 等保持不變) ...
    # 確保在這些方法中，所有底層模組都使用了正確的 self.db_path
    
    def update_data(self, update_all: bool = False, stock_ids: list = None, daily_only: bool = True):
        """
        更新股票數據（優化版：支持日常快速更新）

        Args:
            update_all (bool): 是否更新所有股票
            stock_ids (list): 指定股票代碼列表
            daily_only (bool): 是否只更新當日數據（默認True，大幅提升速度）
        """
        try:
            if daily_only:
                self.logger.info("開始日常快速更新（僅當日數據）")
            else:
                self.logger.info("開始完整數據更新")

            # 如果未指定股票且需要更新全部，則獲取股票清單
            if update_all or not stock_ids:
                stock_list = self.data_collector.collect_stock_list()
                if daily_only:
                    # 日常更新：處理所有股票但只更新當日數據
                    stock_ids = [stock['stock_id'] for stock in stock_list]
                    self.logger.info(f"日常更新模式：將更新 {len(stock_ids)} 支股票的當日數據")
                else:
                    # 完整更新：限制數量避免過長時間
                    stock_ids = [stock['stock_id'] for stock in stock_list[:50]]
                    self.logger.info(f"完整更新模式：將更新 {len(stock_ids)} 支股票的歷史數據")

            # 使用優化的批量更新
            self.data_collector.price_collector.batch_update_stock_prices(
                stock_ids=stock_ids,
                daily_only=daily_only
            )

            # 如果是日常更新，也收集當日新聞
            if daily_only:
                try:
                    self.data_collector.news_collector.collect_integrated_news()
                except Exception as e:
                    self.logger.warning(f"收集新聞時發生錯誤: {e}")

            self.logger.info("股票數據更新完成")

        except Exception as e:
            self.logger.error(f"更新股票數據時發生錯誤: {e}")
            raise
    
    def download_historical_data(self, stock_ids: list = None, use_finlab: bool = False):
        """
        下載歷史數據（支持FinLab快速下載）

        Args:
            stock_ids (list): 指定股票代碼列表，如果為None則下載所有股票
            use_finlab (bool): 是否使用FinLab快速下載（推薦）
        """
        try:
            if use_finlab:
                self.logger.info("使用FinLab快速下載歷史數據...")
                return self._download_with_finlab(stock_ids)
            else:
                self.logger.info("使用傳統API下載歷史數據（較慢）...")
                return self._download_with_api(stock_ids)

        except Exception as e:
            self.logger.error(f"下載歷史數據時發生錯誤: {e}")
            raise

    def _download_with_finlab(self, stock_ids: list = None):
        """使用FinLab快速下載"""
        try:
            from data_collectors.finlab_collector import FinLabCollector

            finlab_collector = FinLabCollector(self.db_path)

            if not finlab_collector.finlab_available:
                self.logger.error("FinLab不可用，請先安裝: pip install finlab")
                return False

            # 使用FinLab下載
            success = finlab_collector.download_historical_data_finlab(
                stock_ids=stock_ids,
                years=15
            )

            finlab_collector.close()

            if success:
                self.logger.info("FinLab歷史數據下載完成")
            else:
                self.logger.error("FinLab下載失敗")

            return success

        except ImportError:
            self.logger.error("FinLab模組未安裝，請執行: pip install finlab")
            return False
        except Exception as e:
            self.logger.error(f"FinLab下載過程發生錯誤: {e}")
            return False

    def _download_with_api(self, stock_ids: list = None):
        """使用傳統API下載（較慢但穩定）"""
        try:
            from datetime import datetime, timedelta

            # 如果未指定股票，則獲取股票清單
            if not stock_ids:
                stock_list = self.data_collector.collect_stock_list()
                stock_ids = [stock['stock_id'] for stock in stock_list]
                self.logger.info(f"將下載 {len(stock_ids)} 支股票的歷史數據")
            else:
                self.logger.info(f"將下載指定的 {len(stock_ids)} 支股票的歷史數據")

            # 設定15年前的日期
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=15*365)).strftime('%Y-%m-%d')

            self.logger.info(f"下載日期範圍: {start_date} 到 {end_date}")

            # 使用優化的批量下載（非日常模式）
            self.data_collector.price_collector.batch_update_stock_prices(
                stock_ids=stock_ids,
                start_date=start_date,
                end_date=end_date,
                daily_only=False
            )

            self.logger.info("API歷史數據下載完成")
            return True

        except Exception as e:
            self.logger.error(f"API下載過程發生錯誤: {e}")
            return False
    
    def analyze_stock(self, stock_id: str):
        """
        分析特定股票
        
        Args:
            stock_id (str): 股票代碼
        
        Returns:
            dict: 分析結果
        """
        try:
            # 生成綜合報告
            comprehensive_report = self.data_collector.generate_comprehensive_report(stock_id)
            
            # 使用不同分析器進行分析
            technical_analyzer = self.analyzer_factory.create_analyzer('technical', self.db_path)
            news_analyzer = self.analyzer_factory.create_analyzer('news', self.db_path)
            financial_analyzer = self.analyzer_factory.create_analyzer('financial', self.db_path)
            
            # 計算技術指標
            technical_indicators = technical_analyzer.calculate_indicators(stock_id)
            
            # 分析新聞情緒
            news_sentiment = news_analyzer.get_stock_news_sentiment(stock_id)
            
            # 財務分析
            financial_ratios = financial_analyzer.calculate_financial_ratios(stock_id)
            
            # 整合分析結果
            analysis_result = {
                'stock_id': stock_id,
                'comprehensive_report': comprehensive_report,
                'technical_indicators': technical_indicators,
                'news_sentiment': news_sentiment,
                'financial_ratios': financial_ratios
            }
            
            return analysis_result
        
        except Exception as e:
            self.logger.error(f"分析股票 {stock_id} 時發生錯誤: {e}")
            return None
    
    def train_prediction_model(self, stock_id: str):
        """
        訓練特定股票的預測模型
        
        Args:
            stock_id (str): 股票代碼
        
        Returns:
            dict: 模型訓練結果
        """
        try:
            # 創建綜合預測模型
            combined_model = self.model_factory.create_model('combined')
            
            # 訓練模型
            model_result = combined_model.train_model(stock_id)
            
            return model_result
        
        except Exception as e:
            self.logger.error(f"訓練 {stock_id} 預測模型時發生錯誤: {e}")
            return None
    
    def predict_stock_movement(self, stock_id: str, days: int = 1):
        """
        預測股票走勢
        
        Args:
            stock_id (str): 股票代碼
            days (int): 預測天數
        
        Returns:
            dict: 預測結果
        """
        try:
            # 創建綜合預測模型
            combined_model = self.model_factory.create_model('combined')
            
            # 進行預測
            prediction_result = combined_model.predict_price_movement(
                stock_id, 
                datetime.now().strftime('%Y-%m-%d'), 
                days
            )
            
            return prediction_result
        
        except Exception as e:
            self.logger.error(f"預測 {stock_id} 股價走勢時發生錯誤: {e}")
            return None
    
    def close(self):
        """             
        關閉所有資源
        """
        try:
            self.logger.info("正在關閉應用程式資源...")
            if self.data_collector:
                self.data_collector.close() # DataCollectionManager 應該有自己的 close 方法來關閉其下的 collectors
            
            # 如果 ModelFactory 或 AnalyzerFactory 持有資料庫連線，也需要關閉
            if hasattr(self.model_factory, 'close'):
                self.model_factory.close()
            if hasattr(self.analyzer_factory, 'close'):
                self.analyzer_factory.close()
            self.logger.info("應用程式資源已關閉。")
        except Exception as e:
            self.logger.error(f"關閉資源時發生錯誤: {e}")

def main():
    """
    主程式入口
    """
    # 設置命令行參數解析
    parser = argparse.ArgumentParser(description='股票分析系統')
    # ... (參數定義不變) ...
    parser.add_argument('--config', type=str, default='app_config.json', help='指定設定檔名稱 (相對於 config/ 目錄)')
    parser.add_argument('--update', action='store_true', help='日常快速更新（僅當日數據）')
    parser.add_argument('--update-full', action='store_true', help='完整更新股票數據（較慢）')
    parser.add_argument('--download-historical', action='store_true', help='下載歷史數據（使用API，較慢）')
    parser.add_argument('--download-finlab', action='store_true', help='使用FinLab快速下載歷史數據（推薦）')
    parser.add_argument('--download-all', action='store_true', help='下載所有股票的歷史數據')
    parser.add_argument('--stock-ids', type=str, nargs='*', help='指定要下載的股票代碼列表')
    parser.add_argument('--analyze', type=str, help='分析指定股票')
    parser.add_argument('--predict', type=str, help='預測指定股票走勢')
    parser.add_argument('--train', type=str, help='訓練指定股票的預測模型')

    args = parser.parse_args()
    
    if not (args.update or args.update_full or args.download_historical or args.download_finlab or args.download_all or args.analyze or args.predict or args.train):
        parser.print_help()
        return

    # 創建應用程式實例，傳入設定檔名
    config_file_relative_path = args.config
    app = StockAnalysisApp(config_filename=config_file_relative_path)

    try:
        # 日常快速更新（推薦）
        if args.update:
            if args.stock_ids:
                app.update_data(stock_ids=args.stock_ids, daily_only=True)
            else:
                app.update_data(update_all=True, daily_only=True)

        # 完整更新（較慢）
        if args.update_full:
            app.update_data(update_all=True, daily_only=False)

        # 使用API下載歷史數據（較慢）
        if args.download_historical:
            app.download_historical_data(stock_ids=args.stock_ids, use_finlab=False)

        # 使用FinLab快速下載（推薦）
        if args.download_finlab:
            app.download_historical_data(stock_ids=args.stock_ids, use_finlab=True)

        # 下載所有股票
        if args.download_all:
            use_finlab = True  # 下載所有股票時默認使用FinLab
            app.download_historical_data(stock_ids=None, use_finlab=use_finlab)
        
        if args.analyze:
            result = app.analyze_stock(args.analyze)
            if result: # 檢查是否有結果
                # 處理DataFrame對象，轉換為可序列化的格式
                def convert_to_serializable(obj):
                    if hasattr(obj, 'to_dict'):
                        return obj.to_dict()
                    elif hasattr(obj, '__dict__'):
                        return obj.__dict__
                    else:
                        return str(obj)
                
                # 遞歸處理結果中的所有對象
                def process_result(data):
                    if isinstance(data, dict):
                        return {k: process_result(v) for k, v in data.items()}
                    elif isinstance(data, list):
                        return [process_result(item) for item in data]
                    elif hasattr(data, 'to_dict'):
                        return data.to_dict()
                    else:
                        return data
                
                serializable_result = process_result(result)
                print(json.dumps(serializable_result, indent=4, ensure_ascii=False, default=str))
        
        if args.predict:
            result = app.predict_stock_movement(args.predict)
            if result:
                print(json.dumps(result, indent=4, ensure_ascii=False))
        
        if args.train:
            result = app.train_prediction_model(args.train)
            if result:
                # 模型訓練結果可能包含 DataFrame，需要特殊處理才能 JSON 序列化
                # 這裡僅打印部分資訊或提示模型已保存
                print(f"模型訓練完成，準確率: {result.get('accuracy', 'N/A')}")
                print(f"模型已保存至: {result.get('model_path', 'N/A')}")

    except Exception as e:
        # 使用 app 內部的 logger 來記錄錯誤，如果 app 初始化成功的話
        if hasattr(app, 'logger'):
            app.logger.error(f"主程式執行時發生嚴重錯誤: {e}", exc_info=True)
        else: # 如果 app 初始化失敗，則使用全域的 logging
            logging.error(f"主程式執行時發生嚴重錯誤 (App 未完全初始化): {e}", exc_info=True)
        print(f"發生錯誤: {e}") # 仍然在控制台打印錯誤
    
    finally:
        if 'app' in locals() and app: # 確保 app 實例已創建
            app.close()

if __name__ == "__main__":
    main()