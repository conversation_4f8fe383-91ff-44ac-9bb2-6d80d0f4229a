#!/usr/bin/env python3
"""增強版主程序

使用依賴注入架構的股票分析系統主程序。
"""

import sys
import argparse
from datetime import datetime, timedelta
from typing import Optional

from utils.app_factory import get_app_factory
from utils.exceptions import StockAnalysisException, handle_exception


def setup_argument_parser() -> argparse.ArgumentParser:
    """設置命令行參數解析器"""
    parser = argparse.ArgumentParser(
        description="增強版股票分析系統",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s predict --stock 2330 --days 30
  %(prog)s train --stock 2330 --start 2023-01-01 --end 2023-12-31
  %(prog)s health-check
  %(prog)s config --show
        """
    )
    
    # 全局選項
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路徑（默認: config/config.json）'
    )
    
    parser.add_argument(
        '--log-level', '-l',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='日誌級別（默認: INFO）'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='詳細輸出'
    )
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 預測命令
    predict_parser = subparsers.add_parser('predict', help='預測股價')
    predict_parser.add_argument(
        '--stock', '-s',
        required=True,
        help='股票代碼（例如: 2330）'
    )
    predict_parser.add_argument(
        '--days', '-d',
        type=int,
        default=30,
        help='預測天數（默認: 30）'
    )
    predict_parser.add_argument(
        '--model-type', '-m',
        choices=['combined', 'price', 'news'],
        default='combined',
        help='模型類型（默認: combined）'
    )
    
    # 訓練命令
    train_parser = subparsers.add_parser('train', help='訓練模型')
    train_parser.add_argument(
        '--stock', '-s',
        required=True,
        help='股票代碼（例如: 2330）'
    )
    train_parser.add_argument(
        '--start',
        type=str,
        help='開始日期（YYYY-MM-DD）'
    )
    train_parser.add_argument(
        '--end',
        type=str,
        help='結束日期（YYYY-MM-DD）'
    )
    train_parser.add_argument(
        '--model-type', '-m',
        choices=['combined', 'price', 'news'],
        default='combined',
        help='模型類型（默認: combined）'
    )
    train_parser.add_argument(
        '--save',
        action='store_true',
        help='保存訓練好的模型'
    )
    
    # 健康檢查命令
    health_parser = subparsers.add_parser('health-check', help='系統健康檢查')
    
    # 配置命令
    config_parser = subparsers.add_parser('config', help='配置管理')
    config_group = config_parser.add_mutually_exclusive_group(required=True)
    config_group.add_argument(
        '--show',
        action='store_true',
        help='顯示當前配置'
    )
    config_group.add_argument(
        '--set',
        nargs=2,
        metavar=('KEY', 'VALUE'),
        help='設置配置值'
    )
    config_group.add_argument(
        '--get',
        metavar='KEY',
        help='獲取配置值'
    )
    
    # 數據管理命令
    data_parser = subparsers.add_parser('data', help='數據管理')
    data_subparsers = data_parser.add_subparsers(dest='data_command', help='數據操作')
    
    # 數據統計
    stats_parser = data_subparsers.add_parser('stats', help='數據統計')
    stats_parser.add_argument(
        '--stock', '-s',
        help='股票代碼（可選）'
    )
    
    # 數據清理
    cleanup_parser = data_subparsers.add_parser('cleanup', help='數據清理')
    cleanup_parser.add_argument(
        '--days',
        type=int,
        default=365,
        help='保留天數（默認: 365）'
    )
    cleanup_parser.add_argument(
        '--dry-run',
        action='store_true',
        help='模擬運行（不實際刪除）'
    )
    
    return parser


@handle_exception
def predict_stock_price(app_factory, args):
    """預測股價"""
    logger = app_factory.get_logger()
    logger.info(f"開始預測股票 {args.stock} 未來 {args.days} 天的價格")
    
    if args.model_type == 'combined':
        model = app_factory.create_combined_model()
    elif args.model_type == 'price':
        model = app_factory.create_price_predictor()
    elif args.model_type == 'news':
        model = app_factory.create_news_model()
    
    # 設置日期範圍
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
    
    # 準備特徵數據
    features = model.prepare_features(args.stock, start_date, end_date)
    
    if features.empty:
        logger.error(f"無法獲取股票 {args.stock} 的數據")
        return
    
    # 進行預測
    if hasattr(model, 'predict'):
        predictions = model.predict(args.stock, args.days)
        
        print(f"\n股票 {args.stock} 預測結果:")
        print(f"模型類型: {args.model_type}")
        print(f"預測天數: {args.days}")
        print(f"當前價格: {features['close'].iloc[-1]:.2f}")
        
        if isinstance(predictions, dict):
            for key, value in predictions.items():
                print(f"{key}: {value:.2f}")
        else:
            print(f"預測價格: {predictions:.2f}")
    else:
        logger.error(f"模型 {args.model_type} 不支持預測功能")


@handle_exception
def train_model(app_factory, args):
    """訓練模型"""
    logger = app_factory.get_logger()
    logger.info(f"開始訓練 {args.model_type} 模型，股票: {args.stock}")
    
    # 設置日期範圍
    if args.start:
        start_date = args.start
    else:
        start_date = (datetime.now() - timedelta(days=730)).strftime('%Y-%m-%d')
    
    if args.end:
        end_date = args.end
    else:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    # 創建模型
    if args.model_type == 'combined':
        model = app_factory.create_combined_model()
    elif args.model_type == 'price':
        model = app_factory.create_price_predictor()
    elif args.model_type == 'news':
        model = app_factory.create_news_model()
    
    # 準備訓練數據
    features = model.prepare_features(args.stock, start_date, end_date)
    
    if features.empty:
        logger.error(f"無法獲取股票 {args.stock} 的訓練數據")
        return
    
    # 訓練模型
    if hasattr(model, 'train'):
        results = model.train(features)
        
        print(f"\n模型訓練完成:")
        print(f"股票代碼: {args.stock}")
        print(f"模型類型: {args.model_type}")
        print(f"訓練期間: {start_date} 至 {end_date}")
        print(f"數據點數: {len(features)}")
        
        if isinstance(results, dict):
            for key, value in results.items():
                if isinstance(value, float):
                    print(f"{key}: {value:.4f}")
                else:
                    print(f"{key}: {value}")
        
        if args.save:
            model.save_model(f"{args.model_type}_{args.stock}")
            logger.info("模型已保存")
    else:
        logger.error(f"模型 {args.model_type} 不支持訓練功能")


def health_check(app_factory):
    """系統健康檢查"""
    print("正在進行系統健康檢查...\n")
    
    health_status = app_factory.health_check()
    
    print(f"整體狀態: {health_status['status'].upper()}")
    print("\n組件狀態:")
    
    for component, status in health_status['components'].items():
        status_icon = "✅" if status['status'] == 'healthy' else "❌"
        print(f"  {status_icon} {component}: {status['status']}")
        
        if status['status'] == 'unhealthy' and 'error' in status:
            print(f"    錯誤: {status['error']}")
        elif 'stats' in status:
            for key, value in status['stats'].items():
                print(f"    {key}: {value}")
        elif status['status'] == 'healthy':
            for key, value in status.items():
                if key != 'status':
                    print(f"    {key}: {value}")


def config_management(app_factory, args):
    """配置管理"""
    config_manager = app_factory.get_config_manager()
    
    if args.show:
        print("當前配置:")
        config_data = config_manager.get_all()
        import json
        print(json.dumps(config_data, indent=2, ensure_ascii=False))
    
    elif args.set:
        key, value = args.set
        config_manager.set(key, value)
        print(f"配置已更新: {key} = {value}")
    
    elif args.get:
        value = config_manager.get(args.get)
        print(f"{args.get}: {value}")


def data_management(app_factory, args):
    """數據管理"""
    logger = app_factory.get_logger()
    db_manager = app_factory.get_db_manager()
    
    if args.data_command == 'stats':
        print("數據統計信息:")
        
        if args.stock:
            # 特定股票統計
            count = db_manager.get_row_count('stock_data', 'stock_code = ?', (args.stock,))
            print(f"股票 {args.stock} 數據記錄數: {count}")
        else:
            # 全部統計
            tables = ['stock_data', 'news_data', 'watchlist']
            for table in tables:
                if db_manager.table_exists(table):
                    count = db_manager.get_row_count(table)
                    print(f"{table} 表記錄數: {count}")
    
    elif args.data_command == 'cleanup':
        logger.info(f"開始數據清理，保留 {args.days} 天的數據")
        
        if args.dry_run:
            print("模擬運行模式 - 不會實際刪除數據")
        
        # 這裡可以添加具體的清理邏輯
        print(f"數據清理完成（保留 {args.days} 天）")


def main():
    """主函數"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        # 創建應用程序工廠
        with get_app_factory(config_file=args.config) as app_factory:
            logger = app_factory.get_logger()
            
            # 設置日誌級別
            if hasattr(args, 'log_level'):
                logger.set_level(args.log_level)
            
            logger.info(f"執行命令: {args.command}")
            
            # 執行相應命令
            if args.command == 'predict':
                predict_stock_price(app_factory, args)
            
            elif args.command == 'train':
                train_model(app_factory, args)
            
            elif args.command == 'health-check':
                health_check(app_factory)
            
            elif args.command == 'config':
                config_management(app_factory, args)
            
            elif args.command == 'data':
                data_management(app_factory, args)
            
            else:
                print(f"未知命令: {args.command}")
                return 1
            
            logger.info("命令執行完成")
            return 0
    
    except StockAnalysisException as e:
        print(f"應用程序錯誤: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1
    
    except KeyboardInterrupt:
        print("\n操作被用戶中斷")
        return 130
    
    except Exception as e:
        print(f"未預期的錯誤: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())