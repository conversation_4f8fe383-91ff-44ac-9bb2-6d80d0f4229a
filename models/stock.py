from datetime import datetime

from sqlalchemy import Column, DateTime, Float, Foreign<PERSON>ey, Integer, String, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class Stock(Base):
    """股票基本信息表"""

    __tablename__ = "stocks"

    id = Column(Integer, primary_key=True)
    stock_id = Column(String(10), unique=True, nullable=False, index=True)
    name = Column(String(50), nullable=False)
    industry = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联
    daily_prices = relationship("DailyPrice", back_populates="stock")
    news = relationship("News", back_populates="stock")


class DailyPrice(Base):
    """股票每日价格表"""

    __tablename__ = "daily_prices"

    id = Column(Integer, primary_key=True)
    stock_id = Column(String(10), ForeignKey("stocks.stock_id"), nullable=False)
    date = Column(DateTime, nullable=False)
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    adj_close = Column(Float)
    volume = Column(Integer)
    turnover = Column(Float)
    change = Column(Float)
    change_percent = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

    # 关联
    stock = relationship("Stock", back_populates="daily_prices")

    # 复合索引
    __table_args__ = ({"sqlite_autoincrement": True},)


class News(Base):
    """股票新闻表"""

    __tablename__ = "news"

    id = Column(Integer, primary_key=True)
    stock_id = Column(String(10), ForeignKey("stocks.stock_id"), nullable=False)
    title = Column(String(200), nullable=False)
    content = Column(String)
    source = Column(String(50))
    url = Column(String(200))
    published_at = Column(DateTime)
    sentiment_score = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

    # 关联
    stock = relationship("Stock", back_populates="news")

    # 复合索引
    __table_args__ = ({"sqlite_autoincrement": True},)


class Watchlist(Base):
    """观察清单表"""

    __tablename__ = "watchlists"

    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    description = Column(String(200))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联
    items = relationship("WatchlistItem", back_populates="watchlist")


class WatchlistItem(Base):
    """观察清单项目表"""

    __tablename__ = "watchlist_items"

    id = Column(Integer, primary_key=True)
    watchlist_id = Column(Integer, ForeignKey("watchlists.id"), nullable=False)
    stock_id = Column(String(10), ForeignKey("stocks.stock_id"), nullable=False)
    note = Column(String(200))
    created_at = Column(DateTime, default=datetime.now)

    # 关联
    watchlist = relationship("Watchlist", back_populates="items")
    stock = relationship("Stock")

    # 复合索引
    __table_args__ = ({"sqlite_autoincrement": True},)
