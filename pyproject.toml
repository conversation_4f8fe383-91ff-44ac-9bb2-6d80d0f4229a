[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tw-stock-prediction"
version = "1.0.0"
description = "台灣股票預測系統 - 整合新聞分析與技術指標的智能預測平台"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "Stock Prediction Team", email = "<EMAIL>"}
]
keywords = ["stock", "prediction", "taiwan", "machine-learning", "finance"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial :: Investment",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "pandas>=1.5.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "joblib>=1.3.0",
    "yfinance>=0.2.0",
    "twstock>=1.3.1",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "selenium>=4.15.0",
    "jieba>=0.42.1",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.17.0",
    "flask>=2.3.0",
    "flask-cors>=4.0.0",
    "python-dotenv>=1.0.0",
    "PyYAML>=6.0.1",
    "SQLAlchemy>=2.0.0",
    "python-dateutil>=2.8.2",
    "scipy>=1.11.0",
    "tqdm>=4.66.0",
    "httpx>=0.25.0",
    "aiohttp>=3.9.0",
    "FinLab>=0.4.6",
    "TA-Lib>=0.6.4",
    "pydantic>=2.4.0",
    "pydantic-settings>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
    "bandit>=1.7.5",
    "pydocstyle>=6.3.0",
    "coverage>=7.3.0",
]

testing = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "coverage>=7.3.0",
    "factory-boy>=3.3.0",
    "freezegun>=1.2.0",
]

docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
    "sphinx-autodoc-typehints>=1.24.0",
]

production = [
    "gunicorn>=21.2.0",
    "redis>=5.0.0",
    "psycopg2-binary>=2.9.0",
    "sentry-sdk>=1.32.0",
]

[project.urls]
Homepage = "https://github.com/your-username/tw-stock-prediction"
Repository = "https://github.com/your-username/tw-stock-prediction.git"
Documentation = "https://tw-stock-prediction.readthedocs.io"
"Bug Tracker" = "https://github.com/your-username/tw-stock-prediction/issues"

[project.scripts]
tw-stock = "main:main"
tw-stock-web = "web_app:main"

[tool.setuptools.packages.find]
where = ["."]  # 在當前目錄查找包
exclude = ["tests*", "docs*", "build*", "dist*", "*.egg-info*"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.txt", "*.md"]

# Black 配置
[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目錄
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

# isort 配置
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy 配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "twstock.*",
    "yfinance.*",
    "FinLab.*",
    "talib.*",
    "jieba.*",
]
ignore_missing_imports = true

# Pytest 配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "api: marks tests that require API access",
    "database: marks tests that require database access",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

# Coverage 配置
[tool.coverage.run]
source = ["."]  # 覆蓋率檢查的源碼目錄
omit = [
    "tests/*",
    "*/tests/*",
    "setup.py",
    "*/site-packages/*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
    "*/build/*",
    "*/dist/*",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
ignore_errors = true
show_missing = true
skip_covered = false
skip_empty = false
sort = "Cover"

[tool.coverage.html]
directory = "htmlcov"

# Bandit 配置（安全檢查）
[tool.bandit]
exclude_dirs = ["tests", "test_*.py", "*_test.py"]
skips = ["B101", "B601"]  # 跳過 assert 和 shell 注入檢查（在測試中常見）

# Pydocstyle 配置
[tool.pydocstyle]
convention = "google"
add-ignore = ["D100", "D101", "D102", "D103", "D104", "D105", "D107"]