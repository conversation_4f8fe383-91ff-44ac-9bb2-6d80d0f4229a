# 核心數據處理
pandas>=1.5.0
numpy>=1.24.0
# sqlite3 是 Python 內建模組，不需要在 requirements.txt 中

# 機器學習
scikit-learn>=1.3.0
joblib>=1.3.0

# 股票數據獲取
yfinance>=0.2.0
twstock>=1.3.1
requests>=2.31.0

# 網頁爬蟲
beautifulsoup4>=4.12.0
lxml>=4.9.0
selenium>=4.15.0

# 中文文本處理
jieba>=0.42.1

# 數據可視化
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0

# Web框架
flask>=2.3.0
flask-cors>=4.0.0

# 日誌和配置
python-dotenv>=1.0.0
PyYAML>=6.0.1

# 數據庫
SQLAlchemy>=2.0.0

# 時間處理
python-dateutil>=2.8.2

# 數值計算
scipy>=1.11.0

# 進度條
tqdm>=4.66.0

# HTTP客戶端
httpx>=0.25.0

# 異步處理
aiohttp>=3.9.0

# 金融數據
FinLab>=0.4.6
TA-Lib>=0.6.4

# 開發工具
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.4.0

# 配置管理
pydantic>=2.4.0
pydantic-settings>=2.0.0