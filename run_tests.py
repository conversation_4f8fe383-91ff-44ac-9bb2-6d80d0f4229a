#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試運行腳本
用於執行所有單元測試和整合測試
"""

import argparse
import os
import sys
import unittest
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def discover_tests(test_dir="tests", pattern="test_*.py"):
    """
    發現並載入測試

    Args:
        test_dir: 測試目錄
        pattern: 測試文件模式

    Returns:
        TestSuite: 測試套件
    """
    loader = unittest.TestLoader()
    start_dir = project_root / test_dir

    if not start_dir.exists():
        print(f"測試目錄不存在: {start_dir}")
        return unittest.TestSuite()

    suite = loader.discover(
        start_dir=str(start_dir), pattern=pattern, top_level_dir=str(project_root)
    )

    return suite


def run_specific_test(test_module, test_class=None, test_method=None):
    """
    運行特定測試

    Args:
        test_module: 測試模組名稱
        test_class: 測試類別名稱（可選）
        test_method: 測試方法名稱（可選）

    Returns:
        TestResult: 測試結果
    """
    loader = unittest.TestLoader()

    if test_method and test_class:
        # 運行特定測試方法
        suite = loader.loadTestsFromName(f"{test_module}.{test_class}.{test_method}")
    elif test_class:
        # 運行特定測試類別
        suite = loader.loadTestsFromName(f"{test_module}.{test_class}")
    else:
        # 運行整個測試模組
        suite = loader.loadTestsFromName(test_module)

    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(suite)


def run_all_tests(verbosity=2, failfast=False):
    """
    運行所有測試

    Args:
        verbosity: 詳細程度 (0-2)
        failfast: 是否在第一個失敗時停止

    Returns:
        TestResult: 測試結果
    """
    print("正在發現測試...")
    suite = discover_tests()

    test_count = suite.countTestCases()
    print(f"發現 {test_count} 個測試")

    if test_count == 0:
        print("沒有找到任何測試")
        return None

    print("\n開始運行測試...")
    print("=" * 70)

    runner = unittest.TextTestRunner(
        verbosity=verbosity, failfast=failfast, buffer=True  # 捕獲 stdout/stderr
    )

    result = runner.run(suite)

    print("\n" + "=" * 70)
    print("測試結果摘要:")
    print(f"運行測試數: {result.testsRun}")
    print(f"失敗數: {len(result.failures)}")
    print(f"錯誤數: {len(result.errors)}")
    print(f"跳過數: {len(result.skipped)}")

    if result.failures:
        print("\n失敗的測試:")
        for test, traceback in result.failures:
            print(f"  - {test}")

    if result.errors:
        print("\n錯誤的測試:")
        for test, traceback in result.errors:
            print(f"  - {test}")

    if result.skipped:
        print("\n跳過的測試:")
        for test, reason in result.skipped:
            print(f"  - {test}: {reason}")

    success_rate = (
        (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    )
    print(f"\n成功率: {success_rate:.1f}%")

    return result


def run_unit_tests_only():
    """
    只運行單元測試（排除整合測試）
    """
    print("運行單元測試...")
    suite = discover_tests(pattern="test_*.py")

    # 過濾掉整合測試
    filtered_suite = unittest.TestSuite()

    for test_group in suite:
        for test_case in test_group:
            # 跳過包含 'Integration' 的測試類別
            if "Integration" not in test_case.__class__.__name__:
                filtered_suite.addTest(test_case)

    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(filtered_suite)


def run_integration_tests_only():
    """
    只運行整合測試
    """
    print("運行整合測試...")
    suite = discover_tests(pattern="test_*.py")

    # 只保留整合測試
    filtered_suite = unittest.TestSuite()

    for test_group in suite:
        for test_case in test_group:
            # 只包含包含 'Integration' 的測試類別
            if "Integration" in test_case.__class__.__name__:
                filtered_suite.addTest(test_case)

    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(filtered_suite)


def check_test_coverage():
    """
    檢查測試覆蓋率（需要安裝 coverage 套件）
    """
    try:
        import coverage

        print("開始測試覆蓋率分析...")

        # 創建覆蓋率對象
        cov = coverage.Coverage()
        cov.start()

        # 運行測試
        suite = discover_tests()
        runner = unittest.TextTestRunner(verbosity=0)
        result = runner.run(suite)

        # 停止覆蓋率收集
        cov.stop()
        cov.save()

        print("\n覆蓋率報告:")
        cov.report()

        # 生成 HTML 報告
        html_dir = project_root / "htmlcov"
        cov.html_report(directory=str(html_dir))
        print(f"\nHTML 覆蓋率報告已生成: {html_dir}/index.html")

        return result

    except ImportError:
        print("錯誤: 需要安裝 coverage 套件")
        print("請運行: pip install coverage")
        return None


def main():
    """
    主函數
    """
    parser = argparse.ArgumentParser(description="台灣股票分析系統測試運行器")

    parser.add_argument(
        "--test-type",
        choices=["all", "unit", "integration", "coverage"],
        default="all",
        help="測試類型 (默認: all)",
    )

    parser.add_argument("--module", help="運行特定測試模組 (例如: tests.test_price_collector)")

    parser.add_argument("--class", dest="test_class", help="運行特定測試類別")

    parser.add_argument("--method", help="運行特定測試方法")

    parser.add_argument(
        "--verbosity",
        "-v",
        type=int,
        choices=[0, 1, 2],
        default=2,
        help="詳細程度 (0=安靜, 1=正常, 2=詳細)",
    )

    parser.add_argument("--failfast", "-f", action="store_true", help="在第一個失敗時停止")

    args = parser.parse_args()

    print("台灣股票分析系統 - 測試運行器")
    print("=" * 50)

    # 檢查測試目錄是否存在
    test_dir = project_root / "tests"
    if not test_dir.exists():
        print(f"錯誤: 測試目錄不存在 {test_dir}")
        sys.exit(1)

    result = None

    try:
        if args.module:
            # 運行特定測試
            print(f"運行特定測試: {args.module}")
            if args.test_class or args.method:
                print(f"類別: {args.test_class}, 方法: {args.method}")

            result = run_specific_test(args.module, args.test_class, args.method)

        elif args.test_type == "unit":
            result = run_unit_tests_only()

        elif args.test_type == "integration":
            result = run_integration_tests_only()

        elif args.test_type == "coverage":
            result = check_test_coverage()

        else:  # all
            result = run_all_tests(verbosity=args.verbosity, failfast=args.failfast)

        # 根據測試結果設置退出碼
        if result and (result.failures or result.errors):
            sys.exit(1)
        else:
            sys.exit(0)

    except KeyboardInterrupt:
        print("\n測試被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n運行測試時發生錯誤: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
