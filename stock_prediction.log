2025-06-25 13:30:40,841 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:30:40,841 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:30:40,842 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:32:28,603 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:32:28,604 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:32:28,604 - DataDownloaderAPI - INFO - DataDownloader<PERSON><PERSON> initialized.
2025-06-25 13:32:28,605 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:32:28,609 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:32:28,609 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:32:28,609 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:32:28,610 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:32:28,610 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:32:28,610 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:32:28,610 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:32:28,610 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:32:28,610 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:32:28,610 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:32:28,613 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:32:28,614 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:32:28,646 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*********:8889
2025-06-25 13:32:28,647 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 13:32:28,647 - werkzeug - INFO -  * Restarting with stat
2025-06-25 13:32:30,064 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:32:30,064 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:32:30,064 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:32:30,066 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:32:30,067 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:32:30,068 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:32:30,068 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:32:30,068 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:32:30,068 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:32:30,068 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:32:30,068 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:32:30,068 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:32:30,068 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:32:30,068 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:32:30,073 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:32:30,073 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:32:30,093 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 13:32:30,101 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 13:34:37,461 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:37] "GET / HTTP/1.1" 200 -
2025-06-25 13:34:37,463 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:37] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 13:34:37,494 - app - ERROR - 获取大盘指数错误: no such column: close_price
2025-06-25 13:34:37,494 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:37] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 13:34:37,499 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:37] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 13:34:38,302 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:38] "GET / HTTP/1.1" 200 -
2025-06-25 13:34:38,309 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:38] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 13:34:38,336 - app - ERROR - 获取大盘指数错误: no such column: close_price
2025-06-25 13:34:38,336 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:38] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 13:34:38,337 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:38] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 13:34:42,560 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:34:42] "GET /download HTTP/1.1" 200 -
2025-06-25 13:35:19,163 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:35:19] "GET /download HTTP/1.1" 200 -
2025-06-25 13:36:36,987 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:36] "GET / HTTP/1.1" 200 -
2025-06-25 13:36:36,988 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:36] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 13:36:36,996 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:36] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 13:36:37,004 - app - ERROR - 获取大盘指数错误: no such column: close_price
2025-06-25 13:36:37,005 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:37] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 13:36:37,005 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:37] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 13:36:45,429 - app - WARNING - 404错误: /train
2025-06-25 13:36:45,430 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:45] "[33mGET /train HTTP/1.1[0m" 404 -
2025-06-25 13:36:46,762 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:46] "GET / HTTP/1.1" 200 -
2025-06-25 13:36:46,767 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:46] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 13:36:46,774 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:46] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 13:36:46,781 - app - ERROR - 获取大盘指数错误: no such column: close_price
2025-06-25 13:36:46,781 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:46] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 13:36:46,781 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:46] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 13:36:50,266 - app - WARNING - 404错误: /topics
2025-06-25 13:36:50,267 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:50] "[33mGET /topics HTTP/1.1[0m" 404 -
2025-06-25 13:36:51,311 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:51] "GET / HTTP/1.1" 200 -
2025-06-25 13:36:51,313 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:51] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 13:36:51,321 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:51] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 13:36:51,328 - app - ERROR - 获取大盘指数错误: no such column: close_price
2025-06-25 13:36:51,329 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:51] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 13:36:51,329 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:51] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 13:36:54,208 - app - WARNING - 404错误: /api/docs
2025-06-25 13:36:54,209 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 13:36:54] "[33mGET /api/docs HTTP/1.1[0m" 404 -
2025-06-25 13:40:43,931 - werkzeug - INFO -  * Detected change in '/Users/<USER>/python/training/stock/main-news/models/stock.py', reloading
2025-06-25 13:40:44,181 - werkzeug - INFO -  * Restarting with stat
2025-06-25 13:40:46,249 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:40:46,249 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:40:46,249 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:40:46,252 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:40:46,253 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:40:46,254 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:40:46,254 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:40:46,254 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:40:46,254 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:40:46,254 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:40:46,254 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:40:46,254 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:40:46,254 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:40:46,254 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:40:46,258 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:40:46,258 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:40:46,282 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 13:40:46,294 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 13:41:00,788 - werkzeug - INFO -  * Detected change in '/Users/<USER>/python/training/stock/main-news/utils/db_operations.py', reloading
2025-06-25 13:41:01,046 - werkzeug - INFO -  * Restarting with stat
2025-06-25 13:41:02,857 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:41:02,857 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:41:02,857 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:41:02,859 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:41:02,860 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:41:02,861 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:41:02,861 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:41:02,861 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:41:02,862 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:41:02,862 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:41:02,862 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:41:02,862 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:41:02,862 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:41:02,862 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:41:02,865 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:41:02,865 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:41:02,888 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 13:41:02,904 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 13:41:48,158 - werkzeug - INFO -  * Detected change in '/Users/<USER>/python/training/stock/main-news/web_app.py', reloading
2025-06-25 13:41:48,402 - werkzeug - INFO -  * Restarting with stat
2025-06-25 13:41:49,787 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:41:49,787 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:41:49,787 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:41:49,789 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:41:49,790 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:41:49,791 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:41:49,791 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:41:49,791 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:41:49,791 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:41:49,791 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:41:49,791 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:41:49,791 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:41:49,791 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:41:49,791 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:41:49,794 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:41:49,794 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:41:49,816 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 13:41:49,824 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 13:42:55,101 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:42:55,101 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:42:55,101 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:42:55,103 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:42:55,103 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:42:55,104 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:42:55,104 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:42:55,104 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:42:55,104 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:42:55,104 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:42:55,104 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:42:55,104 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:42:55,104 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:42:55,104 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:42:55,108 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:42:55,108 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:42:55,143 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*********:8889
2025-06-25 13:42:55,143 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 13:42:55,143 - werkzeug - INFO -  * Restarting with stat
2025-06-25 13:42:56,512 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:42:56,512 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:42:56,512 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:42:56,514 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:42:56,515 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:42:56,516 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:42:56,516 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:42:56,516 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:42:56,516 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:42:56,516 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:42:56,516 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:42:56,516 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:42:56,516 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:42:56,516 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:42:56,519 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:42:56,519 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:42:56,539 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 13:42:56,546 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 13:44:38,385 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:44:38,386 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:44:38,386 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:44:38,387 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:44:38,388 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:44:38,388 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:44:38,392 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:44:38,392 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:44:38,418 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*********:8889
2025-06-25 13:44:38,418 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 13:44:38,418 - werkzeug - INFO -  * Restarting with stat
2025-06-25 13:44:39,709 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:44:39,709 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:44:39,709 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:44:39,711 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:44:39,711 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:44:39,712 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:44:39,712 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:44:39,712 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:44:39,712 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:44:39,712 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:44:39,712 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:44:39,712 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:44:39,712 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:44:39,712 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:44:39,716 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:44:39,716 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:44:39,735 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 13:44:39,742 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 13:47:50,737 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:47:50,737 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:47:50,738 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:47:50,739 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:47:50,741 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:47:50,741 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:47:50,746 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:47:50,746 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:47:50,774 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*********:8889
2025-06-25 13:47:50,774 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 13:47:50,774 - werkzeug - INFO -  * Restarting with stat
2025-06-25 13:47:52,183 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:47:52,183 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:47:52,183 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:47:52,185 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:47:52,186 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:47:52,186 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:47:52,186 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:47:52,186 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:47:52,186 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:47:52,186 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:47:52,186 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:47:52,186 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:47:52,187 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:47:52,187 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:47:52,189 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:47:52,189 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:47:52,211 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 13:47:52,222 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 13:48:45,834 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:48:45,834 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:48:45,834 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:48:45,836 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:48:45,836 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:48:45,837 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:48:45,837 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:48:45,837 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:48:45,837 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:48:45,837 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:48:45,837 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:48:45,837 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:48:45,837 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:48:45,837 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:48:45,841 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:48:45,841 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:48:45,870 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*********:8889
2025-06-25 13:48:45,870 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 13:48:45,871 - werkzeug - INFO -  * Restarting with stat
2025-06-25 13:48:47,284 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:48:47,284 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:48:47,284 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 13:48:47,286 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 13:48:47,287 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 13:48:47,287 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 13:48:47,290 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 13:48:47,290 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 13:48:47,312 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 13:48:47,320 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 14:19:00,006 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:19:00,006 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:19:00,006 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:19:00,008 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:19:00,010 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:19:00,010 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:19:00,010 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:19:00,010 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:19:00,010 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:19:00,010 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:19:00,010 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:19:00,010 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:19:00,011 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:19:00,011 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:19:00,014 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:19:00,014 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 14:19:00,048 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*********:8889
2025-06-25 14:19:00,048 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 14:19:00,049 - werkzeug - INFO -  * Restarting with stat
2025-06-25 14:19:01,440 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:19:01,440 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:19:01,440 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:19:01,442 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:19:01,442 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:19:01,443 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:19:01,443 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:19:01,443 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:19:01,443 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:19:01,443 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:19:01,443 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:19:01,443 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:19:01,443 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:19:01,443 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:19:01,446 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:19:01,447 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 14:19:01,467 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 14:19:01,476 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 14:21:16,670 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:21:16,670 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:21:16,670 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:21:16,672 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:21:16,674 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:21:16,674 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:21:16,675 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:21:16,675 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:21:16,675 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:21:16,675 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:21:16,675 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:21:16,675 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:21:16,675 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:21:16,675 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:21:16,678 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:21:16,679 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 14:21:16,747 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*********:8889
2025-06-25 14:21:16,747 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 14:21:16,748 - werkzeug - INFO -  * Restarting with stat
2025-06-25 14:21:18,108 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:21:18,108 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:21:18,108 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:21:18,109 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:21:18,110 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:21:18,111 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:21:18,111 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:21:18,111 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:21:18,111 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:21:18,111 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:21:18,111 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:21:18,111 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:21:18,111 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:21:18,111 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:21:18,114 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:21:18,114 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 14:21:18,133 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 14:21:18,140 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 14:22:13,362 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:22:13,362 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:22:13,362 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:22:13,364 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:22:13,365 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:22:13,365 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:22:13,366 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:22:13,366 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:22:13,366 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:22:13,366 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:22:13,366 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:22:13,366 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:22:13,366 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:22:13,366 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:22:13,369 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:22:13,369 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 14:22:13,395 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*********:8889
2025-06-25 14:22:13,396 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 14:22:13,396 - werkzeug - INFO -  * Restarting with stat
2025-06-25 14:22:14,767 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:22:14,767 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:22:14,767 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:22:14,768 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:22:14,769 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:22:14,770 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:22:14,770 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:22:14,770 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:22:14,770 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:22:14,770 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:22:14,770 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:22:14,770 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:22:14,770 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:22:14,770 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:22:14,773 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:22:14,773 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 14:22:14,793 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 14:22:14,801 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 14:22:17,563 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:17] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:17,591 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:17] "GET /static/sample_analysis.png HTTP/1.1" 200 -
2025-06-25 14:22:18,159 - app - WARNING - 台灣加權指數數據不可用，使用台積電作為市場代表
2025-06-25 14:22:18,161 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:18] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 14:22:18,164 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:18] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 14:22:20,603 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:20] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:20,621 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:20] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:20,665 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:20] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 14:22:20,717 - app - WARNING - 台灣加權指數數據不可用，使用台積電作為市場代表
2025-06-25 14:22:20,718 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:20] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 14:22:20,719 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:20] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 14:22:26,294 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:26] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:26,310 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:26] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:26,342 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:26] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 14:22:26,396 - app - WARNING - 台灣加權指數數據不可用，使用台積電作為市場代表
2025-06-25 14:22:26,397 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:26] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 14:22:26,397 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:26] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 14:22:29,651 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:29] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:29,666 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:29] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:29,699 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:29] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 14:22:29,753 - app - WARNING - 台灣加權指數數據不可用，使用台積電作為市場代表
2025-06-25 14:22:29,754 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:29] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 14:22:29,754 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:29] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 14:22:40,147 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:40] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:40,158 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:40] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:40,227 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:40] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 14:22:40,270 - app - WARNING - 台灣加權指數數據不可用，使用台積電作為市場代表
2025-06-25 14:22:40,273 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:40] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 14:22:40,274 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:40] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 14:22:58,930 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:58] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:58,939 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:58] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:22:59,067 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:59] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 14:22:59,102 - app - WARNING - 台灣加權指數數據不可用，使用台積電作為市場代表
2025-06-25 14:22:59,103 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:59] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 14:22:59,104 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:22:59] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 14:23:04,909 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:23:04] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:23:04,917 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:23:04] "GET /?ide_webview_request_time=1750837937553 HTTP/1.1" 200 -
2025-06-25 14:23:04,949 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:23:04] "[36mGET /static/sample_analysis.png HTTP/1.1[0m" 304 -
2025-06-25 14:23:05,003 - app - WARNING - 台灣加權指數數據不可用，使用台積電作為市場代表
2025-06-25 14:23:05,004 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:23:05] "GET /api/market_summary/twii HTTP/1.1" 200 -
2025-06-25 14:23:05,005 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 14:23:05] "GET /api/market_summary/hot_stocks HTTP/1.1" 200 -
2025-06-25 14:52:25,601 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:52:25,601 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:52:25,601 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:52:25,603 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:52:25,604 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:52:25,605 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:52:25,605 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:52:25,605 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:52:25,605 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:52:25,605 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:52:25,605 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:52:25,605 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:52:25,605 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:52:25,605 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:52:25,608 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:52:25,609 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 14:52:25,637 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8889
 * Running on http://*************:8889
2025-06-25 14:52:25,637 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 14:52:25,638 - werkzeug - INFO -  * Restarting with stat
2025-06-25 14:52:26,881 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:52:26,881 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:52:26,881 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:52:26,883 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:52:26,883 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:52:26,884 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:52:26,884 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:52:26,884 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:52:26,884 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:52:26,884 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:52:26,884 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:52:26,884 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:52:26,884 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:52:26,884 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:52:26,887 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:52:26,887 - app - INFO - Web应用启动于 http://localhost:8889
2025-06-25 14:52:26,907 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 14:52:26,914 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 14:52:57,696 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:52:57,696 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:52:57,696 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:52:57,698 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:24,350 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:24,350 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:24,350 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:53:24,355 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:24,357 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:53:24,361 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:53:24,361 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:53:24,361 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:53:24,361 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:53:24,361 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:53:24,362 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:53:24,362 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:53:24,362 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:53:24,362 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:53:24,367 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:53:24,367 - app - INFO - Web应用启动于 http://localhost:8890
2025-06-25 14:53:24,462 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8890
 * Running on http://*********:8890
2025-06-25 14:53:24,462 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 14:53:24,463 - werkzeug - INFO -  * Restarting with stat
2025-06-25 14:53:25,855 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:25,855 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:25,855 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:53:25,856 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:25,857 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:53:25,858 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:53:25,858 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:53:25,858 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:53:25,858 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:53:25,858 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:53:25,858 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:53:25,858 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:53:25,858 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:53:25,858 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:53:25,861 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:53:25,861 - app - INFO - Web应用启动于 http://localhost:8890
2025-06-25 14:53:25,881 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 14:53:25,890 - werkzeug - INFO -  * Debugger PIN: 724-594-995
2025-06-25 14:53:55,526 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:55,526 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:55,526 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:53:55,528 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:55,529 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:53:55,530 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:53:55,530 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:53:55,530 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:53:55,530 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:53:55,530 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:53:55,530 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:53:55,530 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:53:55,530 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:53:55,530 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:53:55,533 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:53:55,534 - app - INFO - Web应用启动于 http://localhost:8890
2025-06-25 14:53:55,560 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8890
 * Running on http://*********:8890
2025-06-25 14:53:55,560 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 14:53:55,560 - werkzeug - INFO -  * Restarting with stat
2025-06-25 14:53:56,907 - DataDownloaderAPI - INFO - Initializing DataDownloaderAPI with database: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:56,907 - DataDownloader - INFO - 初始化下載器，使用資料庫路徑: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:56,907 - DataDownloaderAPI - INFO - DataDownloaderAPI initialized.
2025-06-25 14:53:56,909 - utils.db_manager - INFO - 数据库连接池初始化完成: /Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db
2025-06-25 14:53:56,909 - DownloadRoutes - INFO - 註冊下載路由，Blueprint: download
2025-06-25 14:53:56,910 - DownloadRoutes - INFO - 應用已註冊的路由:
2025-06-25 14:53:56,910 - DownloadRoutes - INFO - static: /static/<path:filename>
2025-06-25 14:53:56,910 - DownloadRoutes - INFO - download.download_page: /download
2025-06-25 14:53:56,910 - DownloadRoutes - INFO - download.login_api: /api/login
2025-06-25 14:53:56,910 - DownloadRoutes - INFO - download.start_download: /api/start_download
2025-06-25 14:53:56,910 - DownloadRoutes - INFO - download.get_download_status: /api/download_status
2025-06-25 14:53:56,910 - DownloadRoutes - INFO - download.check_api_key: /api/check_api_key
2025-06-25 14:53:56,910 - DownloadRoutes - INFO - download.get_download_options: /api/download_options
2025-06-25 14:53:56,910 - WatchlistRoutes - INFO - 註冊觀察清單路由，Blueprint: watchlist
2025-06-25 14:53:56,913 - utils.db_manager - INFO - 数据库表结构初始化完成
2025-06-25 14:53:56,913 - app - INFO - Web应用启动于 http://localhost:8890
2025-06-25 14:53:56,933 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 14:53:56,941 - werkzeug - INFO -  * Debugger PIN: 724-594-995
