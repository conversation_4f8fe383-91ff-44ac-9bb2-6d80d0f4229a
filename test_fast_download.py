#!/usr/bin/env python3
"""
快速下載測試腳本
測試新的優化下載功能
"""

import os
import sqlite3
import sys
import time
from datetime import datetime

# 將專案根目錄添加到 Python 路徑
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, PROJECT_ROOT)


def check_database_status():
    """檢查資料庫狀態"""
    db_path = os.path.join(PROJECT_ROOT, "database", "tw_stock_data.db")

    if not os.path.exists(db_path):
        print("❌ 資料庫文件不存在")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 檢查股票數量
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM daily_prices")
        stock_count = cursor.fetchone()[0]

        # 檢查數據筆數
        cursor.execute("SELECT COUNT(*) FROM daily_prices")
        total_records = cursor.fetchone()[0]

        # 檢查最新日期
        cursor.execute("SELECT MAX(date) FROM daily_prices")
        latest_date = cursor.fetchone()[0]

        # 檢查最早日期
        cursor.execute("SELECT MIN(date) FROM daily_prices")
        earliest_date = cursor.fetchone()[0]

        print(f"📊 資料庫狀態:")
        print(f"   股票數量: {stock_count} 支")
        print(f"   總記錄數: {total_records:,} 筆")
        print(f"   日期範圍: {earliest_date} ~ {latest_date}")

        # 檢查最近更新的股票
        cursor.execute(
            """
            SELECT stock_id, COUNT(*) as records, MAX(date) as latest_date 
            FROM daily_prices 
            GROUP BY stock_id 
            ORDER BY latest_date DESC 
            LIMIT 5
        """
        )
        recent_stocks = cursor.fetchall()

        print(f"\n📈 最近更新的股票:")
        for stock_id, records, latest_date in recent_stocks:
            print(f"   {stock_id}: {records} 筆記錄, 最新: {latest_date}")

        conn.close()

    except Exception as e:
        print(f"❌ 檢查資料庫時發生錯誤: {e}")


def test_daily_update():
    """測試日常快速更新"""
    print("\n🚀 測試日常快速更新...")
    start_time = time.time()

    # 測試少量股票的日常更新
    test_stocks = ["2330", "2317", "2454", "0050", "2881"]

    try:
        from main import StockAnalysisApp

        app = StockAnalysisApp()
        app.update_data(stock_ids=test_stocks, daily_only=True)
        app.close()

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ 日常更新完成，耗時: {duration:.2f} 秒")

    except Exception as e:
        print(f"❌ 日常更新失敗: {e}")


def test_finlab_download():
    """測試FinLab快速下載"""
    print("\n⚡ 測試FinLab快速下載...")

    try:
        # 檢查FinLab是否可用
        try:
            import finlab

            print("✅ FinLab 模組可用")
        except ImportError:
            print("❌ FinLab 模組未安裝")
            print("   請執行: pip install finlab")
            return

        start_time = time.time()

        from data_collectors.finlab_collector import FinLabCollector

        collector = FinLabCollector()

        # 測試下載少量股票
        test_stocks = ["2330", "2317", "2454"]
        success = collector.download_historical_data_finlab(test_stocks, years=1)

        collector.close()

        end_time = time.time()
        duration = end_time - start_time

        if success:
            print(f"✅ FinLab下載完成，耗時: {duration:.2f} 秒")
        else:
            print(f"❌ FinLab下載失敗")

    except Exception as e:
        print(f"❌ FinLab測試失敗: {e}")


def test_incremental_download():
    """測試增量下載功能"""
    print("\n🔄 測試增量下載功能...")

    try:
        # 檢查FinLab是否可用
        try:
            import finlab

            print("✅ FinLab 模組可用")
        except ImportError:
            print("❌ FinLab 模組未安裝")
            print("   請執行: pip install finlab")
            return

        from data_collectors.finlab_collector import FinLabCollector

        collector = FinLabCollector()

        # 檢查資料庫狀態
        print("\n📊 檢查資料庫狀態...")
        status = collector.get_database_status()
        if status:
            print(f"   股票數量: {status['stock_count']} 支")
            print(f"   總記錄數: {status['total_records']:,} 筆")
            print(f"   日期範圍: {status['date_range'][0]} ~ {status['date_range'][1]}")

        # 建議需要補充的股票
        print("\n💡 分析缺失數據...")
        suggestions = collector.suggest_missing_data()
        if suggestions:
            print(f"   發現 {len(suggestions)} 支股票可能需要補充數據")
            print(f"   前10支: {suggestions[:10]}")
        else:
            print("   所有股票數據都比較完整")

        collector.close()

    except Exception as e:
        print(f"❌ 增量下載測試失敗: {e}")


def show_usage_examples():
    """顯示使用範例"""
    print("\n📖 新的使用方式:")
    print("\n1. 日常快速更新（推薦，每日執行）:")
    print("   python main.py --update")
    print("   # 只更新當日數據，速度快")

    print("\n2. 檢查缺失數據:")
    print("   python main.py --check-missing")
    print("   # 分析資料庫狀態，建議需要補充的股票")

    print("\n3. 使用FinLab增量下載（推薦）:")
    print("   python main.py --download-finlab")
    print("   # 只下載缺失的數據，速度快且智能")

    print("\n4. 下載指定股票的缺失數據:")
    print("   python main.py --download-finlab --stock-ids 2330 2317 2454")

    print("\n5. FinLab完整下載（重新下載所有數據）:")
    print("   python main.py --download-finlab-full")

    print("\n6. 完整更新（較慢，偶爾使用）:")
    print("   python main.py --update-full")

    print("\n7. 傳統API下載（備用方案）:")
    print("   python main.py --download-historical --stock-ids 2330")


def main():
    """主測試函數"""
    print("🔍 股票數據下載優化測試")
    print("=" * 50)

    # 檢查當前資料庫狀態
    check_database_status()

    # 顯示使用範例
    show_usage_examples()

    # 詢問是否執行測試
    print("\n" + "=" * 50)
    choice = input("是否執行測試? (y/n): ").lower().strip()

    if choice == "y":
        # 測試日常更新
        test_daily_update()

        # 檢查更新後的狀態
        print("\n" + "-" * 30)
        check_database_status()

        # 詢問是否測試增量下載
        incremental_choice = input("\n是否測試增量下載分析? (y/n): ").lower().strip()
        if incremental_choice == "y":
            test_incremental_download()

        # 詢問是否測試FinLab
        finlab_choice = input("\n是否測試FinLab下載? (y/n): ").lower().strip()
        if finlab_choice == "y":
            test_finlab_download()

            # 再次檢查狀態
            print("\n" + "-" * 30)
            check_database_status()

    print("\n✨ 測試完成!")


if __name__ == "__main__":
    main()
