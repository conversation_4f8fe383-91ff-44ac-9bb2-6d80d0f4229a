# test_finlab_fixed.py
import finlab
from finlab import data
import os

def test_finlab_with_token():
    # 方法 1：直接使用 API 金鑰
    api_token = "ZTKJCEj2MN7/nTEi0cXe5XRXuY7PG9d1UxfEZ8qstWoxTM5Kd5QGj64DQRiIfH4y#vip_m"
    
    try:
        print("正在登入 FinLab...")
        finlab.login(api_token=api_token)
        print("✅ FinLab 登入成功！")
        
        # 測試下載一小部分資料
        print("正在測試資料下載...")
        close = data.get("price:收盤價")
        print(f"✅ 資料下載成功！資料形狀: {close.shape}")
        print(f"資料日期範圍: {close.index.min()} 至 {close.index.max()}")
        
        # 顯示幾個股票的最新價格
        latest_prices = close.iloc[-1].dropna().head(5)
        print("最新收盤價 (前5檔股票):")
        for stock, price in latest_prices.items():
            print(f"  {stock}: {price}")
            
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_finlab_with_file():
    # 方法 2：從檔案讀取 API 金鑰
    try:
        with open("api_key.txt", "r") as f:
            api_token = f.read().strip()
        
        print("從檔案讀取 API 金鑰...")
        finlab.login(api_token=api_token)
        print("✅ 使用檔案 API 金鑰登入成功！")
        return True
        
    except FileNotFoundError:
        print("❌ 找不到 api_key.txt 檔案")
        return False
    except Exception as e:
        print(f"❌ 使用檔案登入失敗: {e}")
        return False

if __name__ == "__main__":
    print("=== FinLab API 測試 ===")
    
    # 先測試直接使用 API 金鑰
    if test_finlab_with_token():
        print("\n✅ 直接使用 API 金鑰測試成功！")
    else:
        print("\n❌ 直接使用 API 金鑰測試失敗")
    
    print("\n" + "="*50)
    
    # 再測試從檔案讀取
    if test_finlab_with_file():
        print("\n✅ 從檔案讀取 API 金鑰測試成功！")
    else:
        print("\n❌ 從檔案讀取 API 金鑰測試失敗")