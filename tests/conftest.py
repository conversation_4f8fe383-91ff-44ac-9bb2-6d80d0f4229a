"""Pytest 配置文件和共用 fixtures。"""

import os
import sys
import tempfile
from pathlib import Path
from typing import Generator
from unittest.mock import Mock, patch

import pytest

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設置測試環境變數
os.environ["TESTING"] = "true"
os.environ["LOG_LEVEL"] = "DEBUG"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"


@pytest.fixture(scope="session")
def temp_dir() -> Generator[Path, None, None]:
    """創建臨時目錄用於測試。"""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture(scope="session")
def test_data_dir() -> Path:
    """測試數據目錄。"""
    return Path(__file__).parent / "fixtures"


@pytest.fixture
def mock_database():
    """模擬資料庫連接。"""
    with patch("sqlite3.connect") as mock_connect:
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn
        yield mock_conn


@pytest.fixture
def mock_requests():
    """模擬 HTTP 請求。"""
    with patch("requests.get") as mock_get:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"data": "test"}
        mock_response.text = "<html>test</html>"
        mock_get.return_value = mock_response
        yield mock_get


@pytest.fixture
def sample_stock_data():
    """範例股票數據。"""
    return {
        "symbol": "2330",
        "name": "台積電",
        "price": 500.0,
        "change": 5.0,
        "change_percent": 1.0,
        "volume": 1000000,
        "date": "2024-01-01",
    }


@pytest.fixture
def sample_news_data():
    """範例新聞數據。"""
    return {
        "title": "台積電股價上漲",
        "content": "台積電今日股價上漲5元，漲幅1%",
        "url": "https://example.com/news/1",
        "published_date": "2024-01-01",
        "source": "測試新聞網",
        "sentiment_score": 0.8,
    }


@pytest.fixture
def mock_logger():
    """模擬日誌記錄器。"""
    with patch("utils.logger.get_app_logger") as mock_get_logger:
        mock_logger_instance = Mock()
        mock_get_logger.return_value = mock_logger_instance
        yield mock_logger_instance


@pytest.fixture(autouse=True)
def setup_test_environment():
    """自動設置測試環境。"""
    # 設置測試環境變數
    test_env = {
        "FLASK_ENV": "testing",
        "DATABASE_URL": "sqlite:///:memory:",
        "LOG_LEVEL": "DEBUG",
        "CACHE_TYPE": "simple",
        "TESTING": "true",
    }

    # 保存原始環境變數
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value

    yield

    # 恢復原始環境變數
    for key, value in original_env.items():
        if value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = value


@pytest.fixture
def mock_finlab_api():
    """模擬 FinLab API。"""
    with patch("FinLab.data.get") as mock_get:
        import pandas as pd

        # 創建模擬數據
        mock_data = pd.DataFrame(
            {
                "open": [100, 101, 102],
                "high": [105, 106, 107],
                "low": [95, 96, 97],
                "close": [103, 104, 105],
                "volume": [1000, 1100, 1200],
            },
            index=pd.date_range("2024-01-01", periods=3),
        )

        mock_get.return_value = mock_data
        yield mock_get


@pytest.fixture
def mock_selenium_driver():
    """模擬 Selenium WebDriver。"""
    with patch("selenium.webdriver.Chrome") as mock_driver_class:
        mock_driver = Mock()
        mock_element = Mock()
        mock_element.text = "測試文本"
        mock_element.get_attribute.return_value = "https://example.com"

        mock_driver.find_elements.return_value = [mock_element]
        mock_driver.find_element.return_value = mock_element
        mock_driver.page_source = "<html>測試頁面</html>"

        mock_driver_class.return_value = mock_driver
        yield mock_driver


class TestBase:
    """測試基礎類別。"""

    def setup_method(self):
        """每個測試方法執行前的設置。"""
        pass

    def teardown_method(self):
        """每個測試方法執行後的清理。"""
        pass
