"""資料庫集成測試。"""

import sqlite3
import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from tests.conftest import TestBase


class TestDatabaseIntegration(TestBase):
    """資料庫集成測試。"""

    @pytest.fixture
    def temp_db(self):
        """創建臨時資料庫。"""
        with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as tmp_file:
            db_path = tmp_file.name

        # 創建資料庫連接
        conn = sqlite3.connect(db_path)
        yield conn, db_path

        # 清理
        conn.close()
        Path(db_path).unlink(missing_ok=True)

    def test_database_connection(self, temp_db):
        """測試資料庫連接。"""
        conn, db_path = temp_db

        # 測試連接是否正常
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()

        assert result == (1,)

    def test_create_tables(self, temp_db):
        """測試創建表格。"""
        conn, db_path = temp_db
        cursor = conn.cursor()

        # 創建測試表格
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS test_stocks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                name TEXT NOT NULL,
                price REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )
        conn.commit()

        # 檢查表格是否存在
        cursor.execute(
            """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='test_stocks'
        """
        )
        result = cursor.fetchone()

        assert result is not None
        assert result[0] == "test_stocks"

    def test_insert_and_query_data(self, temp_db):
        """測試插入和查詢數據。"""
        conn, db_path = temp_db
        cursor = conn.cursor()

        # 創建表格
        cursor.execute(
            """
            CREATE TABLE test_stocks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                name TEXT NOT NULL,
                price REAL
            )
        """
        )

        # 插入測試數據
        test_data = [
            ("2330", "台積電", 500.0),
            ("0050", "元大台灣50", 120.0),
            ("2454", "聯發科", 800.0),
        ]

        cursor.executemany(
            "INSERT INTO test_stocks (symbol, name, price) VALUES (?, ?, ?)", test_data
        )
        conn.commit()

        # 查詢數據
        cursor.execute("SELECT symbol, name, price FROM test_stocks ORDER BY symbol")
        results = cursor.fetchall()

        assert len(results) == 3
        assert results[0] == ("0050", "元大台灣50", 120.0)
        assert results[1] == ("2330", "台積電", 500.0)
        assert results[2] == ("2454", "聯發科", 800.0)

    def test_update_data(self, temp_db):
        """測試更新數據。"""
        conn, db_path = temp_db
        cursor = conn.cursor()

        # 創建表格並插入數據
        cursor.execute(
            """
            CREATE TABLE test_stocks (
                symbol TEXT PRIMARY KEY,
                price REAL
            )
        """
        )
        cursor.execute("INSERT INTO test_stocks (symbol, price) VALUES (?, ?)", ("2330", 500.0))
        conn.commit()

        # 更新數據
        cursor.execute("UPDATE test_stocks SET price = ? WHERE symbol = ?", (520.0, "2330"))
        conn.commit()

        # 驗證更新
        cursor.execute("SELECT price FROM test_stocks WHERE symbol = ?", ("2330",))
        result = cursor.fetchone()

        assert result[0] == 520.0

    def test_delete_data(self, temp_db):
        """測試刪除數據。"""
        conn, db_path = temp_db
        cursor = conn.cursor()

        # 創建表格並插入數據
        cursor.execute(
            """
            CREATE TABLE test_stocks (
                symbol TEXT PRIMARY KEY,
                name TEXT
            )
        """
        )
        cursor.executemany(
            "INSERT INTO test_stocks (symbol, name) VALUES (?, ?)",
            [("2330", "台積電"), ("0050", "元大台灣50")],
        )
        conn.commit()

        # 刪除數據
        cursor.execute("DELETE FROM test_stocks WHERE symbol = ?", ("0050",))
        conn.commit()

        # 驗證刪除
        cursor.execute("SELECT COUNT(*) FROM test_stocks")
        count = cursor.fetchone()[0]

        cursor.execute("SELECT symbol FROM test_stocks")
        remaining = cursor.fetchone()

        assert count == 1
        assert remaining[0] == "2330"

    def test_transaction_rollback(self, temp_db):
        """測試事務回滾。"""
        conn, db_path = temp_db
        cursor = conn.cursor()

        # 創建表格
        cursor.execute(
            """
            CREATE TABLE test_stocks (
                symbol TEXT PRIMARY KEY,
                price REAL CHECK(price > 0)
            )
        """
        )
        conn.commit()

        # 開始事務
        try:
            cursor.execute("INSERT INTO test_stocks (symbol, price) VALUES (?, ?)", ("2330", 500.0))
            # 這個插入應該失敗（負價格）
            cursor.execute(
                "INSERT INTO test_stocks (symbol, price) VALUES (?, ?)", ("0050", -100.0)
            )
            conn.commit()
        except sqlite3.IntegrityError:
            conn.rollback()

        # 檢查回滾結果
        cursor.execute("SELECT COUNT(*) FROM test_stocks")
        count = cursor.fetchone()[0]

        # 由於回滾，應該沒有數據
        assert count == 0

    def test_concurrent_access(self, temp_db):
        """測試並發訪問（簡化版）。"""
        conn1, db_path = temp_db

        # 創建第二個連接
        conn2 = sqlite3.connect(db_path)

        try:
            # 在第一個連接中創建表格
            cursor1 = conn1.cursor()
            cursor1.execute(
                """
                CREATE TABLE test_concurrent (
                    id INTEGER PRIMARY KEY,
                    value TEXT
                )
            """
            )
            conn1.commit()

            # 在第二個連接中插入數據
            cursor2 = conn2.cursor()
            cursor2.execute("INSERT INTO test_concurrent (value) VALUES (?)", ("test",))
            conn2.commit()

            # 在第一個連接中查詢數據
            cursor1.execute("SELECT value FROM test_concurrent")
            result = cursor1.fetchone()

            assert result[0] == "test"

        finally:
            conn2.close()


class TestDatabaseSchema(TestBase):
    """資料庫架構測試。"""

    @pytest.fixture
    def schema_db(self):
        """創建帶有完整架構的測試資料庫。"""
        with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as tmp_file:
            db_path = tmp_file.name

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 創建完整的資料庫架構
        cursor.executescript(
            """
            -- 股票基本資料表
            CREATE TABLE stocks (
                symbol TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                market TEXT,
                industry TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 股價歷史資料表
            CREATE TABLE stock_prices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date DATE NOT NULL,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                UNIQUE(symbol, date)
            );
            
            -- 新聞資料表
            CREATE TABLE news (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                url TEXT UNIQUE,
                published_date TIMESTAMP,
                source TEXT,
                sentiment_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 股票新聞關聯表
            CREATE TABLE stock_news (
                stock_symbol TEXT,
                news_id INTEGER,
                relevance_score REAL,
                PRIMARY KEY (stock_symbol, news_id),
                FOREIGN KEY (stock_symbol) REFERENCES stocks (symbol),
                FOREIGN KEY (news_id) REFERENCES news (id)
            );
            
            -- 創建索引
            CREATE INDEX idx_stock_prices_symbol_date ON stock_prices (symbol, date);
            CREATE INDEX idx_news_published_date ON news (published_date);
            CREATE INDEX idx_stock_news_relevance ON stock_news (relevance_score);
        """
        )

        conn.commit()
        yield conn, db_path

        conn.close()
        Path(db_path).unlink(missing_ok=True)

    def test_schema_creation(self, schema_db):
        """測試架構創建。"""
        conn, db_path = schema_db
        cursor = conn.cursor()

        # 檢查所有表格是否存在
        cursor.execute(
            """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """
        )
        tables = [row[0] for row in cursor.fetchall()]

        expected_tables = ["news", "stock_news", "stock_prices", "stocks"]
        assert tables == expected_tables

    def test_foreign_key_constraints(self, schema_db):
        """測試外鍵約束。"""
        conn, db_path = schema_db
        cursor = conn.cursor()

        # 啟用外鍵約束
        cursor.execute("PRAGMA foreign_keys = ON")

        # 插入股票資料
        cursor.execute("INSERT INTO stocks (symbol, name) VALUES (?, ?)", ("2330", "台積電"))

        # 插入股價資料（應該成功）
        cursor.execute(
            """
            INSERT INTO stock_prices (symbol, date, close_price) 
            VALUES (?, ?, ?)
        """,
            ("2330", "2024-01-01", 500.0),
        )

        # 嘗試插入不存在股票的股價資料（應該失敗）
        with pytest.raises(sqlite3.IntegrityError):
            cursor.execute(
                """
                INSERT INTO stock_prices (symbol, date, close_price) 
                VALUES (?, ?, ?)
            """,
                ("9999", "2024-01-01", 100.0),
            )

    def test_unique_constraints(self, schema_db):
        """測試唯一性約束。"""
        conn, db_path = schema_db
        cursor = conn.cursor()

        # 插入股票資料
        cursor.execute("INSERT INTO stocks (symbol, name) VALUES (?, ?)", ("2330", "台積電"))

        # 插入股價資料
        cursor.execute(
            """
            INSERT INTO stock_prices (symbol, date, close_price) 
            VALUES (?, ?, ?)
        """,
            ("2330", "2024-01-01", 500.0),
        )

        # 嘗試插入相同日期的股價資料（應該失敗）
        with pytest.raises(sqlite3.IntegrityError):
            cursor.execute(
                """
                INSERT INTO stock_prices (symbol, date, close_price) 
                VALUES (?, ?, ?)
            """,
                ("2330", "2024-01-01", 510.0),
            )

    def test_index_performance(self, schema_db):
        """測試索引性能（簡化版）。"""
        conn, db_path = schema_db
        cursor = conn.cursor()

        # 插入測試數據
        cursor.execute("INSERT INTO stocks (symbol, name) VALUES (?, ?)", ("2330", "台積電"))

        # 插入大量股價數據
        import datetime

        base_date = datetime.date(2024, 1, 1)

        for i in range(100):
            test_date = base_date + datetime.timedelta(days=i)
            cursor.execute(
                """
                INSERT INTO stock_prices (symbol, date, close_price) 
                VALUES (?, ?, ?)
            """,
                ("2330", test_date.isoformat(), 500.0 + i),
            )

        conn.commit()

        # 測試查詢性能（使用 EXPLAIN QUERY PLAN）
        cursor.execute(
            """
            EXPLAIN QUERY PLAN 
            SELECT * FROM stock_prices 
            WHERE symbol = '2330' AND date >= '2024-01-01'
        """
        )

        query_plan = cursor.fetchall()
        # 檢查是否使用了索引
        plan_text = " ".join([str(row) for row in query_plan])
        assert "idx_stock_prices_symbol_date" in plan_text or "INDEX" in plan_text.upper()
