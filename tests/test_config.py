#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理單元測試
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
import tempfile
import unittest
from pathlib import Path

from utils.config import ConfigManager


class TestConfigManager(unittest.TestCase):
    """
    配置管理器測試類
    """

    def setUp(self):
        """
        測試前準備
        """
        # 創建臨時配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".json")

        # 測試配置數據
        self.test_config = {
            "database": {"path": "test_database.db"},
            "api": {"timeout": 30, "retry_count": 3},
            "logging": {"level": "INFO", "file": "test.log", "dir": "test_logs"},
            "models": {"dir": "test_models"},
        }

        # 寫入測試配置
        json.dump(self.test_config, self.temp_config, indent=2)
        self.temp_config.close()

    def tearDown(self):
        """
        測試後清理
        """
        # 刪除臨時配置文件
        if os.path.exists(self.temp_config.name):
            os.unlink(self.temp_config.name)

    def test_load_existing_config(self):
        """
        測試載入現有配置文件
        """
        # 創建臨時目錄
        temp_dir = tempfile.mkdtemp()
        config_filename = os.path.basename(self.temp_config.name)
        
        # 複製配置文件到臨時目錄
        import shutil
        shutil.copy(self.temp_config.name, os.path.join(temp_dir, config_filename))
        
        try:
            config_manager = ConfigManager(config_dir=temp_dir)
            config = config_manager.load_config(config_filename)

            # 檢查配置是否正確載入
            self.assertEqual(config["database"]["path"], "test_database.db")
            self.assertEqual(config["api"]["timeout"], 30)
            self.assertEqual(config["logging"]["level"], "INFO")
        finally:
            shutil.rmtree(temp_dir)

    def test_load_nonexistent_config(self):
        """
        測試載入不存在的配置文件（應該使用默認配置）
        """
        temp_dir = tempfile.mkdtemp()
        try:
            config_manager = ConfigManager(config_dir=temp_dir)
            config = config_manager.load_config("nonexistent_config.json")

            # 檢查是否使用默認配置
            self.assertIn("database", config)
            self.assertIn("logging", config)
            self.assertIn("models", config)

            # 檢查默認值（根據實際的 ConfigManager 實現）
            self.assertIn("path", config["database"])
            self.assertEqual(config["logging"]["level"], "INFO")
        finally:
            import shutil
            shutil.rmtree(temp_dir)

    def test_update_config(self):
        """
        測試配置合併功能
        """
        temp_dir = tempfile.mkdtemp()
        config_filename = "test_config.json"
        
        try:
            config_manager = ConfigManager(config_dir=temp_dir)
            
            # 創建一個新的配置
            new_config = {
                "database": {"path": "updated_database.db"},
                "api": {"timeout": 60}
            }
            
            # 保存配置
            config_manager.save_config(new_config, config_filename)
            
            # 載入配置並檢查合併結果
            loaded_config = config_manager.load_config(config_filename)
            
            # 檢查更新是否成功（與默認配置合併）
            self.assertEqual(loaded_config["database"]["path"], "updated_database.db")
            
            # 檢查默認配置是否仍然存在
            self.assertIn("logging", loaded_config)
            self.assertEqual(loaded_config["logging"]["level"], "INFO")
        finally:
            import shutil
            shutil.rmtree(temp_dir)

    def test_save_config(self):
        """
        測試保存配置到文件
        """
        temp_dir = tempfile.mkdtemp()
        config_filename = "saved_config.json"
        
        try:
            config_manager = ConfigManager(config_dir=temp_dir)

            # 創建配置
            test_config = {"database": {"path": "saved_database.db"}}

            # 保存配置
            config_manager.save_config(test_config, config_filename)

            # 重新載入配置文件驗證
            config_path = os.path.join(temp_dir, config_filename)
            with open(config_path, "r", encoding="utf-8") as f:
                saved_config = json.load(f)

            self.assertEqual(saved_config["database"]["path"], "saved_database.db")
        finally:
            import shutil
            shutil.rmtree(temp_dir)

    def test_get_specific_config_section(self):
        """
        測試獲取特定配置區段
        """
        temp_dir = tempfile.mkdtemp()
        config_filename = os.path.basename(self.temp_config.name)
        
        try:
            import shutil
            shutil.copy(self.temp_config.name, os.path.join(temp_dir, config_filename))
            
            config_manager = ConfigManager(config_dir=temp_dir)
            config = config_manager.load_config(config_filename)

            # 獲取資料庫配置
            db_path = config_manager.get_config_value(config, "database.path")
            self.assertEqual(db_path, "test_database.db")

            # 獲取 API 配置
            api_timeout = config_manager.get_config_value(config, "api.timeout")
            self.assertEqual(api_timeout, 30)
            
            api_retry = config_manager.get_config_value(config, "api.retry_count")
            self.assertEqual(api_retry, 3)

            # 獲取不存在的區段
            nonexistent = config_manager.get_config_value(config, "nonexistent.key")
            self.assertIsNone(nonexistent)
        finally:
            shutil.rmtree(temp_dir)

    def test_config_validation(self):
        """
        測試配置驗證
        """
        temp_dir = tempfile.mkdtemp()
        config_filename = os.path.basename(self.temp_config.name)
        
        try:
            import shutil
            shutil.copy(self.temp_config.name, os.path.join(temp_dir, config_filename))
            
            config_manager = ConfigManager(config_dir=temp_dir)
            config = config_manager.load_config(config_filename)

            # 檢查必要的配置項是否存在（根據實際的默認配置）
            required_sections = ["database", "logging", "models"]
            for section in required_sections:
                self.assertIn(section, config)

            # 檢查資料庫配置
            self.assertIn("path", config["database"])

            # 檢查 API 配置（如果存在）
            if "api" in config:
                self.assertIn("timeout", config["api"])
                self.assertIn("retry_count", config["api"])
                self.assertIsInstance(config["api"]["timeout"], int)
                self.assertIsInstance(config["api"]["retry_count"], int)

            # 檢查日誌配置
            self.assertIn("level", config["logging"])
            self.assertIn("dir", config["logging"])
        finally:
            shutil.rmtree(temp_dir)

    def test_invalid_json_handling(self):
        """
        測試處理無效的 JSON 配置文件
        """
        temp_dir = tempfile.mkdtemp()
        invalid_filename = "invalid_config.json"
        
        try:
            # 創建無效的 JSON 文件
            invalid_path = os.path.join(temp_dir, invalid_filename)
            with open(invalid_path, "w", encoding="utf-8") as f:
                f.write("{invalid json content")

            # 嘗試載入無效配置（應該回退到默認配置）
            config_manager = ConfigManager(config_dir=temp_dir)
            config = config_manager.load_config(invalid_filename)

            # 應該使用默認配置
            self.assertIn("database", config)
            self.assertIn("path", config["database"])

        finally:
            # 清理
            import shutil
            shutil.rmtree(temp_dir)

    def test_config_merge(self):
        """
        測試配置合併功能
        """
        temp_dir = tempfile.mkdtemp()
        config_filename = "merge_test.json"
        
        try:
            config_manager = ConfigManager(config_dir=temp_dir)

            # 部分更新配置
            partial_update = {
                "api": {"timeout": 45},  # 只更新 timeout，保留 retry_count
                "new_section": {"new_key": "new_value"},
            }

            # 保存部分配置
            config_manager.save_config(partial_update, config_filename)
            
            # 載入配置（會與默認配置合併）
            merged_config = config_manager.load_config(config_filename)

            # 檢查部分更新
            self.assertEqual(merged_config["api"]["timeout"], 45)

            # 檢查新增區段
            self.assertIn("new_section", merged_config)
            self.assertEqual(merged_config["new_section"]["new_key"], "new_value")

            # 檢查默認配置是否仍然存在
            self.assertIn("database", merged_config)
            self.assertIn("logging", merged_config)
        finally:
            import shutil
            shutil.rmtree(temp_dir)


class TestConfigManagerEdgeCases(unittest.TestCase):
    """
    配置管理器邊界情況測試
    """

    def test_empty_config_file(self):
        """
        測試空配置文件
        """
        temp_dir = tempfile.mkdtemp()
        empty_filename = "empty_config.json"
        
        try:
            # 創建空配置文件
            empty_path = os.path.join(temp_dir, empty_filename)
            with open(empty_path, "w", encoding="utf-8") as f:
                f.write("")

            config_manager = ConfigManager(config_dir=temp_dir)
            config = config_manager.load_config(empty_filename)

            # 應該使用默認配置
            self.assertIn("database", config)

        finally:
            import shutil
            shutil.rmtree(temp_dir)

    def test_config_with_missing_sections(self):
        """
        測試缺少某些區段的配置文件
        """
        temp_dir = tempfile.mkdtemp()
        partial_filename = "partial_config.json"
        
        try:
            # 只包含部分配置
            partial_data = {"database": {"path": "partial_database.db"}}
            
            partial_path = os.path.join(temp_dir, partial_filename)
            with open(partial_path, "w", encoding="utf-8") as f:
                json.dump(partial_data, f, indent=2)

            config_manager = ConfigManager(config_dir=temp_dir)
            config = config_manager.load_config(partial_filename)

            # 檢查現有配置
            self.assertEqual(config["database"]["path"], "partial_database.db")

            # 檢查默認配置是否填充缺失部分
            self.assertIn("logging", config)
            self.assertIn("models", config)

        finally:
            import shutil
            shutil.rmtree(temp_dir)


if __name__ == "__main__":
    # 運行測試
    unittest.main(verbosity=2)
