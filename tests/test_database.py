#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據庫操作單元測試
"""

import os
import sqlite3
import sys
import tempfile
import unittest
from datetime import date, datetime
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.db_manager import DatabaseManager
from database.createtable import main as create_all_tables


class TestDatabaseManager(unittest.TestCase):
    """
    數據庫管理器測試類
    """

    def setUp(self):
        """
        測試前準備
        """
        # 創建臨時數據庫文件
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        self.temp_db.close()
        self.db_path = self.temp_db.name

        # 初始化數據庫管理器
        self.db_manager = DatabaseManager(self.db_path)

        # 創建所有表格
        create_all_tables(self.db_path)

    def tearDown(self):
        """
        測試後清理
        """
        # 關閉數據庫連接
        if hasattr(self.db_manager, "conn") and self.db_manager.conn:
            self.db_manager.conn.close()

        # 刪除臨時數據庫文件
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)

    def test_database_connection(self):
        """
        測試數據庫連接
        """
        from sqlalchemy import text
        
        # 測試獲取會話
        with self.db_manager.get_session() as session:
            self.assertIsNotNone(session)
            
            # 測試連接是否可用
            result = session.execute(text("SELECT 1")).fetchone()
            self.assertEqual(result[0], 1)

    def test_execute_query(self):
        """
        測試執行查詢
        """
        from sqlalchemy import text
        
        # 測試查詢表格列表
        with self.db_manager.get_session() as session:
            result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = result.fetchall()

            # 檢查是否有表格被創建
            table_names = [table[0] for table in tables]
            expected_tables = [
                "company_info",
                "daily_prices",
                "daily_technical_indicators",
                "financial_reports",
                "stock_news",
                "news_stock_relation",
                "news_sentiment",
            ]

            for table in expected_tables:
                self.assertIn(table, table_names)

    def test_execute_update(self):
        """
        測試執行更新操作
        """
        from sqlalchemy import text
        
        # 插入測試公司資料
        insert_sql = text("""
        INSERT INTO company_info (stock_id, name, industry, market)
        VALUES (:stock_id, :name, :industry, :market)
        """)

        with self.db_manager.get_session() as session:
            result = session.execute(
                insert_sql, {"stock_id": "2330", "name": "台積電", "industry": "半導體", "market": "上市"}
            )
            affected_rows = result.rowcount
            self.assertEqual(affected_rows, 1)

            # 驗證插入是否成功
            query_result = session.execute(
                text("SELECT * FROM company_info WHERE stock_id = :stock_id"), {"stock_id": "2330"}
            ).fetchall()

            self.assertEqual(len(query_result), 1)
            self.assertEqual(query_result[0][0], "2330")  # stock_id
            self.assertEqual(query_result[0][1], "台積電")  # name

    def test_batch_insert(self):
        """
        測試批量插入
        """
        from sqlalchemy import text
        
        # 準備測試數據
        companies = [
            {"stock_id": "2330", "name": "台積電", "industry": "半導體", "market": "上市"},
            {"stock_id": "2317", "name": "鴻海", "industry": "電子", "market": "上市"},
            {"stock_id": "2454", "name": "聯發科", "industry": "半導體", "market": "上市"},
        ]

        insert_sql = text("""
        INSERT OR REPLACE INTO company_info (stock_id, name, industry, market)
        VALUES (:stock_id, :name, :industry, :market)
        """)

        # 執行批量插入
        with self.db_manager.get_session() as session:
            for company in companies:
                session.execute(insert_sql, company)
            session.commit()

        # 驗證插入結果
        with self.db_manager.get_session() as session:
            result = session.execute(text("SELECT COUNT(*) FROM company_info")).fetchone()
            self.assertEqual(result[0], 3)

    def test_transaction_commit(self):
        """
        測試事務提交
        """
        from sqlalchemy import text
        
        try:
            with self.db_manager.get_session() as session:
                # 插入數據
                session.execute(
                    text("INSERT INTO company_info (stock_id, name, industry, market) VALUES (:stock_id, :name, :industry, :market)"),
                    {"stock_id": "1234", "name": "測試公司", "industry": "測試行業", "market": "上市"}
                )
                session.commit()

                # 驗證數據是否存在
                result = session.execute(
                    text("SELECT * FROM company_info WHERE stock_id = :stock_id"), 
                    {"stock_id": "1234"}
                ).fetchall()
                self.assertEqual(len(result), 1)

        except Exception as e:
            self.fail(f"Transaction failed: {e}")

    def test_transaction_rollback(self):
        """
        測試事務回滾
        """
        from sqlalchemy import text
        
        try:
            with self.db_manager.get_session() as session:
                # 插入數據
                session.execute(
                    text("INSERT INTO company_info (stock_id, name, industry, market) VALUES (:stock_id, :name, :industry, :market)"),
                    {"stock_id": "5678", "name": "回滾測試", "industry": "測試行業", "market": "上市"}
                )
                
                # 故意回滾（不提交）
                session.rollback()

            # 驗證數據不存在（在新的 session 中查詢）
            with self.db_manager.get_session() as session:
                result = session.execute(
                    text("SELECT * FROM company_info WHERE stock_id = :stock_id"), 
                    {"stock_id": "5678"}
                ).fetchall()
                self.assertEqual(len(result), 0)

        except Exception as e:
            self.fail(f"Rollback test failed: {e}")

    def test_error_handling(self):
        """
        測試錯誤處理
        """
        from sqlalchemy import text
        
        # 測試無效的 SQL
        with self.assertRaises(Exception):
            with self.db_manager.get_session() as session:
                session.execute(text("INVALID SQL STATEMENT"))

        # 測試插入重複主鍵（如果有唯一約束）
        with self.db_manager.get_session() as session:
            session.execute(
                text("INSERT INTO company_info (stock_id, name, industry, market) VALUES (:stock_id, :name, :industry, :market)"),
                {"stock_id": "9999", "name": "測試公司1", "industry": "測試行業", "market": "上市"}
            )
            session.commit()

        # 再次插入相同的 stock_id（應該會失敗或被替換，取決於表格設計）
        try:
            with self.db_manager.get_session() as session:
                session.execute(
                    text("INSERT INTO company_info (stock_id, name, industry, market) VALUES (:stock_id, :name, :industry, :market)"),
                    {"stock_id": "9999", "name": "測試公司2", "industry": "測試行業", "market": "上市"}
                )
                session.commit()
        except Exception:
            # 如果有唯一約束，這裡會拋出異常
            pass

    def test_data_types_handling(self):
        """
        測試不同數據類型的處理
        """
        from sqlalchemy import text
        
        # 插入股價數據（包含日期和數值）
        price_data = {
            "stock_id": "2330",
            "date": date.today(),
            "open": 100.5,
            "high": 105.0,
            "low": 99.0,
            "close": 102.5,
            "volume": 1000000,
        }

        insert_sql = text("""
        INSERT INTO daily_prices 
        (stock_id, date, open, high, low, close, volume)
        VALUES (:stock_id, :date, :open, :high, :low, :close, :volume)
        """)

        with self.db_manager.get_session() as session:
            result = session.execute(insert_sql, price_data)
            affected_rows = result.rowcount
            session.commit()
            self.assertEqual(affected_rows, 1)

            # 驗證數據類型
            result = session.execute(
                text("SELECT * FROM daily_prices WHERE stock_id = :stock_id"), 
                {"stock_id": "2330"}
            ).fetchall()

            self.assertEqual(len(result), 1)
            row = result[0]

            # 檢查數值類型（SQLite 可能返回不同類型）
            # 根據實際的 row 結構：('2330', '2025-06-25', 100.5, 105.0, 99.0, 102.5, None, 1000000, None, None, None)
            # 索引: 0=stock_id, 1=date, 2=open, 3=high, 4=low, 5=close, 6=?, 7=volume
            
            self.assertIsInstance(row[2], (float, int))  # open
            self.assertIsInstance(row[7], (int, float))  # volume (正確的索引是 7)

    def test_session_management(self):
        """
        測試 session 管理
        """
        # 測試 session 可以正常創建和使用
        with self.db_manager.get_session() as session1:
            self.assertIsNotNone(session1)

        # 獲取第二個 session
        with self.db_manager.get_session() as session2:
            self.assertIsNotNone(session2)

    def test_session_context_manager(self):
        """
        測試 session 上下文管理器
        """
        from sqlalchemy import text
        
        # 測試 with 語句自動管理 session
        with self.db_manager.get_session() as session:
            result = session.execute(text("SELECT 1")).fetchone()
            self.assertEqual(result[0], 1)


class TestDatabaseTableCreation(unittest.TestCase):
    """
    數據庫表格創建測試
    """

    def setUp(self):
        """
        測試前準備
        """
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        self.temp_db.close()
        self.db_path = self.temp_db.name

    def tearDown(self):
        """
        測試後清理
        """
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)

    def test_create_all_tables(self):
        """
        測試創建所有表格
        """
        # 創建表格
        import sqlite3

        from database.createtable import create_tables

        conn = sqlite3.connect(self.db_path)
        create_tables(conn)
        conn.close()

        # 驗證表格是否創建成功
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]

        expected_tables = [
            "company_info",
            "daily_prices",
            "daily_technical_indicators",
            "financial_reports",
            "stock_news",
            "news_stock_relation",
            "news_sentiment",
        ]

        for table in expected_tables:
            self.assertIn(table, tables)

        conn.close()

    def test_table_structure(self):
        """
        測試表格結構
        """
        import sqlite3

        from database.createtable import create_tables

        conn = sqlite3.connect(self.db_path)
        create_tables(conn)
        conn.close()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 檢查 company_info 表格結構
        cursor.execute("PRAGMA table_info(company_info)")
        columns = [row[1] for row in cursor.fetchall()]

        expected_columns = [
            "stock_id",
            "name",
            "full_name",
            "industry",
            "market",
            "last_updated_time",
        ]
        for col in expected_columns:
            self.assertIn(col, columns)

        # 檢查 daily_prices 表格結構
        cursor.execute("PRAGMA table_info(daily_prices)")
        columns = [row[1] for row in cursor.fetchall()]

        expected_columns = ["stock_id", "date", "open", "high", "low", "close", "volume"]
        for col in expected_columns:
            self.assertIn(col, columns)

        conn.close()

    def test_foreign_key_constraints(self):
        """
        測試外鍵約束
        """
        import sqlite3

        from database.createtable import create_tables

        conn = sqlite3.connect(self.db_path)
        create_tables(conn)
        conn.close()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 檢查外鍵約束是否啟用
        cursor.execute("PRAGMA foreign_keys")
        result = cursor.fetchone()

        # 注意：SQLite 默認情況下外鍵約束可能未啟用
        # 這個測試主要是檢查表格創建時是否定義了外鍵

        # 檢查 daily_prices 表的外鍵
        cursor.execute("PRAGMA foreign_key_list(daily_prices)")
        foreign_keys = cursor.fetchall()

        # 如果定義了外鍵，應該能找到相關信息
        # 這取決於 createtable.py 中的具體實現

        conn.close()


if __name__ == "__main__":
    # 運行測試
    unittest.main(verbosity=2)
