#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的數據庫操作單元測試
"""

import os
import sqlite3
import sys
import tempfile
import unittest
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.createtable import main as create_all_tables
from utils.db_manager import DatabaseManager
from sqlalchemy import text


class TestDatabaseSimple(unittest.TestCase):
    """
    簡化的數據庫測試類
    """

    def setUp(self):
        """
        測試前準備
        """
        # 創建臨時數據庫文件
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        self.temp_db.close()
        self.db_path = self.temp_db.name

        # 創建所有表格
        create_all_tables(self.db_path)

        # 初始化數據庫管理器
        self.db_manager = DatabaseManager(self.db_path)

    def tearDown(self):
        """
        測試後清理
        """
        # 關閉數據庫連接
        if hasattr(self.db_manager, "close"):
            self.db_manager.close()

        # 刪除臨時數據庫文件
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)

    def test_database_connection(self):
        """
        測試數據庫連接
        """
        with self.db_manager.get_session() as session:
            self.assertIsNotNone(session)
            result = session.execute(text("SELECT 1")).fetchone()
            self.assertEqual(result[0], 1)

    def test_tables_created(self):
        """
        測試表格是否創建成功
        """
        with self.db_manager.get_session() as session:
            result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result.fetchall()]
            
            expected_tables = [
                "company_info",
                "daily_prices",
                "stock_news",
            ]
            
            for table in expected_tables:
                self.assertIn(table, tables)

    def test_basic_insert_and_query(self):
        """
        測試基本的插入和查詢操作
        """
        with self.db_manager.get_session() as session:
            # 插入測試數據
            session.execute(
                text("INSERT INTO company_info (stock_id, name, industry, market) VALUES (:stock_id, :name, :industry, :market)"),
                {"stock_id": "2330", "name": "台積電", "industry": "半導體", "market": "上市"}
            )
            
            # 查詢數據
            result = session.execute(
                text("SELECT * FROM company_info WHERE stock_id = :stock_id"),
                {"stock_id": "2330"}
            ).fetchall()
            
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0][0], "2330")
            self.assertEqual(result[0][1], "台積電")


if __name__ == "__main__":
    unittest.main(verbosity=2)