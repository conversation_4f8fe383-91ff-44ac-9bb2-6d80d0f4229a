#!/usr/bin/env python3
"""增強架構測試

測試依賴注入、錯誤處理、性能監控等功能。
"""

import unittest
import tempfile
import os
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from utils.dependency_injection import DIContainer
from utils.enhanced_config_manager import EnhancedConfigManager
from utils.enhanced_logger import EnhancedLogger
from utils.enhanced_db_manager import EnhancedDatabaseManager
from utils.app_factory import ApplicationFactory, get_app_factory, reset_app_factory
from utils.exceptions import (
    StockAnalysisException, DatabaseError, ModelError, 
    ConfigurationError, handle_exception
)


class TestDependencyInjection(unittest.TestCase):
    """測試依賴注入容器"""
    
    def setUp(self):
        self.container = DIContainer()
    
    def test_register_and_resolve_singleton(self):
        """測試單例註冊和解析"""
        # 註冊單例
        self.container.register_singleton('test_service', lambda: {'value': 42})
        
        # 解析兩次，應該是同一個實例
        instance1 = self.container.resolve('test_service')
        instance2 = self.container.resolve('test_service')
        
        self.assertIs(instance1, instance2)
        self.assertEqual(instance1['value'], 42)
    
    def test_register_and_resolve_transient(self):
        """測試瞬態註冊和解析"""
        # 註冊瞬態
        self.container.register_transient('test_service', lambda: {'value': 42})
        
        # 解析兩次，應該是不同的實例
        instance1 = self.container.resolve('test_service')
        instance2 = self.container.resolve('test_service')
        
        self.assertIsNot(instance1, instance2)
        self.assertEqual(instance1['value'], 42)
        self.assertEqual(instance2['value'], 42)
    
    def test_register_instance(self):
        """測試實例註冊"""
        test_instance = {'value': 42}
        self.container.register_instance('test_service', test_instance)
        
        resolved = self.container.resolve('test_service')
        self.assertIs(resolved, test_instance)
    
    def test_resolve_nonexistent_service(self):
        """測試解析不存在的服務"""
        with self.assertRaises(ValueError):
            self.container.resolve('nonexistent_service')


class TestEnhancedConfigManager(unittest.TestCase):
    """測試增強配置管理器"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'test_config.json')
    
    def tearDown(self):
        # 清理臨時文件
        if os.path.exists(self.config_file):
            os.remove(self.config_file)
        os.rmdir(self.temp_dir)
    
    def test_default_config_creation(self):
        """測試默認配置創建"""
        config_manager = EnhancedConfigManager(self.config_file)
        
        # 檢查默認配置
        db_config = config_manager.get_database_config()
        self.assertIsNotNone(db_config.path)
        
        api_config = config_manager.get_api_config()
        self.assertEqual(api_config.request_timeout, 30.0)
    
    def test_config_file_loading(self):
        """測試配置文件加載"""
        # 創建測試配置文件
        test_config = {
            'database': {
                'path': 'test.db',
                'pool_size': 5
            },
            'api': {
                'request_timeout': 60.0
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(test_config, f)
        
        config_manager = EnhancedConfigManager(self.config_file)
        
        # 檢查加載的配置
        self.assertEqual(config_manager.get('database.path'), 'test.db')
        self.assertEqual(config_manager.get('database.pool_size'), 5)
        self.assertEqual(config_manager.get('api.request_timeout'), 60.0)
    
    def test_environment_variable_override(self):
        """測試環境變量覆蓋"""
        with patch.dict(os.environ, {'STOCK_DATABASE__PATH': 'env_test.db'}):
            config_manager = EnhancedConfigManager(self.config_file, env_prefix='STOCK_')
            self.assertEqual(config_manager.get('database.path'), 'env_test.db')
    
    def test_config_validation(self):
        """測試配置驗證"""
        # 創建無效配置
        invalid_config = {
            'database': {'path': ''},  # 空路徑
            'model': {'prediction_days': 0}  # 無效預測天數
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(invalid_config, f)
        
        with self.assertRaises(ConfigurationError):
            EnhancedConfigManager(self.config_file)


class TestEnhancedLogger(unittest.TestCase):
    """測試增強日誌管理器"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.logger = EnhancedLogger(
            name="test_logger",
            log_dir=self.temp_dir,
            enable_console=False,  # 測試時禁用控制台輸出
            enable_file=True
        )
    
    def tearDown(self):
        # 清理日誌文件
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_basic_logging(self):
        """測試基本日誌功能"""
        self.logger.info("測試信息")
        self.logger.warning("測試警告")
        self.logger.error("測試錯誤")
        
        # 檢查日誌文件是否創建
        log_files = list(Path(self.temp_dir).glob("*.log"))
        self.assertGreater(len(log_files), 0)
    
    def test_performance_logging(self):
        """測試性能日誌"""
        self.logger.log_performance("test_operation", 0.5)
        self.logger.log_performance("slow_operation", 2.0)  # 應該記錄為警告
    
    def test_api_call_logging(self):
        """測試API調用日誌"""
        self.logger.log_api_call("GET", "https://api.example.com", 200, 0.3)
        self.logger.log_api_call("POST", "https://api.example.com", 404, 0.5)
        self.logger.log_api_call("GET", "https://api.example.com", 500, 1.0)
    
    def test_database_operation_logging(self):
        """測試數據庫操作日誌"""
        self.logger.log_database_operation("SELECT", "stock_data", 100, 0.1)
        self.logger.log_database_operation("INSERT", "news_data", 1, 0.05)
    
    def test_log_stats(self):
        """測試日誌統計"""
        stats = self.logger.get_log_stats()
        
        self.assertIn('logger_name', stats)
        self.assertIn('current_level', stats)
        self.assertIn('handlers_count', stats)
        self.assertEqual(stats['logger_name'], 'test_logger')


class TestEnhancedDatabaseManager(unittest.TestCase):
    """測試增強數據庫管理器"""
    
    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        self.db_manager = EnhancedDatabaseManager(self.temp_db.name)
    
    def tearDown(self):
        self.db_manager.close()
        os.unlink(self.temp_db.name)
    
    def test_connection(self):
        """測試數據庫連接"""
        conn = self.db_manager.get_connection()
        self.assertIsNotNone(conn)
    
    def test_session_context_manager(self):
        """測試會話上下文管理器"""
        with self.db_manager.get_session() as session:
            result = session.execute("SELECT 1 as test_value")
            self.assertIsNotNone(result)
    
    def test_execute_query(self):
        """測試查詢執行"""
        # 創建測試表
        self.db_manager.execute_non_query(
            "CREATE TABLE test_table (id INTEGER PRIMARY KEY, name TEXT)"
        )
        
        # 插入測試數據
        self.db_manager.execute_non_query(
            "INSERT INTO test_table (name) VALUES (?)",
            ("test_name",)
        )
        
        # 查詢數據
        results = self.db_manager.execute_query("SELECT * FROM test_table")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['name'], 'test_name')
    
    def test_transaction(self):
        """測試事務"""
        # 創建測試表
        self.db_manager.execute_non_query(
            "CREATE TABLE test_table (id INTEGER PRIMARY KEY, name TEXT)"
        )
        
        # 測試成功事務
        with self.db_manager.transaction() as conn:
            conn.execute("INSERT INTO test_table (name) VALUES (?)", ("test1",))
            conn.execute("INSERT INTO test_table (name) VALUES (?)", ("test2",))
        
        results = self.db_manager.execute_query("SELECT COUNT(*) as count FROM test_table")
        self.assertEqual(results[0]['count'], 2)
        
        # 測試失敗事務（回滾）
        try:
            with self.db_manager.transaction() as conn:
                conn.execute("INSERT INTO test_table (name) VALUES (?)", ("test3",))
                raise Exception("測試異常")
        except Exception:
            pass
        
        results = self.db_manager.execute_query("SELECT COUNT(*) as count FROM test_table")
        self.assertEqual(results[0]['count'], 2)  # 應該還是2，因為回滾了
    
    def test_table_operations(self):
        """測試表操作"""
        # 測試表不存在
        self.assertFalse(self.db_manager.table_exists('nonexistent_table'))
        
        # 創建表
        self.db_manager.execute_non_query(
            "CREATE TABLE test_table (id INTEGER PRIMARY KEY, name TEXT)"
        )
        
        # 測試表存在
        self.assertTrue(self.db_manager.table_exists('test_table'))
        
        # 測試表信息
        table_info = self.db_manager.get_table_info('test_table')
        self.assertGreater(len(table_info), 0)
        
        # 測試行數
        row_count = self.db_manager.get_row_count('test_table')
        self.assertEqual(row_count, 0)


class TestApplicationFactory(unittest.TestCase):
    """測試應用程序工廠"""
    
    def setUp(self):
        reset_app_factory()  # 重置全局工廠
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'test_config.json')
        
        # 創建測試配置
        test_config = {
            'database': {
                'path': os.path.join(self.temp_dir, 'test.db')
            },
            'logging': {
                'log_dir': os.path.join(self.temp_dir, 'logs'),
                'enable_console': False
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(test_config, f)
    
    def tearDown(self):
        reset_app_factory()
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_factory_creation(self):
        """測試工廠創建"""
        factory = ApplicationFactory(self.config_file)
        self.assertIsNotNone(factory.get_container())
    
    def test_service_resolution(self):
        """測試服務解析"""
        factory = ApplicationFactory(self.config_file)
        
        # 測試配置管理器
        config_manager = factory.get_config_manager()
        self.assertIsNotNone(config_manager)
        
        # 測試日誌管理器
        logger = factory.get_logger()
        self.assertIsNotNone(logger)
        
        # 測試數據庫管理器
        db_manager = factory.get_db_manager()
        self.assertIsNotNone(db_manager)
    
    def test_model_creation(self):
        """測試模型創建"""
        factory = ApplicationFactory(self.config_file)
        
        # 由於模型依賴可能不完整，這裡只測試工廠方法存在
        self.assertTrue(hasattr(factory, 'create_combined_model'))
        self.assertTrue(hasattr(factory, 'create_price_predictor'))
        self.assertTrue(hasattr(factory, 'create_news_model'))
    
    def test_health_check(self):
        """測試健康檢查"""
        factory = ApplicationFactory(self.config_file)
        health_status = factory.health_check()
        
        self.assertIn('status', health_status)
        self.assertIn('components', health_status)
        self.assertIn('config', health_status['components'])
        self.assertIn('database', health_status['components'])
        self.assertIn('logging', health_status['components'])
    
    def test_singleton_factory(self):
        """測試單例工廠"""
        factory1 = get_app_factory(self.config_file)
        factory2 = get_app_factory(self.config_file)
        
        self.assertIs(factory1, factory2)


class TestExceptionHandling(unittest.TestCase):
    """測試異常處理"""
    
    def test_handle_exception_decorator(self):
        """測試異常處理裝飾器"""
        
        @handle_exception
        def test_function_success():
            return "success"
        
        @handle_exception
        def test_function_failure():
            raise ValueError("測試錯誤")
        
        # 測試成功情況
        result = test_function_success()
        self.assertEqual(result, "success")
        
        # 測試失敗情況
        with self.assertRaises(StockAnalysisException):
            test_function_failure()
    
    def test_custom_exceptions(self):
        """測試自定義異常"""
        # 測試數據庫錯誤
        db_error = DatabaseError("數據庫連接失敗", error_code="DB001")
        self.assertEqual(db_error.error_code, "DB001")
        self.assertIn("數據庫連接失敗", str(db_error))
        
        # 測試模型錯誤
        model_error = ModelError("模型訓練失敗", error_code="MODEL001")
        self.assertEqual(model_error.error_code, "MODEL001")
        self.assertIn("模型訓練失敗", str(model_error))
        
        # 測試配置錯誤
        config_error = ConfigurationError("配置文件無效")
        self.assertIn("配置文件無效", str(config_error))


class TestIntegration(unittest.TestCase):
    """集成測試"""
    
    def setUp(self):
        reset_app_factory()
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'test_config.json')
        
        # 創建完整的測試配置
        test_config = {
            'database': {
                'path': os.path.join(self.temp_dir, 'test.db'),
                'pool_size': 5,
                'max_overflow': 10
            },
            'logging': {
                'level': 'INFO',
                'log_dir': os.path.join(self.temp_dir, 'logs'),
                'enable_console': False,
                'enable_file': True
            },
            'api': {
                'alpha_vantage_key': 'test_key',
                'request_timeout': 30.0
            },
            'model': {
                'prediction_days': 30,
                'sentiment_weight': 0.3,
                'technical_weight': 0.7
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(test_config, f)
    
    def tearDown(self):
        reset_app_factory()
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_full_application_lifecycle(self):
        """測試完整應用程序生命週期"""
        # 創建應用程序工廠
        with ApplicationFactory(self.config_file) as factory:
            # 獲取服務
            config_manager = factory.get_config_manager()
            logger = factory.get_logger()
            db_manager = factory.get_db_manager()
            
            # 測試配置
            self.assertEqual(config_manager.get('database.pool_size'), 5)
            self.assertEqual(config_manager.get('model.prediction_days'), 30)
            
            # 測試日誌
            logger.info("集成測試開始")
            
            # 測試數據庫
            with db_manager.get_session() as session:
                result = session.execute("SELECT 1 as test")
                self.assertIsNotNone(result)
            
            # 測試健康檢查
            health_status = factory.health_check()
            self.assertEqual(health_status['status'], 'healthy')
            
            logger.info("集成測試完成")


if __name__ == '__main__':
    # 設置測試套件
    test_suite = unittest.TestSuite()
    
    # 添加測試類
    test_classes = [
        TestDependencyInjection,
        TestEnhancedConfigManager,
        TestEnhancedLogger,
        TestEnhancedDatabaseManager,
        TestApplicationFactory,
        TestExceptionHandling,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 運行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 輸出結果
    if result.wasSuccessful():
        print("\n✅ 所有測試通過！")
    else:
        print(f"\n❌ 測試失敗: {len(result.failures)} 個失敗, {len(result.errors)} 個錯誤")