#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新聞收集器單元測試
"""

import os
import sys
import tempfile
import unittest
from datetime import date, datetime
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data_collectors.news_collector import NewsCollector
from utils.db_manager import DatabaseManager


class TestNewsCollector(unittest.TestCase):
    """
    新聞收集器測試類
    """

    def setUp(self):
        """
        測試前準備
        """
        # 創建臨時數據庫
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        self.temp_db.close()
        self.db_path = self.temp_db.name

        # 初始化資料庫表格
        import sqlite3

        from database.createtable import create_tables

        conn = sqlite3.connect(self.db_path)
        create_tables(conn)
        conn.close()

        # 初始化新聞收集器
        self.news_collector = NewsCollector(self.db_path)

    def tearDown(self):
        """
        測試後清理
        """
        # 關閉數據庫連接
        if hasattr(self.news_collector, "conn") and self.news_collector.conn:
            self.news_collector.conn.close()

        # 刪除臨時數據庫
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)

    def test_initialization(self):
        """
        測試NewsCollector初始化
        """
        self.assertIsNotNone(self.news_collector.conn)
        self.assertIsNotNone(self.news_collector.logger)

    def test_save_news_to_database(self):
        """
        測試保存新聞到數據庫
        """
        # 準備測試新聞數據
        news_data = {
            "title": "台積電Q3財報亮眼",
            "content": "台積電第三季營收創新高，獲利表現優異...",
            "url": "https://example.com/news/1",
            "publish_date": date.today(),
            "source": "test_source",
            "fetch_date": date.today(),
        }

        # 保存新聞
        news_id = self.news_collector._save_news_to_db(news_data)

        # 驗證保存結果
        self.assertIsNotNone(news_id)
        self.assertIsInstance(news_id, int)

        # 從數據庫查詢驗證
        cursor = self.news_collector.conn.cursor()
        cursor.execute("SELECT * FROM stock_news WHERE news_id = ?", (news_id,))
        result = cursor.fetchall()

        self.assertEqual(len(result), 1)
        saved_news = result[0]
        self.assertEqual(saved_news[1], news_data["title"])  # title
        self.assertEqual(saved_news[2], news_data["content"])  # content
        self.assertEqual(saved_news[3], news_data["url"])  # url

    def test_save_duplicate_news(self):
        """
        測試保存重複新聞
        """
        news_data = {
            "title": "測試新聞標題",
            "content": "這是一則測試新聞內容",
            "url": "https://example.com/test-news",
            "publish_date": date.today(),
            "source": "test_source",
            "fetch_date": date.today(),
        }

        # 第一次保存
        news_id1 = self.news_collector._save_news_to_db(news_data)

        # 第二次保存相同新聞
        news_id2 = self.news_collector._save_news_to_db(news_data)

        # 檢查是否正確處理重複（根據實現可能返回相同ID或None）
        if news_id2 is not None:
            self.assertEqual(news_id1, news_id2)

        # 檢查數據庫中只有一條記錄
        cursor = self.news_collector.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM stock_news WHERE url = ?", (news_data["url"],))
        result = cursor.fetchall()
        self.assertEqual(result[0][0], 1)

    @patch("requests.get")
    def test_collect_twse_news_success(self, mock_get):
        """
        測試成功獲取證交所新聞
        """
        # 模擬成功的HTTP響應
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": [
                {
                    "title": "測試新聞標題",
                    "content": "測試新聞內容",
                    "publishDate": "2024-01-01",
                    "url": "https://example.com/news/1",
                }
            ]
        }
        mock_get.return_value = mock_response

        # 執行新聞獲取（傳入 days 參數）
        news_list = self.news_collector.collect_twse_news(days=7)

        # 驗證結果
        self.assertIsInstance(news_list, list)
        if news_list:  # 如果有返回新聞
            self.assertIn("title", news_list[0])
            self.assertIn("content", news_list[0])

    @patch("requests.get")
    def test_collect_twse_news_failure(self, mock_get):
        """
        測試獲取證交所新聞失敗
        """
        # 模擬失敗的HTTP響應
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.raise_for_status.side_effect = Exception("HTTP 404")
        mock_get.return_value = mock_response

        # 執行新聞獲取（傳入 days 參數）
        news_list = self.news_collector.collect_twse_news(days=7)

        # 應該返回空列表或None
        self.assertTrue(news_list is None or len(news_list) == 0)

    @patch("requests.get")
    def test_collect_money_news_success(self, mock_get):
        """
        測試成功獲取經濟日報新聞
        """
        # 模擬HTML響應
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <div class="news-item">
                <h3><a href="/news/1">測試新聞標題</a></h3>
                <p>測試新聞摘要</p>
                <time>2024-01-01</time>
            </div>
        </html>
        """
        mock_get.return_value = mock_response

        # 執行新聞獲取（傳入 days 參數）
        news_list = self.news_collector.collect_money_news(days=7)

        # 驗證結果（根據實際實現調整）
        self.assertIsInstance(news_list, list)

    @patch("data_collectors.news_collector.NewsCollector.collect_twse_news")
    @patch("data_collectors.news_collector.NewsCollector.collect_money_news")
    def test_collect_all_news(self, mock_money, mock_twse):
        """
        測試收集所有新聞
        """
        # 模擬新聞數據
        mock_twse.return_value = [
            {
                "title": "證交所新聞1",
                "content": "證交所新聞內容1",
                "url": "https://twse.com/news/1",
                "published_date": date.today(),
                "source": "TWSE",
            }
        ]

        mock_money.return_value = [
            {
                "title": "經濟日報新聞1",
                "content": "經濟日報新聞內容1",
                "url": "https://economic.com/news/1",
                "published_date": date.today(),
                "source": "Economic Daily",
            }
        ]

        # 執行收集 - 調用實際存在的方法
        twse_result = self.news_collector.collect_twse_news()
        money_result = self.news_collector.collect_money_news()

        # 驗證 mock 方法被調用
        mock_twse.assert_called_once()
        mock_money.assert_called_once()

        # 驗證返回結果
        self.assertIsInstance(twse_result, list)
        self.assertIsInstance(money_result, list)
        self.assertEqual(len(twse_result), 1)
        self.assertEqual(len(money_result), 1)

        # 手動保存測試新聞到資料庫以驗證保存功能
        test_news = {
            "title": "測試新聞",
            "content": "測試新聞內容",
            "url": "https://test.com/news/1",
            "publish_date": date.today(),
            "source": "test_source",
            "fetch_date": date.today(),
        }

        news_id = self.news_collector._save_news_to_db(test_news)
        self.assertIsNotNone(news_id)

        # 檢查數據庫中的新聞數量
        cursor = self.news_collector.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM stock_news")
        db_result = cursor.fetchall()
        total_news = db_result[0][0]
        self.assertGreater(total_news, 0)

    def test_extract_stock_mentions(self):
        """
        測試提取股票提及
        """
        # 先插入一些公司資料
        companies = [
            ("2330", "台積電", "半導體", "上市"),
            ("2317", "鴻海", "電子", "上市"),
            ("2454", "聯發科", "半導體", "上市"),
        ]

        for company in companies:
            cursor = self.news_collector.conn.cursor()
            cursor.execute(
                "INSERT INTO company_info (stock_id, name, industry, market) VALUES (?, ?, ?, ?)",
                company,
            )
            self.news_collector.conn.commit()

        # 測試文本
        test_text = "台積電今日股價上漲，鴻海也表現不錯，但聯發科下跌。"

        # 先保存一則測試新聞
        news_data = {
            "title": "股市新聞",
            "content": test_text,
            "url": "https://example.com/test",
            "publish_date": date.today(),
            "source": "test_source",
            "fetch_date": date.today(),
        }

        news_id = self.news_collector._save_news_to_db(news_data)

        # 分析股票提及
        self.news_collector._analyze_stock_mentions(news_id, test_text)

        # 檢查結果
        cursor = self.news_collector.conn.cursor()
        cursor.execute("SELECT stock_id FROM news_stock_relation WHERE news_id = ?", (news_id,))
        mentioned_stocks = cursor.fetchall()

        # 驗證結果
        self.assertGreater(len(mentioned_stocks), 0)

        # 檢查是否找到了提及的股票
        mentioned_codes = [stock[0] for stock in mentioned_stocks]
        self.assertIn("2330", mentioned_codes)  # 台積電
        self.assertIn("2317", mentioned_codes)  # 鴻海
        self.assertIn("2454", mentioned_codes)  # 聯發科

    def test_analyze_news_sentiment(self):
        """
        測試新聞情緒分析
        """
        # 先保存一則新聞
        news_data = {
            "title": "台積電業績大好",
            "content": "台積電第三季業績表現優異，營收創新高，投資人信心大增。",
            "url": "https://example.com/good-news",
            "publish_date": date.today(),
            "source": "test_source",
            "fetch_date": date.today(),
        }

        news_id = self.news_collector._save_news_to_db(news_data)

        # 執行情緒分析
        self.news_collector._analyze_sentiment(news_id, news_data["title"], news_data["content"])

        # 檢查情緒分析結果
        cursor = self.news_collector.conn.cursor()
        cursor.execute("SELECT * FROM news_sentiment WHERE news_id = ?", (news_id,))
        result = cursor.fetchall()

        self.assertEqual(len(result), 1)
        sentiment = result[0]

        # 檢查情緒分數是否在合理範圍內
        positive_score = sentiment[2]  # positive_score
        negative_score = sentiment[3]  # negative_score
        neutral_score = sentiment[4]  # neutral_score

        self.assertGreaterEqual(positive_score, 0)
        self.assertLessEqual(positive_score, 1)
        self.assertGreaterEqual(negative_score, 0)
        self.assertLessEqual(negative_score, 1)
        self.assertGreaterEqual(neutral_score, 0)
        self.assertLessEqual(neutral_score, 1)

    def test_error_handling(self):
        """
        測試錯誤處理
        """
        # 測試無效的新聞數據
        invalid_news = {
            "title": None,  # 無效標題
            "content": "",  # 空內容
            "url": "invalid-url",  # 無效URL
            "published_date": "invalid-date",  # 無效日期
            "source": "",
        }

        # 嘗試保存無效新聞（應該處理錯誤）
        try:
            result = self.news_collector._save_news_to_db(invalid_news)
            # 根據實現，可能返回None或拋出異常
        except Exception:
            # 如果拋出異常，這是預期的
            pass

    def test_date_parsing(self):
        """
        測試日期解析
        """
        # 測試不同格式的日期字符串
        date_strings = ["2024-01-01", "2024/01/01", "01-01-2024", "2024年1月1日"]

        for date_str in date_strings:
            try:
                # 如果有日期解析方法，測試它
                if hasattr(self.news_collector, "_parse_date"):
                    parsed_date = self.news_collector._parse_date(date_str)
                    self.assertIsInstance(parsed_date, (date, datetime))
            except Exception:
                # 某些格式可能不被支持
                pass


class TestNewsCollectorIntegration(unittest.TestCase):
    """
    新聞收集器整合測試
    """

    def setUp(self):
        """
        測試前準備
        """
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        self.temp_db.close()
        self.db_path = self.temp_db.name

        # 初始化資料庫表格
        import sqlite3

        from database.createtable import create_tables

        conn = sqlite3.connect(self.db_path)
        create_tables(conn)
        conn.close()
        self.news_collector = NewsCollector(self.db_path)

    def tearDown(self):
        """
        測試後清理
        """
        if hasattr(self.news_collector, "db_manager") and self.news_collector.db_manager:
            self.news_collector.db_manager.close_connection()

        if os.path.exists(self.db_path):
            os.unlink(self.db_path)

    @unittest.skip("需要網路連接的整合測試")
    def test_real_news_collection(self):
        """
        真實新聞收集測試（需要網路連接）
        """
        # 執行真實的新聞收集
        result = self.news_collector.collect_news()

        # 驗證結果
        self.assertIsInstance(result, dict)
        self.assertIn("total_collected", result)

        # 檢查是否有新聞被收集
        if result["total_collected"] > 0:
            # 檢查數據庫中的新聞
            cursor = self.news_collector.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM stock_news")
            db_result = cursor.fetchall()
            self.assertGreater(db_result[0][0], 0)

    def test_complete_workflow(self):
        """
        測試完整工作流程
        """
        # 先添加一些公司資料
        companies = [("2330", "台積電", "半導體", "上市"), ("2317", "鴻海", "電子", "上市")]

        for company in companies:
            cursor = self.news_collector.conn.cursor()
            cursor.execute(
                "INSERT INTO company_info (stock_id, name, industry, market) VALUES (?, ?, ?, ?)",
                company,
            )
            self.news_collector.conn.commit()

        # 手動添加測試新聞
        news_data = {
            "title": "台積電與鴻海合作案",
            "content": "台積電宣布與鴻海達成重要合作協議，雙方將在半導體領域深度合作。",
            "url": "https://example.com/cooperation",
            "publish_date": date.today(),
            "source": "test_source",
            "fetch_date": date.today(),
        }

        # 保存新聞
        news_id = self.news_collector._save_news_to_db(news_data)

        # 分析股票提及
        self.news_collector._analyze_stock_mentions(
            news_id, news_data["title"] + " " + news_data["content"]
        )

        # 股票關聯已在 _analyze_stock_mentions 中處理

        # 執行情緒分析
        self.news_collector._analyze_sentiment(news_id, news_data["title"], news_data["content"])

        # 驗證完整流程結果
        # 檢查新聞是否保存
        cursor = self.news_collector.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM stock_news")
        news_result = cursor.fetchall()
        self.assertEqual(news_result[0][0], 1)

        # 檢查股票關聯是否保存
        cursor.execute("SELECT COUNT(*) FROM news_stock_relation")
        relation_result = cursor.fetchall()
        self.assertGreater(relation_result[0][0], 0)

        # 檢查情緒分析是否完成
        cursor.execute("SELECT COUNT(*) FROM news_sentiment")
        sentiment_result = cursor.fetchall()
        self.assertEqual(sentiment_result[0][0], 1)


if __name__ == "__main__":
    # 運行測試
    unittest.main(verbosity=2)
