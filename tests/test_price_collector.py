#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
價格收集器單元測試
"""

import os
import sqlite3
import sys
import tempfile
import unittest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

import pandas as pd

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data_collectors.price_collector import PriceCollector
from utils.db_manager import DatabaseManager


class TestPriceCollector(unittest.TestCase):
    """
    價格收集器測試類
    """

    def setUp(self):
        """
        測試前準備
        """
        # 創建臨時資料庫
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        self.temp_db.close()

        # 初始化價格收集器
        self.collector = PriceCollector(database_path=self.temp_db.name)

    def tearDown(self):
        """
        測試後清理
        """
        # 關閉連接
        if hasattr(self.collector, "conn"):
            self.collector.conn.close()

        # 刪除臨時資料庫
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)

    def test_database_connection(self):
        """
        測試資料庫連接
        """
        self.assertIsNotNone(self.collector.conn)
        self.assertIsInstance(self.collector.conn, sqlite3.Connection)

    def test_fetch_twse_stock_list(self):
        """
        測試獲取證交所股票清單
        """
        try:
            stock_list = self.collector.fetch_twse_stock_list()

            # 檢查返回結果
            self.assertIsInstance(stock_list, list)

            if stock_list:  # 如果有數據
                # 檢查第一個股票的格式
                first_stock = stock_list[0]
                self.assertIsInstance(first_stock, dict)
                self.assertIn("stock_id", first_stock)
                self.assertIn("name", first_stock)

                # 檢查股票代碼格式
                stock_id = first_stock["stock_id"]
                self.assertTrue(stock_id.isdigit())
                self.assertTrue(len(stock_id) >= 4)

        except Exception as e:
            # 網路問題時跳過測試
            self.skipTest(f"網路連接問題，跳過測試: {e}")

    def test_save_stock_prices(self):
        """
        測試保存股價數據
        """
        # 準備測試數據
        test_data = pd.DataFrame(
            {
                "date": ["2024-01-01", "2024-01-02"],
                "open": [100.0, 101.0],
                "high": [105.0, 106.0],
                "low": [99.0, 100.0],
                "close": [102.0, 103.0],
                "volume": [1000000, 1100000],
            }
        )

        # 保存股價數據
        self.collector.save_stock_prices("2330", test_data)

        # 從數據庫查詢驗證
        query_result = self.collector.conn.execute(
            "SELECT COUNT(*) FROM daily_prices WHERE stock_id = ?", ("2330",)
        ).fetchone()

        self.assertGreater(query_result[0], 0)

    def test_save_technical_indicators(self):
        """
        測試保存技術指標
        """
        # 準備測試技術指標數據
        test_indicators = pd.DataFrame(
            {
                "date": ["2024-01-01", "2024-01-02"],
                "MA5": [100.0, 101.0],
                "MA20": [98.0, 99.0],
                "RSI": [50.0, 52.0],
                "MACD": [1.0, 1.2],
                "KD": [45.0, 47.0],
            }
        )

        # 保存技術指標
        self.collector.save_technical_indicators("2330", test_indicators)

        # 從數據庫查詢驗證
        query_result = self.collector.conn.execute(
            "SELECT COUNT(*) FROM technical_indicators WHERE stock_id = ?", ("2330",)
        ).fetchone()

        self.assertGreater(query_result[0], 0)

    def test_duplicate_data_handling(self):
        """
        測試重複數據處理
        """
        # 準備相同的測試數據
        test_data = pd.DataFrame(
            {
                "date": ["2024-01-01"],
                "open": [100.0],
                "high": [105.0],
                "low": [99.0],
                "close": [102.0],
                "volume": [1000000],
            }
        )

        # 第一次保存
        self.collector.save_stock_prices("2330", test_data)

        # 第二次保存相同數據
        self.collector.save_stock_prices("2330", test_data)

        # 驗證重複數據處理
        query_result = self.collector.conn.execute(
            "SELECT COUNT(*) FROM daily_prices WHERE stock_id = ? AND date = ?",
            ("2330", "2024-01-01"),
        ).fetchone()

        # 應該只有一條記錄（由於 drop_duplicates）
        self.assertEqual(query_result[0], 1)

    def test_date_range_validation(self):
        """
        測試日期範圍驗證
        """
        # 測試有效日期範圍
        start_date = "2023-01-01"
        end_date = "2023-01-31"

        # 這裡可以添加日期範圍驗證的邏輯
        # 由於原始代碼中沒有明確的日期驗證方法，這裡只做基本檢查
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        self.assertLess(start_dt, end_dt)
        self.assertLessEqual((end_dt - start_dt).days, 365)  # 不超過一年


class TestPriceCollectorIntegration(unittest.TestCase):
    """
    價格收集器整合測試
    """

    def setUp(self):
        """
        測試前準備
        """
        # 創建臨時資料庫
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix=".db")
        self.temp_db.close()

        # 初始化價格收集器
        self.collector = PriceCollector(database_path=self.temp_db.name)

    def tearDown(self):
        """
        測試後清理
        """
        # 關閉連接
        if hasattr(self.collector, "conn"):
            self.collector.conn.close()

        # 刪除臨時資料庫
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)

    @unittest.skip("需要網路連接，跳過整合測試")
    def test_full_data_collection_workflow(self):
        """
        測試完整的數據收集流程
        """
        # 獲取股票清單
        stock_list = self.collector.fetch_twse_stock_list()
        self.assertGreater(len(stock_list), 0)

        # 選擇一支股票進行測試
        test_stock = stock_list[0]["stock_id"]

        # 獲取股價數據
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

        prices = self.collector.fetch_stock_prices(test_stock, start_date, end_date)

        if prices is not None and not prices.empty:
            # 保存數據
            result = self.collector.save_stock_prices(prices)
            self.assertTrue(result)

            # 計算技術指標
            indicators = self.collector.calculate_technical_indicators(prices)
            self.assertIsNotNone(indicators)

            # 保存技術指標
            result = self.collector.save_technical_indicators(test_stock, indicators)
            self.assertTrue(result)


if __name__ == "__main__":
    # 運行測試
    unittest.main(verbosity=2)
