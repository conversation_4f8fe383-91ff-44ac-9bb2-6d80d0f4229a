"""工具函數的單元測試。"""

from datetime import date, datetime
from unittest.mock import Mock, patch

import pytest

from tests.conftest import TestBase


class TestDateUtils(TestBase):
    """日期工具函數測試。"""

    def test_date_formatting(self):
        """測試日期格式化。"""
        test_date = date(2024, 1, 15)
        # 這裡可以測試日期格式化函數
        assert test_date.strftime("%Y-%m-%d") == "2024-01-15"

    def test_date_range_validation(self):
        """測試日期範圍驗證。"""
        start_date = date(2024, 1, 1)
        end_date = date(2024, 1, 31)
        assert start_date < end_date

    def test_business_days_calculation(self):
        """測試工作日計算。"""
        # 這裡可以測試工作日計算邏輯
        monday = date(2024, 1, 1)  # 假設是週一
        friday = date(2024, 1, 5)  # 假設是週五
        # 實際的工作日計算邏輯需要根據具體實現來測試
        assert friday > monday


class TestStringUtils(TestBase):
    """字符串工具函數測試。"""

    def test_string_cleaning(self):
        """測試字符串清理。"""
        dirty_string = "  測試字符串  \n\t  "
        cleaned = dirty_string.strip()
        assert cleaned == "測試字符串"

    def test_text_normalization(self):
        """測試文本標準化。"""
        text = "這是一個測試文本！"
        # 這裡可以測試文本標準化函數
        assert len(text) > 0
        assert "測試" in text

    def test_sentiment_text_preprocessing(self):
        """測試情感分析文本預處理。"""
        text = "台積電股價上漲，投資人樂觀看待！！！"
        # 移除多餘標點符號
        cleaned = text.replace("！！！", "！")
        assert cleaned == "台積電股價上漲，投資人樂觀看待！"


class TestValidationUtils(TestBase):
    """驗證工具函數測試。"""

    def test_stock_symbol_validation(self):
        """測試股票代碼驗證。"""
        valid_symbols = ["2330", "0050", "1234"]
        invalid_symbols = ["", "abc", "12345", "123a"]

        for symbol in valid_symbols:
            assert symbol.isdigit() and len(symbol) == 4

        for symbol in invalid_symbols:
            assert not (symbol.isdigit() and len(symbol) == 4)

    def test_price_validation(self):
        """測試價格驗證。"""
        valid_prices = [100.0, 50.5, 1000.25]
        invalid_prices = [-10.0, 0, "abc"]

        for price in valid_prices:
            assert isinstance(price, (int, float)) and price > 0

        for price in invalid_prices:
            if isinstance(price, (int, float)):
                assert price <= 0
            else:
                assert not isinstance(price, (int, float))

    def test_url_validation(self):
        """測試 URL 驗證。"""
        valid_urls = [
            "https://www.example.com",
            "http://example.com/path",
            "https://news.example.com/article/123",
        ]
        invalid_urls = ["not_a_url", "ftp://example.com", "", "https://"]

        for url in valid_urls:
            assert url.startswith(("http://", "https://"))

        for url in invalid_urls:
            assert not url.startswith(("http://", "https://")) or len(url) <= 8


class TestConfigUtils(TestBase):
    """配置工具函數測試。"""

    def test_environment_variable_loading(self):
        """測試環境變數載入。"""
        import os

        # 測試設置環境變數
        test_key = "TEST_CONFIG_VAR"
        test_value = "test_value"
        os.environ[test_key] = test_value

        assert os.environ.get(test_key) == test_value

        # 清理
        del os.environ[test_key]

    def test_default_config_values(self):
        """測試默認配置值。"""
        import os

        # 測試默認值
        default_value = "default"
        actual_value = os.environ.get("NON_EXISTENT_VAR", default_value)
        assert actual_value == default_value

    @patch.dict("os.environ", {"TEST_BOOL": "true"})
    def test_boolean_config_parsing(self):
        """測試布林值配置解析。"""
        import os

        bool_value = os.environ.get("TEST_BOOL", "false").lower() == "true"
        assert bool_value is True

    @patch.dict("os.environ", {"TEST_INT": "123"})
    def test_integer_config_parsing(self):
        """測試整數配置解析。"""
        import os

        int_value = int(os.environ.get("TEST_INT", "0"))
        assert int_value == 123
        assert isinstance(int_value, int)


class TestErrorHandling(TestBase):
    """錯誤處理測試。"""

    def test_custom_exception_creation(self):
        """測試自定義異常創建。"""
        # 測試基本異常
        with pytest.raises(ValueError):
            raise ValueError("測試錯誤")

    def test_exception_message_formatting(self):
        """測試異常訊息格式化。"""
        error_msg = "股票代碼 {symbol} 無效"
        formatted_msg = error_msg.format(symbol="INVALID")
        assert formatted_msg == "股票代碼 INVALID 無效"

    def test_error_logging(self, mock_logger):
        """測試錯誤日誌記錄。"""
        error_message = "測試錯誤訊息"

        try:
            raise ValueError(error_message)
        except ValueError as e:
            mock_logger.error(f"捕獲錯誤: {str(e)}")

        mock_logger.error.assert_called_once_with(f"捕獲錯誤: {error_message}")


class TestDataProcessing(TestBase):
    """數據處理測試。"""

    def test_data_cleaning(self):
        """測試數據清理。"""
        dirty_data = [1, 2, None, 4, "", 6]
        cleaned_data = [x for x in dirty_data if x is not None and x != ""]
        assert cleaned_data == [1, 2, 4, 6]

    def test_data_transformation(self):
        """測試數據轉換。"""
        raw_data = ["1", "2", "3"]
        transformed_data = [int(x) for x in raw_data]
        assert transformed_data == [1, 2, 3]
        assert all(isinstance(x, int) for x in transformed_data)

    def test_data_aggregation(self):
        """測試數據聚合。"""
        data = [1, 2, 3, 4, 5]
        total = sum(data)
        average = total / len(data)

        assert total == 15
        assert average == 3.0

    def test_data_filtering(self):
        """測試數據過濾。"""
        data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        even_numbers = [x for x in data if x % 2 == 0]
        odd_numbers = [x for x in data if x % 2 == 1]

        assert even_numbers == [2, 4, 6, 8, 10]
        assert odd_numbers == [1, 3, 5, 7, 9]
