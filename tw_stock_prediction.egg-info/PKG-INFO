Metadata-Version: 2.4
Name: tw-stock-prediction
Version: 1.0.0
Summary: 台灣股票預測系統 - 整合新聞分析與技術指標的智能預測平台
Author-email: Stock Prediction Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/your-username/tw-stock-prediction
Project-URL: Repository, https://github.com/your-username/tw-stock-prediction.git
Project-URL: Documentation, https://tw-stock-prediction.readthedocs.io
Project-URL: Bug Tracker, https://github.com/your-username/tw-stock-prediction/issues
Keywords: stock,prediction,taiwan,machine-learning,finance
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Office/Business :: Financial :: Investment
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: pandas>=1.5.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: scikit-learn>=1.3.0
Requires-Dist: joblib>=1.3.0
Requires-Dist: yfinance>=0.2.0
Requires-Dist: twstock>=1.3.1
Requires-Dist: requests>=2.31.0
Requires-Dist: beautifulsoup4>=4.12.0
Requires-Dist: lxml>=4.9.0
Requires-Dist: selenium>=4.15.0
Requires-Dist: jieba>=0.42.1
Requires-Dist: matplotlib>=3.7.0
Requires-Dist: seaborn>=0.12.0
Requires-Dist: plotly>=5.17.0
Requires-Dist: flask>=2.3.0
Requires-Dist: flask-cors>=4.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: PyYAML>=6.0.1
Requires-Dist: SQLAlchemy>=2.0.0
Requires-Dist: python-dateutil>=2.8.2
Requires-Dist: scipy>=1.11.0
Requires-Dist: tqdm>=4.66.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: aiohttp>=3.9.0
Requires-Dist: FinLab>=0.4.6
Requires-Dist: TA-Lib>=0.6.4
Requires-Dist: pydantic>=2.4.0
Requires-Dist: pydantic-settings>=2.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-cov>=4.1.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-mock>=3.11.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"
Requires-Dist: pre-commit>=3.4.0; extra == "dev"
Requires-Dist: bandit>=1.7.5; extra == "dev"
Requires-Dist: pydocstyle>=6.3.0; extra == "dev"
Requires-Dist: coverage>=7.3.0; extra == "dev"
Provides-Extra: testing
Requires-Dist: pytest>=7.4.0; extra == "testing"
Requires-Dist: pytest-cov>=4.1.0; extra == "testing"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "testing"
Requires-Dist: pytest-mock>=3.11.0; extra == "testing"
Requires-Dist: coverage>=7.3.0; extra == "testing"
Requires-Dist: factory-boy>=3.3.0; extra == "testing"
Requires-Dist: freezegun>=1.2.0; extra == "testing"
Provides-Extra: docs
Requires-Dist: sphinx>=7.1.0; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=1.3.0; extra == "docs"
Requires-Dist: myst-parser>=2.0.0; extra == "docs"
Requires-Dist: sphinx-autodoc-typehints>=1.24.0; extra == "docs"
Provides-Extra: production
Requires-Dist: gunicorn>=21.2.0; extra == "production"
Requires-Dist: redis>=5.0.0; extra == "production"
Requires-Dist: psycopg2-binary>=2.9.0; extra == "production"
Requires-Dist: sentry-sdk>=1.32.0; extra == "production"

# 🚀 台灣股票預測系統

[![CI/CD Pipeline](https://github.com/your-username/tw-stock-prediction/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/your-username/tw-stock-prediction/actions)
[![codecov](https://codecov.io/gh/your-username/tw-stock-prediction/branch/main/graph/badge.svg)](https://codecov.io/gh/your-username/tw-stock-prediction)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

> 🎯 **整合新聞情感分析與技術指標的智能台股預測平台**

一個現代化的台灣股票預測系統，結合機器學習、自然語言處理和技術分析，為投資決策提供數據驅動的洞察。

## 功能特色

### 📊 數據收集
- **股價數據**: 支援從多個來源獲取歷史股價數據
  - 台灣證券交易所 (TWSE)
  - Yahoo Finance
  - FinLab API
- **新聞數據**: 自動收集和分析股票相關新聞
  - 證交所公告
  - 經濟日報
  - 新聞情緒分析
- **財務數據**: 獲取公司財務報告和基本面資訊
  - 季報/年報數據
  - 財務比率計算
  - 三大法人買賣超

### 🔍 技術分析
- 移動平均線 (MA5, MA10, MA20, MA60, MA120, MA240)
- 相對強弱指標 (RSI)
- MACD 指標
- KD 指標
- 布林通道 (Bollinger Bands)
- 本益比、股價淨值比、殖利率

### 🤖 機器學習預測
- **價格預測模型**: 基於技術指標的股價預測
- **新聞情緒模型**: 中文新聞情緒分析
- **綜合預測模型**: 結合技術面、基本面和消息面的綜合預測

### 📈 分析功能
- 股票技術分析
- 財務健康評估
- 新聞情緒追蹤
- 批量股票比較
- 預測準確性評估

## 安裝指南

### 系統需求
- Python 3.8+
- SQLite3
- 8GB+ RAM (推薦)

### 安裝步驟

1. **克隆專案**
```bash
git clone <repository-url>
cd main-news
```

2. **創建虛擬環境**
```bash
python -m venv venv
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows
```

3. **安裝依賴**
```bash
pip install -r requirements.txt
```

4. **配置設定**
```bash
# 複製配置文件
cp config/app_config.json.example config/app_config.json
# 編輯配置文件，設定 API 金鑰等
```

5. **初始化資料庫**
```bash
python -c "from database.createtable import create_database; create_database()"
```

## 使用方法

### 命令行介面

#### 數據下載
```bash
# 下載所有股票歷史數據
python main.py --download-all

# 下載特定股票數據
python main.py --download-historical --stock-id 2330

# 使用 FinLab 快速下載
python main.py --download-historical --use-finlab

# 增量更新數據
python main.py --download-historical --incremental
```

#### 數據更新
```bash
# 更新最新股價
python main.py --update-data

# 檢查缺失數據
python main.py --check-missing
```

#### 股票分析
```bash
# 分析特定股票
python main.py --analyze --stock-id 2330

# 批量分析
python main.py --analyze --stock-ids 2330,2317,2454
```

#### 預測功能
```bash
# 預測股價走勢
python main.py --predict --stock-id 2330

# 訓練預測模型
python main.py --train --stock-id 2330
```

### Python API

```python
from main import StockAnalysisApp

# 初始化應用
app = StockAnalysisApp()

# 下載數據
app.download_historical_data(stock_id="2330")

# 分析股票
result = app.analyze_stock("2330")
print(result)

# 預測股價
prediction = app.predict_stock_movement("2330")
print(f"預測結果: {prediction}")

# 關閉應用
app.close()
```

## 配置說明

### app_config.json
```json
{
  "database": {
    "path": "database/tw_stock_data.db"
  },
  "api": {
    "timeout": 30,
    "retry_count": 3
  },
  "logging": {
    "level": "INFO",
    "file": "logs/app.log",
    "dir": "logs"
  },
  "models": {
    "dir": "models"
  }
}
```

### API 金鑰設定
在 `keys/` 目錄下創建相應的金鑰文件：
- `apikey.txt`: FinLab API 金鑰
- `SecretKey.txt`: 其他 API 金鑰

## 專案結構

```
main-news/
├── analyzers/           # 分析器模組
│   ├── technical_analyzer.py
│   ├── financial_analyzer.py
│   └── news_analyzer.py
├── data_collectors/     # 數據收集器
│   ├── price_collector.py
│   ├── news_collector.py
│   └── financial_collector.py
├── models/             # 機器學習模型
│   ├── price_model.py
│   ├── news_model.py
│   └── combined_model.py
├── database/           # 資料庫相關
│   ├── createtable.py
│   └── tw_stock_data.db
├── config/             # 配置文件
├── logs/              # 日誌文件
├── utils/             # 工具函數
└── main.py            # 主程式
```

## 開發指南

### 添加新的數據源
1. 在 `data_collectors/` 中創建新的收集器
2. 繼承 `CollectorBase` 類
3. 實現必要的方法
4. 在 `CollectorFactory` 中註冊

### 添加新的分析指標
1. 在相應的分析器中添加計算方法
2. 更新資料庫表結構（如需要）
3. 添加單元測試

### 添加新的預測模型
1. 在 `models/` 中創建新模型
2. 繼承 `ModelBase` 類
3. 實現訓練和預測方法
4. 在 `ModelFactory` 中註冊

## 常見問題

### Q: 下載數據時出現連接錯誤
A: 檢查網路連接，或調整 `app_config.json` 中的 `timeout` 和 `retry_count` 設定。

### Q: 預測準確性不高
A: 嘗試增加訓練數據量，調整模型參數，或使用綜合預測模型。

### Q: 新聞情緒分析結果不準確
A: 可以自定義情緒詞典，或訓練專門的金融新聞情緒模型。

## 貢獻指南

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

## 授權

本專案採用 MIT 授權 - 詳見 [LICENSE](LICENSE) 文件

## 聯絡方式

如有問題或建議，請開啟 Issue 或聯絡開發團隊。

---

**注意**: 本系統僅供學習和研究使用，投資決策請謹慎評估風險。
