pandas>=1.5.0
numpy>=1.24.0
scikit-learn>=1.3.0
joblib>=1.3.0
yfinance>=0.2.0
twstock>=1.3.1
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
selenium>=4.15.0
jieba>=0.42.1
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0
flask>=2.3.0
flask-cors>=4.0.0
python-dotenv>=1.0.0
PyYAML>=6.0.1
SQLAlchemy>=2.0.0
python-dateutil>=2.8.2
scipy>=1.11.0
tqdm>=4.66.0
httpx>=0.25.0
aiohttp>=3.9.0
FinLab>=0.4.6
TA-Lib>=0.6.4
pydantic>=2.4.0
pydantic-settings>=2.0.0

[dev]
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.4.0
bandit>=1.7.5
pydocstyle>=6.3.0
coverage>=7.3.0

[docs]
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0
sphinx-autodoc-typehints>=1.24.0

[production]
gunicorn>=21.2.0
redis>=5.0.0
psycopg2-binary>=2.9.0
sentry-sdk>=1.32.0

[testing]
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
coverage>=7.3.0
factory-boy>=3.3.0
freezegun>=1.2.0
