# batch_prediction_tab.py
import csv
import datetime
import os
import threading
import tkinter as tk
from tkinter import filedialog, messagebox, ttk

from core.stock_price_predictor import StockPricePredictor


class BatchPredictionTab:
    def __init__(self, parent, root):
        """初始化批次預測頁面

        Args:
            parent: 父容器
            root: 主視窗
        """
        self.parent = parent
        self.root = root
        self.predictor = None
        self.model_dir = "models"
        self.db_path = "tw_stock_data.db"

        # 建立UI
        self.setup_batch_tab()

    def setup_batch_tab(self):
        """設置批次預測頁面"""
        frame = ttk.LabelFrame(self.parent, text="批次股票漲跌預測")
        frame.pack(expand=True, fill="both", padx=10, pady=10)

        # 控制區
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill="x", padx=10, pady=10)

        # 股票輸入
        stock_frame = ttk.Frame(control_frame)
        stock_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(stock_frame, text="股票代碼清單:").pack(side=tk.LEFT, padx=5)
        self.batch_stock_ids_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.batch_stock_ids_var, width=50).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Label(stock_frame, text="(以逗號分隔，例如: 2330,2454,2317)").pack(side=tk.LEFT, padx=5)

        # 預測天數
        pred_frame = ttk.Frame(control_frame)
        pred_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(pred_frame, text="預測未來幾天:").pack(side=tk.LEFT, padx=5)
        self.batch_days_var = tk.IntVar(value=1)
        ttk.Combobox(
            pred_frame,
            textvariable=self.batch_days_var,
            values=[1, 3, 5, 10],
            width=10,
            state="readonly",
        ).pack(side=tk.LEFT, padx=5)

        # 排序選項
        sort_frame = ttk.Frame(control_frame)
        sort_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(sort_frame, text="排序方式:").pack(side=tk.LEFT, padx=5)
        self.sort_by_var = tk.StringVar(value="機率由高至低")
        ttk.Combobox(
            sort_frame,
            textvariable=self.sort_by_var,
            values=["機率由高至低", "機率由低至高", "股票代碼"],
            width=15,
            state="readonly",
        ).pack(side=tk.LEFT, padx=5)

        # 批次預測按鈕
        predict_btn = ttk.Button(
            control_frame, text="開始批次預測", command=self.batch_predict, style="Accent.TButton"
        )
        predict_btn.pack(side=tk.LEFT, padx=5, pady=10, ipadx=10, ipady=5)

        # 儲存結果按鈕
        save_btn = ttk.Button(control_frame, text="儲存結果", command=self.save_batch_results)
        save_btn.pack(side=tk.LEFT, padx=5, pady=10, ipadx=10, ipady=5)

        # 結果表格
        result_frame = ttk.LabelFrame(frame, text="預測結果")
        result_frame.pack(expand=True, fill="both", padx=10, pady=10)

        # 建立表格
        columns = (
            "股票代碼",
            "預測日期",
            "目標日期",
            "預測天數",
            "預測結果",
            "預測機率",
            "目前價格",
        )
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=10)

        # 設置列標題
        for col in columns:
            self.result_tree.heading(col, text=col)
            self.result_tree.column(col, width=100, anchor="center")

        # 添加滾動條
        scrollbar_y = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_tree.yview)
        scrollbar_x = ttk.Scrollbar(
            result_frame, orient="horizontal", command=self.result_tree.xview
        )
        self.result_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 排列元件
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        self.result_tree.pack(fill="both", expand=True)

    def batch_predict(self):
        """批次預測多檔股票"""
        # 檢查輸入
        stock_ids_text = self.batch_stock_ids_var.get().strip()
        if not stock_ids_text:
            messagebox.showerror("錯誤", "請輸入股票代碼清單")
            return

        # 解析股票代碼
        stock_ids = [s.strip() for s in stock_ids_text.split(",")]
        if not stock_ids:
            messagebox.showerror("錯誤", "無法解析股票代碼清單")
            return

        prediction_days = self.batch_days_var.get()

        # 確保預測器已初始化
        if self.predictor is None:
            self.predictor = StockPricePredictor(
                database_path=self.db_path, model_dir=self.model_dir
            )

        # 清空結果表格
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 更新狀態
        if hasattr(self.root, "status_var"):
            self.root.status_var.set(f"正在批次預測 {len(stock_ids)} 檔股票...")

        # 在背景執行緒中進行批次預測
        batch_thread = threading.Thread(
            target=self.batch_predict_thread, args=(stock_ids, prediction_days)
        )
        batch_thread.daemon = True
        batch_thread.start()

    def batch_predict_thread(self, stock_ids, prediction_days):
        """在背景執行緒中進行批次預測

        Args:
            stock_ids (list): 股票代碼列表
            prediction_days (int): 預測天數
        """
        try:
            results = []

            # 逐一預測每檔股票
            for stock_id in stock_ids:
                # 檢查模型是否存在
                model_path = os.path.join(
                    self.model_dir, f"{stock_id}_pred{prediction_days}d_model.pkl"
                )
                if not os.path.exists(model_path):
                    self.add_batch_result(
                        {
                            "stock_id": stock_id,
                            "prediction_date": "-",
                            "target_date": "-",
                            "prediction_days": prediction_days,
                            "prediction": "模型不存在",
                            "probability": 0,
                            "current_price": 0,
                        }
                    )
                    continue

                # 進行預測
                result = self.predictor.predict_price_movement(
                    stock_id=stock_id, prediction_days=prediction_days
                )

                if result is None:
                    self.add_batch_result(
                        {
                            "stock_id": stock_id,
                            "prediction_date": "-",
                            "target_date": "-",
                            "prediction_days": prediction_days,
                            "prediction": "預測失敗",
                            "probability": 0,
                            "current_price": 0,
                        }
                    )
                    continue

                # 添加結果
                results.append(result)
                self.add_batch_result(result)

            # 排序結果
            self.sort_batch_results()

            # 更新狀態
            if hasattr(self.root, "status_var"):
                self.root.status_var.set(f"已完成 {len(stock_ids)} 檔股票的批次預測")

            # 如果沒有結果
            if not results:
                self.root.after(
                    0,
                    lambda: messagebox.showinfo(
                        "預測完成", "批次預測完成，但沒有取得任何有效結果。"
                    ),
                )

        except Exception as e:
            error_msg = f"批次預測時發生錯誤: {str(e)}"
            if hasattr(self.root, "status_var"):
                self.root.status_var.set(error_msg)

            # 顯示錯誤訊息
            self.root.after(0, lambda: messagebox.showerror("錯誤", error_msg))

    def add_batch_result(self, result):
        """添加批次預測結果到表格

        Args:
            result (dict): 預測結果
        """
        # 轉換為表格顯示格式
        values = (
            result["stock_id"],
            result["prediction_date"] if "prediction_date" in result else "-",
            result["target_date"] if "target_date" in result else "-",
            result["prediction_days"],
            result["prediction"],
            (
                f"{float(result['probability']):.2%}"
                if isinstance(result["probability"], (int, float))
                else "-"
            ),
            result["current_price"] if isinstance(result["current_price"], (int, float)) else "-",
        )

        # 添加到表格
        self.root.after(0, lambda: self.result_tree.insert("", "end", values=values))

    def sort_batch_results(self):
        """根據選定的方式排序批次預測結果"""
        sort_method = self.sort_by_var.get()

        # 取得所有項目
        items = [
            (self.result_tree.item(item, "values"), item)
            for item in self.result_tree.get_children()
        ]

        # 根據排序方式排序
        if sort_method == "機率由高至低":
            # 按預測機率降序排序
            items.sort(key=lambda x: self.extract_probability(x[0][5]), reverse=True)
        elif sort_method == "機率由低至高":
            # 按預測機率升序排序
            items.sort(key=lambda x: self.extract_probability(x[0][5]))
        elif sort_method == "股票代碼":
            # 按股票代碼排序
            items.sort(key=lambda x: x[0][0])

        # 重新插入項目
        for values, item in items:
            self.result_tree.move(item, "", "end")

    def extract_probability(self, prob_str):
        """從機率字符串中提取數值

        Args:
            prob_str (str): 機率字符串，如 "85.5%"

        Returns:
            float: 機率值，範圍 0-1
        """
        try:
            if prob_str == "-":
                return 0
            return float(prob_str.strip("%")) / 100
        except:
            return 0

    def save_batch_results(self):
        """保存批次預測結果到CSV檔案"""
        # 檢查是否有結果
        if not self.result_tree.get_children():
            messagebox.showinfo("提示", "沒有可保存的預測結果")
            return

        # 選擇保存位置
        file_path = filedialog.asksaveasfilename(
            title="保存預測結果",
            defaultextension=".csv",
            filetypes=(("CSV檔案", "*.csv"), ("所有檔案", "*.*")),
            initialfile=f"股票預測結果_{datetime.datetime.now().strftime('%Y%m%d')}.csv",
        )

        if not file_path:
            return

        try:
            # 準備匯出資料
            results = []
            columns = self.result_tree.cget("columns")

            # 添加表頭
            results.append(columns)

            # 添加每一行資料
            for item in self.result_tree.get_children():
                values = self.result_tree.item(item, "values")
                results.append(values)

            # 寫入CSV
            with open(file_path, "w", newline="", encoding="utf-8-sig") as f:
                writer = csv.writer(f)
                writer.writerows(results)

            messagebox.showinfo("保存成功", f"預測結果已保存至:\n{file_path}")

        except Exception as e:
            messagebox.showerror("錯誤", f"保存結果時發生錯誤:\n{str(e)}")
