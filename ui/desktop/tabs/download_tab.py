# download_tab.py
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import logging
from core.stock_data_downloader import StockDataDownloader

class DownloadTab:
    def __init__(self, parent, root):
        """初始化下載頁面
        
        Args:
            parent: 父容器
            root: 主視窗
        """
        self.parent = parent
        self.root = root
        self.downloader = None
        self.db_path = "tw_stock_data.db"
        
        # 建立UI
        self.setup_download_tab()
    
    def setup_download_tab(self):
        """設置資料下載頁面"""
        frame = ttk.LabelFrame(self.parent, text="FinLab 資料下載")
        frame.pack(expand=True, fill="both", padx=10, pady=10)
        
        # API金鑰
        api_frame = ttk.Frame(frame)
        api_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(api_frame, text="API金鑰:").pack(side=tk.LEFT, padx=5)
        self.api_key_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.api_key_var, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(api_frame, text="選擇金鑰檔案", command=self.select_api_key_file).pack(side=tk.LEFT, padx=5)
        
        # 資料庫路徑
        db_frame = ttk.Frame(frame)
        db_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(db_frame, text="資料庫路徑:").pack(side=tk.LEFT, padx=5)
        self.db_path_var = tk.StringVar(value=self.db_path)
        ttk.Entry(db_frame, textvariable=self.db_path_var, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(db_frame, text="選擇位置", command=self.select_db_path).pack(side=tk.LEFT, padx=5)
        
        # 資料選項
        options_frame = ttk.LabelFrame(frame, text="下載選項")
        options_frame.pack(fill="x", padx=10, pady=10)
        
        # 資料選項 - 上半部
        options_top = ttk.Frame(options_frame)
        options_top.pack(fill="x", padx=5, pady=5)
        
        self.option_price = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_top, text="價格資料", variable=self.option_price).pack(side=tk.LEFT, padx=15)
        
        self.option_financial = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_top, text="財務資料", variable=self.option_financial).pack(side=tk.LEFT, padx=15)
        
        self.option_technical = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_top, text="技術指標", variable=self.option_technical).pack(side=tk.LEFT, padx=15)
        
        self.option_institutional = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_top, text="三大法人", variable=self.option_institutional).pack(side=tk.LEFT, padx=15)
        
   # 資料選項 - 下半部
        options_bottom = ttk.Frame(options_frame)
        options_bottom.pack(fill="x", padx=5, pady=5)
        self.option_twse_inst = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_bottom, text="證交所主力", variable=self.option_twse_inst).pack(side=tk.LEFT, padx=15)
        self.option_margin = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_bottom, text="融資融券", variable=self.option_margin).pack(side=tk.LEFT, padx=15)
        
        self.option_revenue = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_bottom, text="月營收", variable=self.option_revenue).pack(side=tk.LEFT, padx=15)
        
        self.option_company = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_bottom, text="公司資訊", variable=self.option_company).pack(side=tk.LEFT, padx=15)
        
        self.option_economic = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_bottom, text="經濟指標", variable=self.option_economic).pack(side=tk.LEFT, padx=15)
        
        # 全選按鈕
        select_all_btn = ttk.Button(options_frame, text="全選", command=self.select_all_options)
        select_all_btn.pack(side=tk.RIGHT, padx=10, pady=5)
        
        # 下載按鈕
        download_frame = ttk.Frame(frame)
        download_frame.pack(fill="x", padx=10, pady=20)
        
        ttk.Button(
            download_frame, 
            text="開始下載所有資料", 
            command=self.start_download,
            style="Accent.TButton"
        ).pack(side=tk.TOP, padx=5, pady=10, ipadx=10, ipady=5)
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            download_frame, 
            variable=self.progress_var, 
            maximum=100, 
            length=500, 
            mode="determinate"
        )
        self.progress_bar.pack(side=tk.TOP, padx=5, pady=10)
        
        # 日誌顯示
        log_frame = ttk.LabelFrame(frame, text="下載日誌")
        log_frame.pack(expand=True, fill="both", padx=10, pady=10)
        
        self.log_text = tk.Text(log_frame, height=10, width=80)
        self.log_text.pack(side=tk.LEFT, fill="both", expand=True, padx=5, pady=5)
        
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
    
    def select_api_key_file(self):
        """選擇API金鑰檔案"""
        file_path = filedialog.askopenfilename(
            title="選擇API金鑰檔案",
            filetypes=(("文字檔", "*.txt"), ("所有檔案", "*.*"))
        )
        if file_path:
            try:
                with open(file_path, "r") as f:
                    api_key = f.read().strip()
                    self.api_key_var.set(api_key)
                self.log_text.insert(tk.END, f"已選擇API金鑰檔案: {file_path}\n")
                self.log_text.see(tk.END)
            except Exception as e:
                messagebox.showerror("錯誤", f"讀取API金鑰檔案時發生錯誤:\n{str(e)}")
    
    def select_db_path(self):
        """選擇資料庫儲存路徑"""
        file_path = filedialog.asksaveasfilename(
            title="選擇資料庫儲存路徑",
            defaultextension=".db",
            filetypes=(("SQLite資料庫", "*.db"), ("所有檔案", "*.*")),
            initialfile=self.db_path
        )
        if file_path:
            self.db_path = file_path
            self.db_path_var.set(file_path)
    
    def select_all_options(self):
        """全選所有下載選項"""
        for option in [
            self.option_price, self.option_financial, 
            self.option_technical, self.option_institutional,
            self.option_margin, self.option_revenue, 
            self.option_company, self.option_economic
        ]:
            option.set(True)
    
    def start_download(self):
        """開始下載資料"""
        # 取得API金鑰
        api_key = self.api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("錯誤", "請提供API金鑰")
            return
        
        # 取得資料庫路徑
        db_path = self.db_path_var.get().strip()
        if not db_path:
            messagebox.showerror("錯誤", "請提供資料庫路徑")
            return
        
        # 確認選項
        selected_options = []
        if self.option_price.get():
            selected_options.append("價格資料")
        if self.option_financial.get():
            selected_options.append("財務資料")
        if self.option_technical.get():
            selected_options.append("技術指標")
        if self.option_institutional.get():
            selected_options.append("三大法人")
        if self.option_margin.get():
            selected_options.append("融資融券")
        if self.option_revenue.get():
            selected_options.append("月營收")
        if self.option_company.get():
            selected_options.append("公司資訊")
        if self.option_economic.get():
            selected_options.append("經濟指標")
        
        if not selected_options:
            messagebox.showerror("錯誤", "請至少選擇一種資料類型")
            return
        
        # 確認是否繼續下載
        confirm = messagebox.askyesno(
            "確認下載", 
            f"即將下載以下資料類型:\n{', '.join(selected_options)}\n\n"
            f"資料將儲存到:\n{db_path}\n\n"
            "此過程可能需要一段時間。要繼續嗎？"
        )
        
        if not confirm:
            return
        
        # 開始下載執行緒
        self.progress_var.set(0)
        self.log_text.insert(tk.END, "=== 開始下載資料 ===\n")
        self.log_text.see(tk.END)
        
        # 建立下載執行緒
        download_thread = threading.Thread(
            target=self.download_data_thread,
            args=(api_key, db_path, selected_options)
        )
        download_thread.daemon = True
        download_thread.start()
    
    def download_data_thread(self, api_key, db_path, selected_options):
        """在背景執行緒中下載資料
        
        Args:
            api_key (str): API金鑰
            db_path (str): 資料庫路徑
            selected_options (list): 選取的資料選項
        """
        try:
            # 初始化下載器
            self.downloader = StockDataDownloader(api_key=api_key, database_path=db_path)
            
            # 設置日誌重定向
            self.setup_log_redirect()
            
            # 當前進度
            current_option = 0
            total_options = len(selected_options)
            
            # 開始下載選定的資料
            for option in selected_options:
                current_option += 1
                progress_pct = (current_option - 1) / total_options * 100
                self.update_progress(progress_pct, f"正在下載{option}...")
                
                if option == "價格資料":
                    self.downloader.download_price_data()
                elif option == "財務資料":
                    self.downloader.download_financial_data()
                elif option == "技術指標":
                    self.downloader.download_technical_indicators()
                elif option == "三大法人":
                    self.downloader.download_institutional_investors_data()
                elif option == "融資融券":
                    self.downloader.download_margin_trading_data()
                elif option == "月營收":
                    self.downloader.download_monthly_revenue()
                elif option == "公司資訊":
                    self.downloader.download_company_info()
                    self.downloader.download_stock_categories()
                elif option == "經濟指標":
                    self.downloader.download_economic_data()
                    self.downloader.download_world_indices()
            
            # 完成下載
            self.update_progress(100, "下載完成")
            self.log_text.insert(tk.END, "=== 所有資料下載完成 ===\n")
            self.log_text.see(tk.END)
            
            # 關閉下載器
            self.downloader.close()
            
            # 顯示下載完成的訊息
            self.root.after(0, lambda: messagebox.showinfo("下載完成", "所有選定的資料已成功下載。"))
        
        except Exception as e:
            error_msg = f"下載資料時發生錯誤: {str(e)}"
            self.log_text.insert(tk.END, f"錯誤: {error_msg}\n")
            self.log_text.see(tk.END)
            
            # 關閉下載器
            if self.downloader:
                self.downloader.close()
            
            # 顯示錯誤訊息
            self.root.after(0, lambda: messagebox.showerror("錯誤", error_msg))
    
    def setup_log_redirect(self):
        """設置日誌重定向到UI"""
        import logging
        
        class TextHandler(logging.Handler):
            def __init__(self, text_widget):
                logging.Handler.__init__(self)
                self.text_widget = text_widget
            
            def emit(self, record):
                msg = self.format(record) + '\n'
                self.text_widget.after(0, self.append_log, msg)
            
            def append_log(self, msg):
                self.text_widget.insert(tk.END, msg)
                self.text_widget.see(tk.END)
        
        # 取得根日誌器
        logger = logging.getLogger()
        
        # 添加處理器
        text_handler = TextHandler(self.log_text)
        text_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(text_handler)
    
    def update_progress(self, progress_value, status_text):
        """更新進度條和狀態文字
        
        Args:
            progress_value (float): 進度值 (0-100)
            status_text (str): 狀態文字
        """
        self.root.after(0, lambda: self.progress_var.set(progress_value))
        # 更新主視窗的狀態文字，如果有status_var
        if hasattr(self.root, 'status_var'):
            self.root.after(0, lambda: self.root.status_var.set(status_text))
