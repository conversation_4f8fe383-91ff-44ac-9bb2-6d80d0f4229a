# news_tab.py
import tkinter as tk
from tkinter import messagebox, ttk


class NewsTab:
    def __init__(self, parent, root):
        """初始化新聞頁面

        Args:
            parent: 父容器
            root: 主視窗
        """
        self.parent = parent
        self.root = root
        self.db_path = "tw_stock_data.db"

        # 建立UI
        self.setup_news_tab()

    def setup_news_tab(self):
        """設置新聞頁面"""
        frame = ttk.LabelFrame(self.parent, text="股票新聞分析")
        frame.pack(expand=True, fill="both", padx=10, pady=10)

        # 控制區
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill="x", padx=10, pady=10)

        # 股票輸入
        stock_frame = ttk.Frame(control_frame)
        stock_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(stock_frame, text="股票代碼:").pack(side=tk.LEFT, padx=5)
        self.news_stock_id_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.news_stock_id_var, width=15).pack(
            side=tk.LEFT, padx=5
        )

        # 時間範圍
        days_frame = ttk.Frame(control_frame)
        days_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(days_frame, text="分析天數:").pack(side=tk.LEFT, padx=5)
        self.news_days_var = tk.IntVar(value=30)
        ttk.Combobox(
            days_frame,
            textvariable=self.news_days_var,
            values=[7, 14, 30, 90],
            width=10,
            state="readonly",
        ).pack(side=tk.LEFT, padx=5)

        # 分析按鈕
        ttk.Button(
            control_frame, text="分析新聞", command=self.analyze_news, style="Accent.TButton"
        ).pack(side=tk.LEFT, padx=5, pady=10, ipadx=10, ipady=5)

        # 新聞列表區域
        news_frame = ttk.LabelFrame(frame, text="新聞列表")
        news_frame.pack(expand=True, fill="both", padx=10, pady=10)

        # 新聞列表
        columns = ("日期", "標題", "來源", "情緒")
        self.news_tree = ttk.Treeview(news_frame, columns=columns, show="headings", height=10)

        # 設置列標題
        for col in columns:
            self.news_tree.heading(col, text=col)
            if col == "標題":
                self.news_tree.column(col, width=400, anchor="w")
            elif col == "來源":
                self.news_tree.column(col, width=100, anchor="center")
            else:
                self.news_tree.column(col, width=120, anchor="center")

        # 添加滾動條
        scrollbar_y = ttk.Scrollbar(news_frame, orient="vertical", command=self.news_tree.yview)
        scrollbar_x = ttk.Scrollbar(news_frame, orient="horizontal", command=self.news_tree.xview)
        self.news_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 排列元件
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        self.news_tree.pack(fill="both", expand=True)

        # 情緒摘要區域
        sentiment_frame = ttk.LabelFrame(frame, text="情緒分析摘要")
        sentiment_frame.pack(fill="x", padx=10, pady=10)

        self.sentiment_text = tk.Text(sentiment_frame, height=5, width=80)
        self.sentiment_text.pack(fill="both", expand=True, padx=5, pady=5)

        # 初始文字
        self.sentiment_text.insert(tk.END, "尚未進行分析")

    def analyze_news(self):
        """分析新聞"""
        # 獲取輸入
        stock_id = self.news_stock_id_var.get().strip()
        if not stock_id:
            messagebox.showerror("錯誤", "請輸入股票代碼")
            return

        days = self.news_days_var.get()

        # 更新狀態
        if hasattr(self.root, "status_var"):
            self.root.status_var.set(f"正在分析 {stock_id} 的新聞...")

        # 清空列表和摘要
        self.news_tree.delete(*self.news_tree.get_children())
        self.sentiment_text.delete(1.0, tk.END)
        self.sentiment_text.insert(tk.END, "分析中...")

        # 這裡應該實現真實的新聞分析功能
        # 由於沒有實際代碼，這裡僅添加示例數據
        import random
        from datetime import datetime, timedelta

        # 隨機生成一些示例新聞
        today = datetime.now()
        sentiment_types = ["正面", "中性", "負面"]

        # 生成模擬數據
        for i in range(10):
            date = (today - timedelta(days=i)).strftime("%Y-%m-%d")
            title = f"{stock_id} 相關新聞標題 {i+1}"
            source = random.choice(["經濟日報", "工商時報", "聯合報", "中央社"])
            sentiment = random.choice(sentiment_types)

            self.news_tree.insert("", "end", values=(date, title, source, sentiment))

        # 更新情緒摘要
        positive_count = sum(
            1
            for item in self.news_tree.get_children()
            if self.news_tree.item(item, "values")[3] == "正面"
        )
        neutral_count = sum(
            1
            for item in self.news_tree.get_children()
            if self.news_tree.item(item, "values")[3] == "中性"
        )
        negative_count = sum(
            1
            for item in self.news_tree.get_children()
            if self.news_tree.item(item, "values")[3] == "負面"
        )

        total = positive_count + neutral_count + negative_count
        positive_ratio = positive_count / total if total > 0 else 0
        neutral_ratio = neutral_count / total if total > 0 else 0
        negative_ratio = negative_count / total if total > 0 else 0

        sentiment_text = f"股票: {stock_id} 新聞情緒分析結果\n"
        sentiment_text += f"分析天數: {days} 天\n"
        sentiment_text += f"正面新聞: {positive_count} ({positive_ratio:.1%})\n"
        sentiment_text += f"中性新聞: {neutral_count} ({neutral_ratio:.1%})\n"
        sentiment_text += f"負面新聞: {negative_count} ({negative_ratio:.1%})\n"

        # 顯示摘要
        self.sentiment_text.delete(1.0, tk.END)
        self.sentiment_text.insert(tk.END, sentiment_text)

        # 更新狀態
        if hasattr(self.root, "status_var"):
            self.root.status_var.set(f"{stock_id} 新聞分析完成")
