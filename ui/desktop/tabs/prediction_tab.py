# prediction_tab.py
import os
import threading
import tkinter as tk
from tkinter import messagebox, ttk

from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

from core.stock_price_predictor import StockPricePredictor


class PredictionTab:
    def __init__(self, parent, root):
        """初始化預測頁面

        Args:
            parent: 父容器
            root: 主視窗
        """
        self.parent = parent
        self.root = root
        self.predictor = None
        self.model_dir = "models"
        self.db_path = "tw_stock_data.db"
        self.current_figure = None

        # 建立UI
        self.setup_predict_tab()

    def setup_predict_tab(self):
        """設置預測頁面"""
        frame = ttk.LabelFrame(self.parent, text="股票漲跌預測")
        frame.pack(expand=True, fill="both", padx=10, pady=10)

        # 左側控制區
        control_frame = ttk.Frame(frame)
        control_frame.pack(side=tk.LEFT, fill="y", padx=10, pady=10)

        # 股票選擇
        stock_frame = ttk.Frame(control_frame)
        stock_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(stock_frame, text="股票代碼:").pack(side=tk.LEFT, padx=5)
        self.predict_stock_id_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.predict_stock_id_var, width=15).pack(
            side=tk.LEFT, padx=5
        )

        # 預測天數
        pred_frame = ttk.Frame(control_frame)
        pred_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(pred_frame, text="預測未來幾天:").pack(side=tk.LEFT, padx=5)
        self.predict_days_var = tk.IntVar(value=1)
        ttk.Combobox(
            pred_frame,
            textvariable=self.predict_days_var,
            values=[1, 3, 5, 10],
            width=10,
            state="readonly",
        ).pack(side=tk.LEFT, padx=5)

        # 預測按鈕
        predict_btn = ttk.Button(
            control_frame, text="進行預測", command=self.predict_stock, style="Accent.TButton"
        )
        predict_btn.pack(fill="x", padx=5, pady=20, ipady=5)

        # 顯示圖表選項
        chart_label = ttk.Label(control_frame, text="顯示圖表:")
        chart_label.pack(anchor="w", padx=5, pady=5)

        self.chart_days_var = tk.IntVar(value=30)
        chart_options = [(f"過去 {days} 天", days) for days in [30, 60, 90, 180, 365]]

        for text, days in chart_options:
            ttk.Radiobutton(
                control_frame,
                text=text,
                variable=self.chart_days_var,
                value=days,
                command=self.update_chart,
            ).pack(anchor="w", padx=15, pady=2)

        # 預測結果
        result_frame = ttk.LabelFrame(control_frame, text="預測結果")
        result_frame.pack(fill="x", padx=5, pady=20)

        self.predict_result_var = tk.StringVar(value="尚未預測")
        result_label = ttk.Label(
            result_frame,
            textvariable=self.predict_result_var,
            font=("Arial", 12, "bold"),
            wraplength=250,
        )
        result_label.pack(padx=10, pady=10)

        # 右側圖表區
        chart_frame = ttk.LabelFrame(frame, text="股價走勢與預測")
        chart_frame.pack(side=tk.RIGHT, fill="both", expand=True, padx=10, pady=10)

        # 圖表容器
        self.chart_container = ttk.Frame(chart_frame)
        self.chart_container.pack(fill="both", expand=True, padx=5, pady=5)

    def predict_stock(self):
        """預測單一股票漲跌"""
        # 檢查輸入
        stock_id = self.predict_stock_id_var.get().strip()
        if not stock_id:
            messagebox.showerror("錯誤", "請輸入股票代碼")
            return

        prediction_days = self.predict_days_var.get()

        # 確保預測器已初始化
        if self.predictor is None:
            self.predictor = StockPricePredictor(
                database_path=self.db_path, model_dir=self.model_dir
            )

        # 檢查模型是否存在
        model_path = os.path.join(self.model_dir, f"{stock_id}_pred{prediction_days}d_model.pkl")
        if not os.path.exists(model_path):
            messagebox.showerror(
                "錯誤", f"找不到 {stock_id} 的預測模型。\n請先訓練模型，或確認模型路徑正確。"
            )
            return

        # 更新狀態
        if hasattr(self.root, "status_var"):
            self.root.status_var.set(f"正在預測 {stock_id} 的未來 {prediction_days} 天漲跌...")

        # 在背景執行緒中進行預測
        predict_thread = threading.Thread(
            target=self.predict_stock_thread, args=(stock_id, prediction_days)
        )
        predict_thread.daemon = True
        predict_thread.start()

    def predict_stock_thread(self, stock_id, prediction_days):
        """在背景執行緒中進行股票預測

        Args:
            stock_id (str): 股票代碼
            prediction_days (int): 預測天數
        """
        try:
            # 進行預測
            result = self.predictor.predict_price_movement(
                stock_id=stock_id, prediction_days=prediction_days
            )

            if result is None:
                self.update_predict_result("預測失敗，請檢查數據是否足夠。")
                if hasattr(self.root, "status_var"):
                    self.root.status_var.set("預測失敗")
                return

            # 更新預測結果
            prediction = result["prediction"]
            probability = result["probability"]
            current_price = result["current_price"]
            target_date = result["target_date"]

            result_text = f"股票: {stock_id}\n"
            result_text += f"現價: {current_price}\n"
            result_text += f"預測: {target_date} 將會"

            if prediction == "up":
                result_text += f" 上漲 (機率: {probability:.2%})"
            else:
                result_text += f" 下跌 (機率: {probability:.2%})"

            self.update_predict_result(result_text)

            # 更新狀態
            if hasattr(self.root, "status_var"):
                self.root.status_var.set(f"{stock_id} 預測完成")

            # 繪製圖表
            self.plot_prediction_chart(stock_id, prediction_days)

        except Exception as e:
            error_msg = f"預測時發生錯誤: {str(e)}"
            self.update_predict_result(f"錯誤: {error_msg}")
            if hasattr(self.root, "status_var"):
                self.root.status_var.set(error_msg)

            # 顯示錯誤訊息
            self.root.after(0, lambda: messagebox.showerror("錯誤", error_msg))

    def update_predict_result(self, text):
        """更新預測結果文字

        Args:
            text (str): 要顯示的文字
        """
        self.root.after(0, lambda: self.predict_result_var.set(text))

    def plot_prediction_chart(self, stock_id, prediction_days):
        """繪製預測圖表

        Args:
            stock_id (str): 股票代碼
            prediction_days (int): 預測天數
        """
        try:
            # 使用模型繪製圖表
            chart_days = self.chart_days_var.get()
            fig = self.predictor.plot_predictions(
                stock_id, days=chart_days, prediction_days=prediction_days
            )

            if fig is None:
                return

            # 更新UI上的圖表
            self.update_chart_ui(fig)

        except Exception as e:
            print(f"繪製圖表時發生錯誤: {str(e)}")

    def update_chart_ui(self, figure):
        """更新UI上的圖表

        Args:
            figure: Matplotlib圖表
        """
        # 清除舊圖表
        for widget in self.chart_container.winfo_children():
            widget.destroy()

        # 保存目前的圖表
        self.current_figure = figure

        # 建立新的圖表畫布
        canvas = FigureCanvasTkAgg(figure, self.chart_container)
        canvas.draw()
        canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

    def update_chart(self):
        """更新圖表顯示"""
        # 檢查是否有進行過預測
        stock_id = self.predict_stock_id_var.get().strip()
        prediction_days = self.predict_days_var.get()

        if stock_id:
            self.plot_prediction_chart(stock_id, prediction_days)
