# stock_filter_tab.py
import csv
import datetime
import os
import sqlite3
import threading
import tkinter as tk
from tkinter import filedialog, messagebox, ttk

import matplotlib.dates as mdates
import matplotlib.pyplot as plt
import pandas as pd
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk


class StockFilterTab:
    def __init__(self, parent, root):
        """初始化股票篩選頁面

        Args:
            parent: 父容器
            root: 主視窗
        """
        self.parent = parent
        self.root = root
        self.filter_finder = None
        self.filter_results = None
        self.current_filter_figure = None
        self.db_path = "tw_stock_data.db"

        # 建立UI
        self.setup_stock_filter_tab()

    def setup_stock_filter_tab(self):
        """設置股票篩選頁面"""
        frame = ttk.LabelFrame(self.parent, text="股票條件篩選")
        frame.pack(expand=True, fill="both", padx=10, pady=10)

        # 左側控制區
        control_frame = ttk.Frame(frame)
        control_frame.pack(side=tk.LEFT, fill="y", padx=10, pady=10)

        # 篩選模式選擇
        mode_frame = ttk.Frame(control_frame)
        mode_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(mode_frame, text="篩選模式:").pack(side=tk.LEFT, padx=5)
        self.filter_mode_var = tk.StringVar(value="均線交叉")
        mode_combo = ttk.Combobox(
            mode_frame,
            textvariable=self.filter_mode_var,
            values=["均線交叉", "價格突破", "全部條件"],
            width=15,
            state="readonly",
        )
        mode_combo.pack(side=tk.LEFT, padx=5)
        mode_combo.bind("<<ComboboxSelected>>", self.update_filter_options)

        # 日期範圍
        date_frame = ttk.Frame(control_frame)
        date_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(date_frame, text="起始日期:").pack(side=tk.LEFT, padx=5)
        self.filter_start_date_var = tk.StringVar(
            value=(datetime.datetime.now() - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
        )
        ttk.Entry(date_frame, textvariable=self.filter_start_date_var, width=15).pack(
            side=tk.LEFT, padx=5
        )

        ttk.Label(date_frame, text="結束日期:").pack(side=tk.LEFT, padx=20)
        self.filter_end_date_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(date_frame, textvariable=self.filter_end_date_var, width=15).pack(
            side=tk.LEFT, padx=5
        )

        # 均線交叉選項 (初始顯示)
        self.crossover_frame = ttk.LabelFrame(control_frame, text="均線交叉設置")
        self.crossover_frame.pack(fill="x", padx=5, pady=10)

        ma_frame = ttk.Frame(self.crossover_frame)
        ma_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(ma_frame, text="短期均線:").pack(side=tk.LEFT, padx=5)
        self.ma_short_var = tk.IntVar(value=5)
        ttk.Combobox(
            ma_frame,
            textvariable=self.ma_short_var,
            values=[3, 5, 8, 10, 15, 20],
            width=10,
            state="readonly",
        ).pack(side=tk.LEFT, padx=5)

        ttk.Label(ma_frame, text="長期均線:").pack(side=tk.LEFT, padx=15)
        self.ma_long_var = tk.IntVar(value=10)
        ttk.Combobox(
            ma_frame,
            textvariable=self.ma_long_var,
            values=[10, 15, 20, 30, 60, 120],
            width=10,
            state="readonly",
        ).pack(side=tk.LEFT, padx=5)

        month_ma_frame = ttk.Frame(self.crossover_frame)
        month_ma_frame.pack(fill="x", padx=5, pady=5)

        self.use_month_ma_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            month_ma_frame, text="使用月線作為第三條均線", variable=self.use_month_ma_var
        ).pack(side=tk.LEFT, padx=5)

        # 突破設置
        self.breakout_frame = ttk.LabelFrame(control_frame, text="價格突破設置")
        self.breakout_frame.pack(fill="x", padx=5, pady=10)
        self.breakout_frame.pack_forget()  # 初始隱藏

        ttk.Label(self.breakout_frame, text="突破天數:").pack(side=tk.LEFT, padx=5)
        self.breakout_days_var = tk.IntVar(value=60)
        ttk.Combobox(
            self.breakout_frame,
            textvariable=self.breakout_days_var,
            values=[20, 30, 60, 90, 120, 180, 240],
            width=10,
            state="readonly",
        ).pack(side=tk.LEFT, padx=5)

        # 通用選項
        common_frame = ttk.LabelFrame(control_frame, text="通用設置")
        common_frame.pack(fill="x", padx=5, pady=10)

        vol_frame = ttk.Frame(common_frame)
        vol_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(vol_frame, text="成交量放大倍數:").pack(side=tk.LEFT, padx=5)
        self.vol_ratio_var = tk.DoubleVar(value=1.5)
        ttk.Combobox(
            vol_frame, textvariable=self.vol_ratio_var, values=[1.0, 1.5, 2.0, 2.5, 3.0], width=10
        ).pack(side=tk.LEFT, padx=5)

        recent_frame = ttk.Frame(common_frame)
        recent_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(recent_frame, text="僅顯示最近天數:").pack(side=tk.LEFT, padx=5)
        self.recent_days_var = tk.IntVar(value=7)
        ttk.Combobox(
            recent_frame,
            textvariable=self.recent_days_var,
            values=[3, 5, 7, 10, 15, 30, 0],
            width=10,
            state="readonly",
        ).pack(side=tk.LEFT, padx=5)
        ttk.Label(recent_frame, text="(0表示顯示全部)").pack(side=tk.LEFT, padx=5)

        # 篩選按鈕
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill="x", padx=5, pady=15)

        ttk.Button(
            button_frame, text="執行篩選", command=self.run_stock_filter, style="Accent.TButton"
        ).pack(side=tk.LEFT, padx=5, pady=5, ipadx=10, ipady=5)

        ttk.Button(button_frame, text="導出結果", command=self.export_filter_results).pack(
            side=tk.LEFT, padx=5, pady=5, ipadx=10, ipady=5
        )

        # 圖表顯示區
        self.filter_chart_frame = ttk.LabelFrame(frame, text="篩選結果與圖表")
        self.filter_chart_frame.pack(side=tk.RIGHT, fill="both", expand=True, padx=10, pady=10)

        # 分割區域：上方為結果列表，下方為股價圖表
        splitter = ttk.PanedWindow(self.filter_chart_frame, orient=tk.VERTICAL)
        splitter.pack(fill="both", expand=True)

        # 上方：結果區域
        result_frame = ttk.Frame(splitter)
        splitter.add(result_frame, weight=1)

        # 結果表格
        columns = (
            "股票代碼",
            "公司名稱",
            "起始日期",
            "結束日期/突破日",
            "持續天數/突破幅度",
            "成交量放大",
            "最新價格",
        )
        self.filter_result_tree = ttk.Treeview(
            result_frame, columns=columns, show="headings", height=10
        )

        # 設置列標題
        for col in columns:
            self.filter_result_tree.heading(col, text=col)
            if col in ["股票代碼", "成交量放大"]:
                self.filter_result_tree.column(col, width=80, anchor="center")
            elif col in ["公司名稱"]:
                self.filter_result_tree.column(col, width=120, anchor="w")
            else:
                self.filter_result_tree.column(col, width=100, anchor="center")

        # 設置選中事件
        self.filter_result_tree.bind("<<TreeviewSelect>>", self.on_filter_result_selected)

        # 添加滾動條
        scrollbar_y = ttk.Scrollbar(
            result_frame, orient="vertical", command=self.filter_result_tree.yview
        )
        scrollbar_x = ttk.Scrollbar(
            result_frame, orient="horizontal", command=self.filter_result_tree.xview
        )
        self.filter_result_tree.configure(
            yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set
        )

        # 排列元件
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        self.filter_result_tree.pack(fill="both", expand=True)

        # 下方：圖表區域
        chart_frame = ttk.Frame(splitter)
        splitter.add(chart_frame, weight=2)

        self.filter_chart_container = ttk.Frame(chart_frame)
        self.filter_chart_container.pack(fill="both", expand=True)

        # 分配初始位置
        splitter.sashpos(0, 200)

    def update_filter_options(self, event=None):
        """根據篩選模式更新顯示的選項"""
        mode = self.filter_mode_var.get()

        if mode == "均線交叉":
            self.crossover_frame.pack(fill="x", padx=5, pady=10)
            self.breakout_frame.pack_forget()
        elif mode == "價格突破":
            self.crossover_frame.pack_forget()
            self.breakout_frame.pack(fill="x", padx=5, pady=10)
        else:  # 全部條件
            self.crossover_frame.pack(fill="x", padx=5, pady=10)
            self.breakout_frame.pack(fill="x", padx=5, pady=10)

    def run_stock_filter(self):
        """執行股票篩選"""
        # 清空之前的結果
        for item in self.filter_result_tree.get_children():
            self.filter_result_tree.delete(item)

        # 清空圖表
        for widget in self.filter_chart_container.winfo_children():
            widget.destroy()

        # 獲取篩選參數
        start_date = self.filter_start_date_var.get().strip()
        end_date = self.filter_end_date_var.get().strip()
        mode = self.filter_mode_var.get()
        recent_days = self.recent_days_var.get()

        # 更新狀態
        if hasattr(self.root, "status_var"):
            self.root.status_var.set("正在執行股票篩選...")

        # 在背景執行緒中進行篩選
        threading.Thread(
            target=self.run_stock_filter_thread, args=(mode, start_date, end_date, recent_days)
        ).start()

    def run_stock_filter_thread(self, mode, start_date, end_date, recent_days):
        """在背景執行緒中執行股票篩選"""
        try:
            # 引入股票篩選器
            from stock_condition_finder import StockConditionFinder

            # 初始化篩選器
            self.filter_finder = StockConditionFinder(self.db_path)

            # 載入公司資訊
            company_info = self.filter_finder.load_company_info()

            # 根據篩選模式執行相應的篩選
            if mode == "均線交叉" or mode == "全部條件":
                ma_short = self.ma_short_var.get()
                ma_long = self.ma_long_var.get()
                use_month_ma = self.use_month_ma_var.get()
                vol_ratio = self.vol_ratio_var.get()

                # 更新狀態
                if hasattr(self.root, "status_var"):
                    self.root.after(
                        0, lambda: self.root.status_var.set(f"正在尋找均線交叉的股票...")
                    )

                # 執行篩選
                single_day, multi_day = self.filter_finder.find_ma_crossover(
                    start_date,
                    end_date,
                    ma_short,
                    ma_long,
                    use_month_ma=use_month_ma,
                    vol_surge_ratio=vol_ratio,
                )

                # 獲取最近交叉
                if recent_days > 0:
                    recent_crossovers = self.filter_finder.get_recent_crossovers(
                        single_day, multi_day, recent_days
                    )
                    self.add_crossover_results_to_tree(recent_crossovers, company_info)
                else:
                    # 添加所有結果
                    all_crossovers = []
                    for stock, intervals in {**single_day, **multi_day}.items():
                        for interval in intervals:
                            all_crossovers.append((stock, *interval))

                    # 按開始日期排序
                    all_crossovers.sort(key=lambda x: x[1], reverse=True)
                    self.add_crossover_results_to_tree(all_crossovers, company_info)

            if mode == "價格突破" or mode == "全部條件":
                breakout_days = self.breakout_days_var.get()
                vol_ratio = self.vol_ratio_var.get()

                # 更新狀態
                if hasattr(self.root, "status_var"):
                    self.root.after(
                        0,
                        lambda: self.root.status_var.set(
                            f"正在尋找突破{breakout_days}日高點的股票..."
                        ),
                    )

                # 執行篩選
                breakout_results = self.filter_finder.find_breakout_stocks(
                    start_date, end_date, breakout_days, vol_surge_ratio=vol_ratio
                )

                # 添加結果到表格
                self.add_breakout_results_to_tree(breakout_results, company_info, recent_days)

            # 關閉數據庫連接
            self.filter_finder.close()
            self.filter_finder = None

            # 更新狀態
            if hasattr(self.root, "status_var"):
                self.root.after(0, lambda: self.root.status_var.set("篩選完成"))

        except Exception as e:
            error_msg = f"執行篩選時發生錯誤: {str(e)}"
            if hasattr(self.root, "status_var"):
                self.root.after(0, lambda: self.root.status_var.set(error_msg))
            self.root.after(0, lambda: messagebox.showerror("錯誤", error_msg))
            import traceback

            traceback.print_exc()

    def add_crossover_results_to_tree(self, crossovers, company_info):
        """將均線交叉結果添加到表格"""
        # 打開新連接加載最新收盤價
        conn = sqlite3.connect(self.db_path)
        try:
            close_df = pd.read_sql(
                "SELECT date, stock_id, close FROM price_close ORDER BY date DESC", conn
            )
            close_df = close_df.pivot(index="date", columns="stock_id", values="close")
        except Exception as e:
            print(f"加載收盤價數據錯誤: {e}")
            close_df = pd.DataFrame()
        finally:
            conn.close()

        for item in crossovers:
            if len(item) == 5:
                stock, start, end, length, vol_surge = item
            else:
                stock, start, end, length, vol_surge = item[0], item[1], item[2], item[3], item[4]

            # 獲取公司名稱
            company_name = ""
            if company_info is not None and not company_info.empty:
                company_row = company_info[company_info["stock_id"] == stock]
                if not company_row.empty and "公司簡稱" in company_row.columns:
                    company_name = company_row["公司簡稱"].iloc[0]

            # 獲取最新價格
            latest_price = "-"
            if not close_df.empty and stock in close_df.columns:
                latest_prices = close_df[stock].dropna()
                if len(latest_prices) > 0:
                    latest_price = str(latest_prices.iloc[0])

            # 添加到表格
            self.filter_result_tree.insert(
                "",
                "end",
                values=(
                    stock,
                    company_name,
                    start.strftime("%Y-%m-%d"),
                    end.strftime("%Y-%m-%d"),
                    f"{length}天",
                    "是" if vol_surge else "否",
                    latest_price,
                ),
            )

    def add_breakout_results_to_tree(self, breakout_results, company_info, recent_days):
        """將突破結果添加到表格"""
        # 打開新連接加載最新收盤價
        conn = sqlite3.connect(self.db_path)
        try:
            close_df = pd.read_sql(
                "SELECT date, stock_id, close FROM price_close ORDER BY date DESC", conn
            )
            close_df = close_df.pivot(index="date", columns="stock_id", values="close")
        except Exception as e:
            print(f"加載收盤價數據錯誤: {e}")
            close_df = pd.DataFrame()
        finally:
            conn.close()

        # 整理所有股票的最新突破日
        latest_breakouts = []

        for stock, breakouts in breakout_results.items():
            # 按日期排序
            sorted_breakouts = sorted(breakouts, key=lambda x: x[0], reverse=True)

            # 獲取最新突破
            latest_breakout = sorted_breakouts[0]
            date, close_price, previous_high, breakout_pct, vol_surge = latest_breakout

            # 如果有日期限制，且突破日不在範圍內，則跳過
            if recent_days > 0:
                recent_threshold = pd.Timestamp.now().floor("D") - pd.Timedelta(days=recent_days)
                if date < recent_threshold:
                    continue

            # 獲取公司名稱
            company_name = ""
            if company_info is not None and not company_info.empty:
                company_row = company_info[company_info["stock_id"] == stock]
                if not company_row.empty and "公司簡稱" in company_row.columns:
                    company_name = company_row["公司簡稱"].iloc[0]

            # 獲取最新收盤價
            latest_price = "-"
            if not close_df.empty and stock in close_df.columns:
                latest_prices = close_df[stock].dropna()
                if len(latest_prices) > 0:
                    latest_price = str(latest_prices.iloc[0])

            # 將信息加入列表
            latest_breakouts.append(
                (
                    stock,
                    company_name,
                    date,
                    close_price,
                    previous_high,
                    breakout_pct,
                    vol_surge,
                    latest_price,
                )
            )

        # 按日期排序
        latest_breakouts.sort(key=lambda x: x[2], reverse=True)

        # 添加到表格
        for item in latest_breakouts:
            (
                stock,
                company_name,
                date,
                close_price,
                previous_high,
                breakout_pct,
                vol_surge,
                latest_price,
            ) = item

            # 添加到表格
            self.filter_result_tree.insert(
                "",
                "end",
                values=(
                    stock,
                    company_name,
                    date.strftime("%Y-%m-%d"),
                    "-",
                    f"{breakout_pct:.2f}%" if breakout_pct is not None else "-",
                    "是" if vol_surge else "否",
                    latest_price,
                ),
            )

    def on_filter_result_selected(self, event):
        """當選中篩選結果時顯示該股票的圖表"""
        selected_items = self.filter_result_tree.selection()
        if not selected_items:
            return

        # 獲取選中的項目
        item = selected_items[0]
        values = self.filter_result_tree.item(item, "values")
        stock_id = values[0]

        # 顯示該股票的圖表
        self.plot_stock_chart(stock_id)

    def plot_stock_chart(self, stock_id):
        """繪製股票圖表"""
        try:
            # 更新狀態
            if hasattr(self.root, "status_var"):
                self.root.status_var.set(f"正在載入 {stock_id} 的圖表...")

            # 清空圖表
            for widget in self.filter_chart_container.winfo_children():
                widget.destroy()

            # 獲取股票資料 - 使用新的 SQLite 連接
            end_date = datetime.datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.datetime.now() - datetime.timedelta(days=365)).strftime(
                "%Y-%m-%d"
            )

            # 創建新的數據庫連接，確保在當前線程中
            conn = sqlite3.connect(self.db_path)

            try:
                # 載入價格資料
                close_data = pd.read_sql(
                    f"SELECT date, stock_id, close FROM price_close WHERE stock_id='{stock_id}' AND date BETWEEN '{start_date}' AND '{end_date}' ORDER BY date",
                    conn,
                )
                volume_data = pd.read_sql(
                    f"SELECT date, stock_id, volume FROM price_volume WHERE stock_id='{stock_id}' AND date BETWEEN '{start_date}' AND '{end_date}' ORDER BY date",
                    conn,
                )
                high_data = pd.read_sql(
                    f"SELECT date, stock_id, high FROM price_high WHERE stock_id='{stock_id}' AND date BETWEEN '{start_date}' AND '{end_date}' ORDER BY date",
                    conn,
                )
                low_data = pd.read_sql(
                    f"SELECT date, stock_id, low FROM price_low WHERE stock_id='{stock_id}' AND date BETWEEN '{start_date}' AND '{end_date}' ORDER BY date",
                    conn,
                )

                # 轉換為時間序列格式
                close_data["date"] = pd.to_datetime(close_data["date"])
                close_data.set_index("date", inplace=True)
                volume_data["date"] = pd.to_datetime(volume_data["date"])
                volume_data.set_index("date", inplace=True)

                # 提取數據列
                stock_close = close_data["close"]
                stock_volume = volume_data["volume"] if not volume_data.empty else pd.Series()

                if len(stock_close) == 0:
                    if hasattr(self.root, "status_var"):
                        self.root.status_var.set(f"找不到 {stock_id} 的價格資料")
                    return

                # 計算移動平均線
                ma5 = stock_close.rolling(5).mean()
                ma10 = stock_close.rolling(10).mean()
                ma20 = stock_close.rolling(20).mean()
                ma60 = stock_close.rolling(60).mean()

                # 創建圖表
                fig, (ax1, ax2) = plt.subplots(
                    2, 1, figsize=(10, 8), gridspec_kw={"height_ratios": [3, 1]}, sharex=True
                )

                # 繪製股價走勢
                ax1.plot(stock_close.index, stock_close, "b-", linewidth=1.5, label="收盤價")
                ax1.plot(ma5.index, ma5, "r--", linewidth=1, label="5日均線")
                ax1.plot(ma10.index, ma10, "g--", linewidth=1, label="10日均線")
                ax1.plot(ma20.index, ma20, "c--", linewidth=1, label="20日均線")
                ax1.plot(ma60.index, ma60, "m--", linewidth=1, label="60日均線")

                # 設置標題、軸標籤等
                ax1.set_title(f"{stock_id} 股價走勢", fontsize=15)
                ax1.set_ylabel("價格")
                ax1.grid(True)
                ax1.legend(loc="best")

                # 設置日期格式
                ax1.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
                ax1.xaxis.set_major_locator(mdates.MonthLocator())

                # 繪製成交量
                if len(stock_volume) > 0:
                    ax2.bar(stock_volume.index, stock_volume, color="blue", alpha=0.6)
                    ax2.set_ylabel("成交量")
                    ax2.grid(True)

                # 調整布局
                plt.tight_layout()
                plt.xticks(rotation=45)

                # 在UI中顯示
                self.current_filter_figure = fig
                canvas = FigureCanvasTkAgg(fig, self.filter_chart_container)
                canvas.draw()
                canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

                # 添加工具欄
                toolbar_frame = tk.Frame(self.filter_chart_container)
                toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X)
                toolbar = NavigationToolbar2Tk(canvas, toolbar_frame)
                toolbar.update()

                # 更新狀態
                if hasattr(self.root, "status_var"):
                    self.root.status_var.set(f"已顯示 {stock_id} 的圖表")

            finally:
                # 確保連接關閉
                conn.close()

        except Exception as e:
            error_msg = f"繪製 {stock_id} 圖表時發生錯誤: {str(e)}"
            if hasattr(self.root, "status_var"):
                self.root.status_var.set(error_msg)
            messagebox.showerror("錯誤", error_msg)
            import traceback

            traceback.print_exc()

    def export_filter_results(self):
        """匯出篩選結果到CSV檔案"""
        # 檢查是否有結果
        if not self.filter_result_tree.get_children():
            messagebox.showinfo("提示", "沒有可匯出的結果")
            return

        # 選擇保存位置
        file_path = filedialog.asksaveasfilename(
            title="匯出篩選結果",
            defaultextension=".csv",
            filetypes=(("CSV檔案", "*.csv"), ("所有檔案", "*.*")),
            initialfile=f"股票篩選結果_{datetime.datetime.now().strftime('%Y%m%d')}.csv",
        )

        if not file_path:
            return

        try:
            # 準備匯出資料
            export_data = []
            columns = self.filter_result_tree.cget("columns")

            # 添加表頭
            export_data.append(columns)

            # 添加每一行資料
            for item in self.filter_result_tree.get_children():
                values = self.filter_result_tree.item(item, "values")
                export_data.append(values)

            # 寫入CSV
            with open(file_path, "w", newline="", encoding="utf-8-sig") as f:
                writer = csv.writer(f)
                writer.writerows(export_data)

            messagebox.showinfo("匯出成功", f"篩選結果已匯出至:\n{file_path}")

        except Exception as e:
            messagebox.showerror("錯誤", f"匯出結果時發生錯誤:\n{str(e)}")
