# training_tab.py
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import datetime
from core.stock_price_predictor import StockPricePredictor

class TrainingTab:
    def __init__(self, parent, root):
        """初始化模型訓練頁面
        
        Args:
            parent: 父容器
            root: 主視窗
        """
        self.parent = parent
        self.root = root
        self.predictor = None
        self.model_dir = "models"
        self.db_path = "tw_stock_data.db"
        
        # 建立UI
        self.setup_train_tab()
    
    def setup_train_tab(self):
        """設置模型訓練頁面"""
        frame = ttk.LabelFrame(self.parent, text="股票漲跌預測模型訓練")
        frame.pack(expand=True, fill="both", padx=10, pady=10)
        
        # 股票選擇
        stock_frame = ttk.Frame(frame)
        stock_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(stock_frame, text="股票代碼:").pack(side=tk.LEFT, padx=5)
        self.train_stock_id_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.train_stock_id_var, width=15).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(stock_frame, text="(例如: 2330)").pack(side=tk.LEFT)
        
        # 日期範圍
        date_frame = ttk.Frame(frame)
        date_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(date_frame, text="起始日期:").pack(side=tk.LEFT, padx=5)
        self.train_start_date_var = tk.StringVar(value=(datetime.datetime.now() - datetime.timedelta(days=365*3)).strftime("%Y-%m-%d"))
        ttk.Entry(date_frame, textvariable=self.train_start_date_var, width=15).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(date_frame, text="結束日期:").pack(side=tk.LEFT, padx=20)
        self.train_end_date_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(date_frame, textvariable=self.train_end_date_var, width=15).pack(side=tk.LEFT, padx=5)
        
        # 預測天數
        pred_frame = ttk.Frame(frame)
        pred_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(pred_frame, text="預測未來幾天:").pack(side=tk.LEFT, padx=5)
        self.prediction_days_var = tk.IntVar(value=1)
        ttk.Combobox(
            pred_frame, 
            textvariable=self.prediction_days_var, 
            values=[1, 3, 5, 10], 
            width=10, 
            state="readonly"
        ).pack(side=tk.LEFT, padx=5)
        
        # 模型目錄
        model_frame = ttk.Frame(frame)
        model_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(model_frame, text="模型保存目錄:").pack(side=tk.LEFT, padx=5)
        self.model_dir_var = tk.StringVar(value=self.model_dir)
        ttk.Entry(model_frame, textvariable=self.model_dir_var, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(model_frame, text="選擇目錄", command=self.select_model_dir).pack(side=tk.LEFT, padx=5)
        
        # 訓練按鈕
        train_btn_frame = ttk.Frame(frame)
        train_btn_frame.pack(fill="x", padx=10, pady=20)
        
        ttk.Button(
            train_btn_frame, 
            text="開始訓練模型", 
            command=self.start_train_model,
            style="Accent.TButton"
        ).pack(side=tk.LEFT, padx=5, pady=10, ipadx=10, ipady=5)
        
        # 結果顯示
        result_frame = ttk.LabelFrame(frame, text="訓練結果")
        result_frame.pack(expand=True, fill="both", padx=10, pady=10)
        
        self.train_result_text = tk.Text(result_frame, height=15, width=80)
        self.train_result_text.pack(side=tk.LEFT, fill="both", expand=True, padx=5, pady=5)
        
        scrollbar = ttk.Scrollbar(result_frame, command=self.train_result_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.train_result_text.config(yscrollcommand=scrollbar.set)
    
    def select_model_dir(self):
        """選擇模型儲存目錄"""
        dir_path = filedialog.askdirectory(
            title="選擇模型儲存目錄",
            initialdir=self.model_dir
        )
        if dir_path:
            self.model_dir = dir_path
            self.model_dir_var.set(dir_path)
    
    def start_train_model(self):
        """開始訓練模型"""
        # 檢查輸入
        stock_id = self.train_stock_id_var.get().strip()
        if not stock_id:
            messagebox.showerror("錯誤", "請輸入股票代碼")
            return
        
        start_date = self.train_start_date_var.get().strip()
        end_date = self.train_end_date_var.get().strip()
        prediction_days = self.prediction_days_var.get()
        model_dir = self.model_dir_var.get().strip()
        
        # 確保目錄存在
        if not os.path.exists(model_dir):
            try:
                os.makedirs(model_dir)
            except Exception as e:
                messagebox.showerror("錯誤", f"創建模型目錄時發生錯誤:\n{str(e)}")
                return
        
        # 確認是否繼續
        confirm = messagebox.askyesno(
            "確認訓練", 
            f"即將為股票 {stock_id} 訓練預測未來 {prediction_days} 天漲跌的模型。\n\n"
            f"訓練資料期間: {start_date} 至 {end_date}\n"
            f"模型將儲存到: {model_dir}\n\n"
            "此過程可能需要幾分鐘。要繼續嗎？"
        )
        
        if not confirm:
            return
        
        # 清空結果顯示
        self.train_result_text.delete(1.0, tk.END)
        self.train_result_text.insert(tk.END, f"開始訓練 {stock_id} 的預測模型...\n")
        
        # 更新狀態
        if hasattr(self.root, 'status_var'):
            self.root.status_var.set(f"正在訓練 {stock_id} 的預測模型...")
        
        # 開始訓練執行緒
        train_thread = threading.Thread(
            target=self.train_model_thread,
            args=(stock_id, start_date, end_date, prediction_days, model_dir)
        )
        train_thread.daemon = True
        train_thread.start()
    
    def train_model_thread(self, stock_id, start_date, end_date, prediction_days, model_dir):
        """在背景執行緒中訓練模型
        
        Args:
            stock_id (str): 股票代碼
            start_date (str): 起始日期
            end_date (str): 結束日期
            prediction_days (int): 預測天數
            model_dir (str): 模型保存目錄
        """
        try:
            # 初始化預測器
            if self.predictor is None:
                self.predictor = StockPricePredictor(database_path=self.db_path, model_dir=model_dir)
            
            # 訓練模型
            result = self.predictor.train_model(
                stock_id=stock_id,
                start_date=start_date,
                end_date=end_date,
                prediction_days=prediction_days
            )
            
            if result is None:
                self.update_train_result(f"訓練 {stock_id} 模型失敗，請檢查資料是否足夠。")
                if hasattr(self.root, 'status_var'):
                    self.root.status_var.set(f"訓練 {stock_id} 模型失敗")
                return
            
            # 顯示訓練結果
            accuracy = result['accuracy']
            best_params = result['best_params']
            cls_report = result['classification_report']
            model_path = result['model_path']
            
            result_text = f"股票 {stock_id} 模型訓練完成！\n\n"
            result_text += f"測試集準確率: {accuracy:.4f}\n\n"
            result_text += f"最佳參數:\n{str(best_params)}\n\n"
            result_text += f"分類報告:\n{cls_report}\n\n"
            result_text += f"模型已保存到: {model_path}\n"
            
            if result['feature_importances'] is not None:
                result_text += "\n最重要的特徵 (前10):\n"
                for i, row in result['feature_importances'].head(10).iterrows():
                    result_text += f"{row['feature']}: {row['importance']:.4f}\n"
            
            self.update_train_result(result_text)
            if hasattr(self.root, 'status_var'):
                self.root.status_var.set(f"{stock_id} 模型訓練完成，準確率: {accuracy:.4f}")
            
            # 顯示訓練完成訊息
            self.root.after(0, lambda: messagebox.showinfo("訓練完成", f"{stock_id} 模型訓練完成，準確率: {accuracy:.4f}"))
        
        except Exception as e:
            error_msg = f"訓練模型時發生錯誤: {str(e)}"
            self.update_train_result(f"錯誤: {error_msg}")
            if hasattr(self.root, 'status_var'):
                self.root.status_var.set(error_msg)
            
            # 顯示錯誤訊息
            self.root.after(0, lambda: messagebox.showerror("錯誤", error_msg))
    
    def update_train_result(self, text):
        """更新訓練結果文字
        
        Args:
            text (str): 要顯示的文字
        """
        self.root.after(0, lambda: self.train_result_text.delete(1.0, tk.END))
        self.root.after(0, lambda: self.train_result_text.insert(tk.END, text))
