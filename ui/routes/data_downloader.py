# ui/routes/data_downloader.py
import json
import logging
import os
import queue
import sqlite3
import threading
import time
import traceback
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
import requests

# 添加這一行導入語句 - 這是解決問題的關鍵
from core.data_downloader import CoreDataDownloader

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("data_download.log"), logging.StreamHandler()],
)

logger = logging.getLogger("DataDownloader")


class DownloadStatus:
    """下載狀態追蹤類"""

    def __init__(self):
        """初始化下載狀態"""
        self.is_downloading = False
        self.progress = 0
        self.current_task = ""
        self.logs = []
        self.completed = False
        self.success = False
        self._lock = threading.Lock()
        self._log_queue = queue.Queue()  # 用於多線程安全的日誌處理
        self._start_time = None
        self._end_time = None

    def start(self):
        """開始下載任務"""
        with self._lock:
            self.is_downloading = True
            self.progress = 0
            self.logs = []
            self.completed = False
            self.success = False
            self._start_time = datetime.now()

    def complete(self, success=True):
        """完成下載任務"""
        with self._lock:
            self.is_downloading = False
            self.progress = 100
            self.completed = True
            self.success = success
            self._end_time = datetime.now()

    def set_progress(self, progress: float, task: str):
        """設置當前進度和任務"""
        with self._lock:
            self.progress = min(100, max(0, progress))  # 確保在0-100範圍內
            self.current_task = task

    def add_log(self, level: str, message: str):
        """添加日誌"""
        log = {"timestamp": datetime.now().isoformat(), "level": level, "message": message}
        self._log_queue.put(log)

    def process_logs(self):
        """處理日誌隊列，將日誌添加到日誌列表"""
        with self._lock:
            while not self._log_queue.empty():
                log = self._log_queue.get()
                self.logs.append(log)
                level_method = getattr(logger, log["level"], logger.info)
                level_method(log["message"])

    def get_status(self) -> Dict[str, Any]:
        """獲取當前狀態"""
        self.process_logs()
        with self._lock:
            return {
                "status": "running" if self.is_downloading else "idle",
                "progress": self.progress,
                "current_task": self.current_task,
                "logs": self.logs,
                "completed": self.completed,
                "success": self.success,
                "start_time": self._start_time.isoformat() if self._start_time else None,
                "end_time": self._end_time.isoformat() if self._end_time else None,
                "elapsed_time": (
                    str(self._end_time - self._start_time)
                    if self._end_time and self._start_time
                    else None
                ),
            }


class DataDownloader:
    """網頁版資料下載器"""

    def __init__(self, database_path=None, api_key=None):
        """初始化下載器

        Args:
            database_path (str): 資料庫路徑
            api_key (str, optional): FinLab API金鑰
        """
        # 使用傳入的資料庫路徑
        self.database_path = database_path
        self.api_key = api_key
        self.status = DownloadStatus()
        self.core_downloader = None
        self.download_options = {
            "price": True,
            "technical": True,
            "company": True,
            "financial": False,
            "institutional": False,
            "margin": False,
            "revenue": False,
            "economic": False,
            "news": False,
        }
        self.news_days = 7
        self._download_thread = None
        # 使用與模塊頂部相同的 logger
        self.logger = logging.getLogger("DataDownloader")

        # 記錄使用的資料庫路徑
        self.logger.info(f"初始化下載器，使用資料庫路徑: {self.database_path}")

    def connect_database(self):
        """連接或創建資料庫"""
        try:
            if self.core_downloader is None:
                # 加入更多日誌
                self.logger.info(f"正在初始化 CoreDataDownloader，資料庫路徑: {self.database_path}")
                # 檢查資料庫目錄是否存在
                db_dir = os.path.dirname(self.database_path)
                self.logger.info(f"資料庫目錄: {db_dir}, 是否存在: {os.path.exists(db_dir)}")
                # 檢查是否有寫入權限
                if os.path.exists(db_dir):
                    self.logger.info(f"目錄寫入權限: {os.access(db_dir, os.W_OK)}")

                # 嘗試創建資料庫目錄
                os.makedirs(db_dir, exist_ok=True)

                # 確保導入了 CoreDataDownloader
                from core.data_downloader import CoreDataDownloader

                self.core_downloader = CoreDataDownloader(self.api_key, self.database_path)
                self.logger.info(f"成功初始化 CoreDataDownloader，使用路徑: {self.database_path}")
                return True
            return True
        except Exception as e:
            self.logger.error(f"連接資料庫時發生錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def close(self):
        """關閉資料庫連接"""
        if self.core_downloader:
            self.core_downloader.close()
            self.logger.info("Core downloader connection closed.")

    def set_api_key(self, api_key):
        """設置API金鑰"""
        self.logger.info(f"Setting API Key: {api_key[:5]}******")
        self.api_key = api_key
        if self.core_downloader:
            self.logger.info("Updating API key on existing core_downloader instance.")
            self.core_downloader.api_key = api_key
        else:
            self.logger.info(
                "Core downloader instance not yet created. API key will be used on initialization."
            )

    def set_download_options(self, options):
        """設置下載選項"""
        self.download_options.update(options)

    def set_news_days(self, days):
        """設置新聞下載天數"""
        self.news_days = max(1, min(90, days))  # 限制在1-90天內

    def start_download(self):
        """開始下載過程 (非阻塞)"""
        if self.status.is_downloading:
            return False

        if not self.api_key:
            self.status.add_log("error", "未設置API金鑰，請先設置API金鑰")
            self.logger.error("Download already in progress.")
            return False

        self.logger.info(
            f"Checking API Key before starting download: {'Set' if self.api_key else 'Not Set'}"
        )
        if not self.api_key:
            self.status.add_log("error", "未設置API金鑰，請先設置API金鑰")
            self.logger.error("API Key not set. Download cannot start.")
            return False

        # 檢查是否有任何下載選項
        selected_option_keys = [k for k, v in self.download_options.items() if v]
        self.logger.info(f"Selected download options: {selected_option_keys}")
        if not selected_option_keys:
            self.status.add_log("error", "未選擇任何下載選項")
            self.logger.error("No download options selected. Download cannot start.")
            return False

        # 啟動下載線程
        self.logger.info("Attempting to start download thread...")
        self._download_thread = threading.Thread(target=self._download_process)
        self._download_thread.daemon = True  # Ensure thread doesn't block exit
        try:
            self._download_thread.start()
            self.logger.info("Download thread started successfully.")
            return True
        except Exception as e:
            self.logger.error(f"Failed to start download thread: {str(e)}")
            self.logger.error(traceback.format_exc())
            self.status.add_log("error", f"無法啟動下載線程: {str(e)}")
            return False

    def get_status(self):
        """獲取當前下載狀態"""
        return self.status.get_status()

    def _download_process(self):
        """下載處理主流程 (在單獨線程中執行)"""
        self.logger.info("Entering _download_process thread.")
        try:
            # 標記開始下載
            self.status.start()
            self.logger.info("Download status set to starting.")
            self.status.add_log("info", "===== 開始下載資料 =====")
            self.status.add_log("info", f"資料庫路徑: {self.database_path}")
            self.status.add_log("info", f"API金鑰: {self.api_key[:5]}*******")  # Log API key safely
            if not self.connect_database():
                self.logger.error(
                    "Failed to connect database or initialize CoreDataDownloader in thread."
                )
                self.status.add_log("error", "無法連接資料庫或初始化核心下載器")
                self.status.complete(False)
                return
            self.logger.info("Database connected and CoreDataDownloader initialized successfully.")
            # 連接資料庫
            self.logger.info("Attempting to connect database and initialize CoreDataDownloader...")

            # --- 新增/確保以下區塊存在 ---
            self.logger.info("Attempting to download market index data...")
            self.status.set_progress(self.status.progress, "正在下載大盤指數資料...")
            if self.core_downloader.download_market_index_data(
                db_index_code="TWII"
            ):  # 使用 'TWII' 作為範例
                self.status.add_log("success", "大盤指數資料下載完成")
            else:
                self.status.add_log("error", "大盤指數資料下載失敗")
            self.logger.info("Pausing for 1 second after market index download...")
            time.sleep(1)  # 可選的延遲
            # --- 結束新增區塊 ---
            # 獲取選中的下載選項
            selected_options = [
                option for option, selected in self.download_options.items() if selected
            ]
            self.logger.info(f"Selected options for download: {selected_options}")

            # 依次處理每個下載選項
            total_options = len(selected_options)
            self.logger.info(f"Total options to download: {total_options}")
            for i, option in enumerate(selected_options):
                option_name = self._get_option_display_name(option)
                progress_val = (i + 1) * 100 / total_options  # Progress after completing the task
                self.logger.info(f"Processing option {i+1}/{total_options}: {option_name}")
                self.status.set_progress(
                    progress=i * 100 / total_options,  # Progress before starting the task
                    task=f"正在下載 {option_name}...",
                )

                # 添加下載日誌
                self.status.add_log("info", f"開始下載{option_name}...")

                # 根據選項調用相應的下載方法
                try:
                    if option == "price":
                        self.core_downloader.download_price_data()
                    elif option == "technical":
                        self.core_downloader.download_technical_indicators()
                    elif option == "company":
                        self.core_downloader.download_company_info()
                        self.core_downloader.download_stock_categories()
                    elif option == "financial":
                        self.core_downloader.download_financial_data()
                    elif option == "institutional":
                        self.core_downloader.download_institutional_investors_data()
                    elif option == "margin":
                        self.core_downloader.download_margin_trading_data()
                    elif option == "revenue":
                        # 月營收資料可能需要額外實現
                        self.status.add_log("warning", "月營收資料下載功能尚未完全實現")
                    elif option == "economic":
                        # 經濟指標資料可能需要額外實現
                        self.status.add_log("warning", "經濟指標資料下載功能尚未完全實現")
                    elif option == "news":
                        # 新聞資料可能需要額外實現
                        self.status.add_log("warning", "新聞資料下載功能尚未完全實現")
                    self.logger.info(f"Download successful for {option_name}")
                    # 標記此選項完成
                    self.status.add_log("success", f"{option_name} 下載完成")

                except Exception as e:
                    error_msg = str(e)
                    self.logger.error(f"Error downloading {option_name}: {error_msg}")
                    self.logger.error(traceback.format_exc())
                    self.status.add_log("error", f"下載 {option_name} 時發生錯誤: {error_msg}")
                    # Continue to next option even if one fails
                # Update progress after task completion attempt
                self.status.set_progress(progress=progress_val, task=f"已處理 {option_name}")

                # 等待片刻，避免過於頻繁的API請求
                self.logger.info("Pausing for 1 second before next option...")
                time.sleep(1)

            # 下載完成
            self.logger.info("All download options processed.")
            self.status.add_log("success", "===== 所有資料下載完成 =====")
            self.status.complete(True)  # Mark as success even if some options failed
        except Exception as e:
            # 處理整體過程中的異常
            error_msg = str(e)
            self.logger.error(f"Critical error during download process: {error_msg}")
            self.logger.error(traceback.format_exc())
            self.status.add_log("error", f"下載過程中發生嚴重錯誤: {error_msg}")
            self.status.complete(False)
        finally:
            # 確保關閉資料庫連接
            self.logger.info("Download process finished. Closing database connection.")
            self.close()
            self.logger.info("Exiting _download_process thread.")

    def _get_option_display_name(self, option):
        """獲取選項的顯示名稱"""
        option_names = {
            "price": "股價資料",
            "technical": "技術指標",
            "company": "公司資訊",
            "financial": "財務報表",
            "institutional": "三大法人資料",
            "margin": "融資融券資料",
            "revenue": "月營收資料",
            "economic": "經濟指標",
            "news": "新聞資料",
        }
        return option_names.get(option, option)


class DataDownloaderAPI:
    """資料下載器API，用於與Web前端整合"""

    def __init__(self, database_path="tw_stock_data.db"):
        """初始化API"""
        # 使用絕對路徑
        if not os.path.isabs(database_path):
            base_dir = "/Users/<USER>/python/training/stock/main-news/"
            database_path = os.path.join(base_dir, "database", database_path)

        # Use the same logger instance
        self.logger = logging.getLogger("DataDownloaderAPI")
        self.logger.info(f"Initializing DataDownloaderAPI with database: {database_path}")
        self.downloader = DataDownloader(database_path)
        self._last_api_key = None
        self.logger.info("DataDownloaderAPI initialized.")

    def login(self, api_key):
        """登入API

        Args:
            api_key: FinLab API金鑰
        Returns:
            Dict: 登入結果
        """
        self.logger.info(f"Login attempt with API key: {api_key[:5]}******")
        try:
            # 設置API金鑰
            self.downloader.set_api_key(api_key)

            # 確保連接資料庫並初始化 core_downloader
            self.logger.info("Connecting database for API key test...")
            if not self.downloader.connect_database():
                self.logger.error(
                    "Failed to connect database or initialize downloader during login."
                )
                return {"status": "error", "message": "無法連接資料庫或初始化下載器"}
            self.logger.info("Database connected for API key test.")

            # 測試API金鑰是否有效
            self.logger.info("Testing API key validity...")
            is_valid = (
                self.downloader.core_downloader and self.downloader.core_downloader.test_api_key()
            )
            self.logger.info(f"API key test result: {'Valid' if is_valid else 'Invalid'}")

            if is_valid:
                self._last_api_key = api_key
                self.logger.info("Login successful.")
                return {"status": "success", "message": "登入成功"}
            else:
                self.logger.warning("API key invalid.")
                return {"status": "error", "message": "API金鑰無效，請檢查金鑰是否正確"}
        except Exception as e:
            self.logger.error(f"Exception during login: {str(e)}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"登入時發生未預期錯誤: {str(e)}"}

    def start_download(self, options, news_days=7):
        """開始下載過程

        Args:
            options: 下載選項
            news_days: 新聞下載天數
        Returns:
            Dict: 開始下載的結果
        """
        self.logger.info(
            f"Received start_download request. Options: {options}, News days: {news_days}"
        )
        try:
            self.downloader.set_download_options(options)
            self.downloader.set_news_days(news_days)

            self.logger.info("Calling downloader.start_download()...")
            success = self.downloader.start_download()

            if success:
                self.logger.info("downloader.start_download() returned True.")
                return {"status": "success", "message": "下載已開始"}
            else:
                self.logger.warning("downloader.start_download() returned False.")
                # Get the latest log from status to provide more context
                latest_log = (
                    self.downloader.get_status()["logs"][-1]["message"]
                    if self.downloader.get_status()["logs"]
                    else "請檢查日誌"
                )
                return {"status": "error", "message": f"無法開始下載: {latest_log}"}
        except Exception as e:
            self.logger.error(f"Exception during start_download: {str(e)}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"開始下載時發生未預期錯誤: {str(e)}"}

    def get_status(self):
        """獲取下載狀態
        Returns:
            Dict: 下載狀態
        """
        # No need for extra logging here unless debugging status retrieval itself
        try:
            return self.downloader.get_status()
        except Exception as e:
            self.logger.error(f"Exception during get_status: {str(e)}")
            self.logger.error(traceback.format_exc())
            return {
                "status": "error",
                "message": f"獲取狀態時發生未預期錯誤: {str(e)}",
                "progress": 0,
                "logs": [],
            }

    def check_api_key(self):
        """檢查API金鑰狀態

        Returns:
            Dict: API金鑰狀態
        """
        return {"status": "success", "has_key": self.downloader.api_key is not None}
