# /ui/routes/download_routes.py
"""
下載模組的 Flask 路由處理
處理下載相關的 Web 請求
"""

import json
import logging
import traceback

from flask import Blueprint, jsonify, render_template, request

# 引入我們的新下載器API
from ui.routes.data_downloader import DataDownloaderAPI

# 設置日誌
logger = logging.getLogger("DownloadRoutes")

# 創建 Blueprint
download_bp = Blueprint("download", __name__)

# 全局 API 實例
downloader_api = DataDownloaderAPI()


# 頁面路由 - 資料下載頁面
@download_bp.route("/download")
def download_page():
    """下載頁面"""
    return render_template("download.html")


# API 路由 - 登入 FinLab API
@download_bp.route("/api/login", methods=["POST"])
def login_api():
    """登入 FinLab API"""
    try:
        data = request.json
        if not data:
            return jsonify({"status": "error", "message": "請求資料格式錯誤"}), 400

        api_key = data.get("api_key", "")

        if not api_key:
            return jsonify({"status": "error", "message": "請提供 API 金鑰"}), 400

        result = downloader_api.login(api_key)
        return jsonify(result)

    except Exception as e:
        logger.error(f"登入請求處理錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"處理請求時發生錯誤: {str(e)}"}), 500


# API 路由 - 開始下載
@download_bp.route("/api/start_download", methods=["POST"])
def start_download():
    """開始下載資料"""
    try:
        data = request.json
        if not data:
            return jsonify({"status": "error", "message": "請求資料格式錯誤"}), 400

        options = data.get("options", {})
        news_days = int(data.get("news_days", 7))

        # 驗證選項
        valid_options = [
            "price",
            "technical",
            "company",
            "financial",
            "institutional",
            "margin",
            "revenue",
            "economic",
            "news",
        ]

        # 過濾無效選項
        filtered_options = {k: v for k, v in options.items() if k in valid_options}

        # 檢查是否有任何選項
        if not any(filtered_options.values()):
            return jsonify({"status": "error", "message": "請至少選擇一種資料類型"}), 400

        # 開始下載
        result = downloader_api.start_download(filtered_options, news_days)
        return jsonify(result)

    except Exception as e:
        logger.error(f"開始下載請求處理錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"處理請求時發生錯誤: {str(e)}"}), 500


# API 路由 - 獲取下載狀態
@download_bp.route("/api/download_status", methods=["GET"])
def get_download_status():
    """獲取下載狀態"""
    try:
        status = downloader_api.get_status()

        # 限制返回的日誌數量，避免響應過大
        if "logs" in status and len(status["logs"]) > 100:
            status["logs"] = status["logs"][-100:]  # 只返回最新的100條日誌

        return jsonify(status)

    except Exception as e:
        logger.error(f"獲取狀態請求處理錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"處理請求時發生錯誤: {str(e)}",
                    "progress": 0,
                    "logs": [],
                }
            ),
            500,
        )


# API 路由 - 檢查 API 金鑰狀態
@download_bp.route("/api/check_api_key", methods=["GET"])
def check_api_key():
    """檢查 API 金鑰狀態"""
    try:
        result = downloader_api.check_api_key()
        return jsonify(result)

    except Exception as e:
        logger.error(f"檢查 API 金鑰請求處理錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"處理請求時發生錯誤: {str(e)}"}), 500


# API 路由 - 獲取下載選項說明
@download_bp.route("/api/download_options", methods=["GET"])
def get_download_options():
    """獲取下載選項說明"""
    try:
        # 提供下載選項的說明資訊
        options = {
            "price": {
                "name": "股價資料",
                "description": "股票的開盤、收盤、最高、最低價及成交量",
                "category": "核心資料",
                "default": True,
            },
            "technical": {
                "name": "技術指標",
                "description": "KD、RSI、MACD、均線等技術分析指標",
                "category": "核心資料",
                "default": True,
            },
            "company": {
                "name": "公司資訊",
                "description": "基本資料、產業分類等公司基本資料",
                "category": "核心資料",
                "default": True,
            },
            "financial": {
                "name": "財務報表",
                "description": "資產、負債、營收、獲利等財務報表資料",
                "category": "財務資料",
                "default": False,
            },
            "institutional": {
                "name": "三大法人資料",
                "description": "外資、投信、自營商等三大法人買賣資料",
                "category": "交易資料",
                "default": False,
            },
            "margin": {
                "name": "融資融券資料",
                "description": "信用交易相關資料",
                "category": "交易資料",
                "default": False,
            },
            "revenue": {
                "name": "月營收資料",
                "description": "每月營收數據",
                "category": "財務資料",
                "default": False,
            },
            "economic": {
                "name": "經濟指標",
                "description": "景氣對策信號、PMI等經濟指標",
                "category": "其他資料",
                "default": False,
            },
            "news": {
                "name": "新聞資料",
                "description": "股票相關新聞及其情緒分析",
                "category": "其他資料",
                "default": False,
            },
        }

        return jsonify({"status": "success", "options": options})

    except Exception as e:
        logger.error(f"獲取選項說明請求處理錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"處理請求時發生錯誤: {str(e)}"}), 500


# 將 Blueprint 註冊到 Flask 應用
def register_download_routes(app):
    """將下載路由註冊到 Flask 應用"""
    logger.info(f"註冊下載路由，Blueprint: {download_bp.name}")
    app.register_blueprint(download_bp)

    # 列出所有路由（在註冊後）
    logger.info("應用已註冊的路由:")
    for rule in app.url_map.iter_rules():
        logger.info(f"{rule.endpoint}: {rule.rule}")
