# /ui/routes/watchlist_routes.py
import json
import logging
import sqlite3
import traceback

from flask import Blueprint, jsonify, render_template, request

# 設置日誌
logger = logging.getLogger("WatchlistRoutes")

# 創建 Blueprint
watchlist_bp = Blueprint("watchlist", __name__)


# 資料庫連接函數
def get_db_connection(
    database_path="/Users/<USER>/python/training/stock/main-news/database/tw_stock_data.db",
):
    return sqlite3.connect(database_path)


# 獲取股票信息 API
@watchlist_bp.route("/api/stock_info/<stock_id>", methods=["GET"])
def get_stock_info(stock_id):
    try:
        conn = get_db_connection()  # 假設 get_db_connection() 已定義
        cursor = conn.cursor()

        # 獲取股票名稱
        cursor.execute(
            """
            SELECT 公司簡稱 FROM company_info WHERE stock_id = ?
        """,
            (stock_id,),
        )
        company_result = cursor.fetchone()

        # 獲取最近價格 (從 value 欄位讀取並重命名為 close)
        cursor.execute(
            """
            SELECT value AS close, date FROM price_close 
            WHERE stock_id = ? 
            ORDER BY date DESC 
            LIMIT 2
        """,
            (stock_id,),
        )
        price_results = cursor.fetchall()

        # 獲取成交量 (從 value 欄位讀取並重命名為 volume)
        cursor.execute(
            """
            SELECT value AS volume FROM price_volume 
            WHERE stock_id = ? 
            ORDER BY date DESC 
            LIMIT 1
        """,
            (stock_id,),
        )
        volume_result = cursor.fetchone()

        # 獲取預測結果 (假設 stock_predictions 資料表結構正確)
        cursor.execute(
            """
            SELECT prediction, probability FROM stock_predictions 
            WHERE stock_id = ? 
            ORDER BY prediction_date DESC 
            LIMIT 1
        """,
            (stock_id,),
        )
        prediction_result = cursor.fetchone()

        conn.close()

        # 計算漲跌幅
        change = None
        change_percent = None  # 新增變數
        current_price_value = None  # 新增變數

        if price_results and len(price_results) > 0:
            current_price_value = price_results[0][0]  # close (AS value)
            if len(price_results) > 1:
                previous_price_value = price_results[1][0]  # close (AS value)
                if previous_price_value and previous_price_value != 0:  # 避免除以零
                    change = current_price_value - previous_price_value
                    change_percent = (change / previous_price_value) * 100

        result = {
            "status": "success",
            "stock_id": stock_id,
            "name": company_result[0] if company_result else None,
            "price": current_price_value if current_price_value is not None else None,
            "date": price_results[0][1] if price_results else None,
            "change_value": change,  # 實際漲跌數值
            "change_percent": change_percent,  # 漲跌百分比
            "volume": volume_result[0] if volume_result else None,
            "prediction": prediction_result[0] if prediction_result else None,
            "probability": prediction_result[1] if prediction_result else None,
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"獲取股票信息時發生錯誤: {str(e)}")
        logger.error(traceback.format_exc())  # 添加 traceback
        return jsonify({"status": "error", "message": f"獲取股票信息時發生錯誤: {str(e)}"}), 500


# 獲取觀察清單
@watchlist_bp.route("/api/watchlist", methods=["GET"])
def get_watchlist():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        query = """
        SELECT w.id, w.stock_id, w.notes, w.created_at, 
               p.close as current_price
        FROM watchlist w
        LEFT JOIN price_close p ON w.stock_id = p.stock_id
        WHERE p.date = (SELECT MAX(date) FROM price_close WHERE stock_id = w.stock_id)
        ORDER BY w.created_at DESC
        """

        cursor.execute(query)
        items = cursor.fetchall()

        watchlist = []
        for item in items:
            watchlist.append(
                {
                    "id": item[0],
                    "stock_id": item[1],
                    "notes": item[2],
                    "created_at": item[3],
                    "current_price": item[4],
                }
            )

        conn.close()
        return jsonify({"status": "success", "watchlist": watchlist})

    except Exception as e:
        logger.error(f"獲取觀察清單時發生錯誤: {str(e)}")
        return jsonify({"status": "error", "message": f"獲取觀察清單時發生錯誤: {str(e)}"}), 500


# 添加股票到觀察清單
@watchlist_bp.route("/api/watchlist/add", methods=["POST"])
def add_to_watchlist():
    try:
        data = request.json
        stock_id = data.get("stock_id")
        notes = data.get("notes", "")

        if not stock_id:
            return jsonify({"status": "error", "message": "股票代碼不能為空"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 檢查是否已存在
        cursor.execute("SELECT id FROM watchlist WHERE stock_id = ?", (stock_id,))
        existing = cursor.fetchone()

        if existing:
            return jsonify({"status": "error", "message": "該股票已在觀察清單中"}), 400

        # 添加到觀察清單
        cursor.execute("INSERT INTO watchlist (stock_id, notes) VALUES (?, ?)", (stock_id, notes))

        conn.commit()
        watchlist_id = cursor.lastrowid
        conn.close()

        return jsonify({"status": "success", "message": "已添加到觀察清單", "id": watchlist_id})

    except Exception as e:
        logger.error(f"添加到觀察清單時發生錯誤: {str(e)}")
        return jsonify({"status": "error", "message": f"添加到觀察清單時發生錯誤: {str(e)}"}), 500


# 從觀察清單中刪除股票
@watchlist_bp.route("/api/watchlist/remove/<int:item_id>", methods=["DELETE"])
def remove_from_watchlist(item_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM watchlist WHERE id = ?", (item_id,))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({"status": "error", "message": "找不到指定的觀察清單項目"}), 404

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "已從觀察清單中移除"})

    except Exception as e:
        logger.error(f"從觀察清單中移除時發生錯誤: {str(e)}")
        return jsonify({"status": "error", "message": f"從觀察清單中移除時發生錯誤: {str(e)}"}), 500


# 更新觀察清單項目的備註
@watchlist_bp.route("/api/watchlist/update/<int:item_id>", methods=["PUT"])
def update_watchlist_item(item_id):
    try:
        data = request.json
        notes = data.get("notes", "")

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("UPDATE watchlist SET notes = ? WHERE id = ?", (notes, item_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({"status": "error", "message": "找不到指定的觀察清單項目"}), 404

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "觀察清單項目已更新"})

    except Exception as e:
        logger.error(f"更新觀察清單項目時發生錯誤: {str(e)}")
        return jsonify({"status": "error", "message": f"更新觀察清單項目時發生錯誤: {str(e)}"}), 500


# 在 app.py 或 web_app.py 中註冊路由
# 註冊 Blueprint 的函數
def register_watchlist_routes(app):
    """將觀察清單路由註冊到 Flask 應用"""
    logger.info(f"註冊觀察清單路由，Blueprint: {watchlist_bp.name}")
    app.register_blueprint(watchlist_bp)
