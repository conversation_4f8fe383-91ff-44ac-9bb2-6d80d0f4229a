/**
 * 下載頁面的 JavaScript 代碼
 * 處理 API 金鑰設定、下載選項和進度顯示
 */

// 安全存取 localStorage
const storage = {
    getItem: function(key) {
        try {
            return window.localStorage.getItem(key);
        } catch (e) {
            console.warn('無法使用 localStorage:', e);
            return null;
        }
    },
    setItem: function(key, value) {
        try {
            window.localStorage.setItem(key, value);
            return true;
        } catch (e) {
            console.warn('無法使用 localStorage:', e);
            return false;
        }
    },
    removeItem: function(key) {
        try {
            window.localStorage.removeItem(key);
            return true;
        } catch (e) {
            console.warn('無法使用 localStorage:', e);
            return false;
        }
    }
};

// 下載管理器類
class DownloadManager {
    constructor() {
        // 初始化狀態
        this.isDownloading = false;
        this.progress = 0;
        this.currentTask = "";
        this.downloadOptions = {};
        this.newsDays = 7;
        this.statusPollingInterval = null;
        
        // 獲取 API 金鑰
        this.apiKey = storage.getItem('finlab_api_key') || "";
        
        // UI 元素
        this.progressBar = document.getElementById('downloadProgressBar');
        this.statusText = document.getElementById('currentDownloadStatus');
        this.logContainer = document.getElementById('downloadLog');
        this.progressSection = document.getElementById('progressSection');
        
        // 初始化事件監聽
        this.initEventListeners();
        
        // 檢查 API 金鑰狀態
        this.checkApiKey();
        
        // 獲取下載選項
        this.loadDownloadOptions();
    }
    
    // 初始化事件監聽
    initEventListeners() {
        // API 金鑰登入按鈕
        const loginBtn = document.getElementById('loadApiKeyBtn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => this.loginWithApiKey());
        }
        
        // 選擇金鑰檔案按鈕
        const fileLoadBtn = document.getElementById('apiKeyFromFileBtn');
        if (fileLoadBtn) {
            fileLoadBtn.addEventListener('click', () => this.loadApiKeyFromFile());
        }
        
        // 變更金鑰按鈕
        const changeKeyBtn = document.getElementById('changeApiKeyBtn');
        if (changeKeyBtn) {
            changeKeyBtn.addEventListener('click', () => this.showApiKeyInput());
        }
        
        // 全選按鈕
        const selectAllBtn = document.getElementById('selectAllBtn');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAllOptions());
        }
        
        // 開始下載按鈕
        const startDownloadBtn = document.getElementById('startDownloadBtn');
        if (startDownloadBtn) {
            startDownloadBtn.addEventListener('click', () => this.startDownload());
        }
        
        // 檔案輸入框變更事件
        const fileInput = document.getElementById('apiKeyFileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.loadApiKeyFromFile();
                }
            });
        }
    }
    
    // 檢查 API 金鑰狀態
    async checkApiKey() {
        try {
            // 檢查是否已有 API 金鑰
            if (this.apiKey) {
                document.querySelectorAll('.no-api-key').forEach(el => el.style.display = 'none');
                document.querySelectorAll('.has-api-key').forEach(el => el.style.display = 'block');
            } else {
                document.querySelectorAll('.no-api-key').forEach(el => el.style.display = 'block');
                document.querySelectorAll('.has-api-key').forEach(el => el.style.display = 'none');
            }
            
            // 可以選擇向後端驗證 API 金鑰的有效性
            // const response = await fetch('/api/check_api_key');
            // const result = await response.json();
            // if (result.status === 'success' && result.has_key) {
            //     ...
            // }
        } catch (error) {
            console.error('檢查 API 金鑰時出錯:', error);
        }
    }
    
    // 使用 API 金鑰登入
    async loginWithApiKey() {
        const apiKeyInput = document.getElementById('apiKeyInput');
        const apiKey = apiKeyInput.value.trim();
        
        if (!apiKey) {
            this.showAlert('請輸入有效的 API 金鑰', 'danger');
            return;
        }
        
        try {
            // 顯示處理中狀態
            const btn = document.getElementById('loadApiKeyBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>登入中...';
            btn.disabled = true;
            
            // 發送 API 請求
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ api_key: apiKey }),
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                // 登入成功
                this.apiKey = apiKey;
                storage.setItem('finlab_api_key', apiKey);
                this.showAlert('登入成功', 'success');
                
                // 更新 UI
                document.querySelectorAll('.no-api-key').forEach(el => el.style.display = 'none');
                document.querySelectorAll('.has-api-key').forEach(el => el.style.display = 'block');
                
                // 滾動到下載選項區域
                document.querySelector('.download-container:nth-child(2)').scrollIntoView({
                    behavior: 'smooth'
                });
            } else {
                // 登入失敗
                this.showAlert(`登入失敗: ${result.message || '請檢查 API 金鑰是否正確'}`, 'danger');
            }
            
            // 重設按鈕
            btn.innerHTML = originalText;
            btn.disabled = false;
        } catch (error) {
            console.error('登入請求錯誤:', error);
            this.showAlert('登入請求發生錯誤，請稍後再試', 'danger');
            
            // 重設按鈕
            const btn = document.getElementById('loadApiKeyBtn');
            btn.innerHTML = '<i class="fas fa-key me-2"></i>登入 FinLab';
            btn.disabled = false;
        }
    }
    
    // 從檔案載入 API 金鑰
    loadApiKeyFromFile() {
        const fileInput = document.getElementById('apiKeyFileInput');
        const file = fileInput.files[0];
        
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const apiKey = e.target.result.trim();
                if (apiKey) {
                    document.getElementById('apiKeyInput').value = apiKey;
                    this.loginWithApiKey();
                } else {
                    this.showAlert('檔案內容為空', 'warning');
                }
            };
            reader.onerror = () => {
                this.showAlert('讀取檔案時發生錯誤', 'danger');
            };
            reader.readAsText(file);
        } else {
            this.showAlert('請先選擇 API 金鑰檔案', 'warning');
        }
    }
    
    // 顯示 API 金鑰輸入區域
    showApiKeyInput() {
        document.querySelectorAll('.no-api-key').forEach(el => el.style.display = 'block');
        document.querySelectorAll('.has-api-key').forEach(el => el.style.display = 'none');
    }
    
    // 載入下載選項
    async loadDownloadOptions() {
        try {
            // 從後端獲取下載選項
            const response = await fetch('/api/download_options');
            const result = await response.json();
            
            if (result.status === 'success') {
                this.downloadOptions = result.options;
                this.renderDownloadOptions();
            } else {
                console.error('無法載入下載選項:', result.message);
                
                // 使用預設選項
                this.useDefaultOptions();
            }
        } catch (error) {
            console.error('載入下載選項時出錯:', error);
            
            // 使用預設選項
            this.useDefaultOptions();
        }
    }
    
    // 使用預設選項
    useDefaultOptions() {
        this.downloadOptions = {
            "price": {
                "name": "股價資料",
                "description": "股票的開盤、收盤、最高、最低價及成交量",
                "category": "核心資料",
                "default": true
            },
            "technical": {
                "name": "技術指標",
                "description": "KD、RSI、MACD、均線等技術分析指標",
                "category": "核心資料",
                "default": true
            },
            "company": {
                "name": "公司資訊",
                "description": "基本資料、產業分類等公司基本資料",
                "category": "核心資料",
                "default": true
            },
            "financial": {
                "name": "財務報表",
                "description": "資產、負債、營收、獲利等財務報表資料",
                "category": "財務資料",
                "default": false
            },
            "institutional": {
                "name": "三大法人資料",
                "description": "外資、投信、自營商等三大法人買賣資料",
                "category": "交易資料",
                "default": false
            },
            "margin": {
                "name": "融資融券資料",
                "description": "信用交易相關資料",
                "category": "交易資料",
                "default": false
            },
            "revenue": {
                "name": "月營收資料",
                "description": "每月營收數據",
                "category": "財務資料",
                "default": false
            },
            "economic": {
                "name": "經濟指標",
                "description": "景氣對策信號、PMI等經濟指標",
                "category": "其他資料",
                "default": false
            },
            "news": {
                "name": "新聞資料",
                "description": "股票相關新聞及其情緒分析",
                "category": "其他資料",
                "default": false
            }
        };
        
        this.renderDownloadOptions();
    }
    
    // 渲染下載選項
    renderDownloadOptions() {
        // 分類選項
        const categories = {};
        
        // 整理選項到分類
        Object.entries(this.downloadOptions).forEach(([key, option]) => {
            const category = option.category || '其他';
            if (!categories[category]) {
                categories[category] = [];
            }
            categories[category].push({
                key,
                ...option
            });
        });
        
        // 更新核心資料區域
        if (categories['核心資料']) {
            const coreContainer = document.querySelector('.card-body:has(.form-check)');
            if (coreContainer) {
                this.renderOptionsToContainer(coreContainer, categories['核心資料']);
            }
        }
        
        // 更新其他分類
        Object.entries(categories).forEach(([category, options]) => {
            if (category !== '核心資料') {
                // 查找或創建此分類的容器
                let container = document.querySelector(`.card-header:contains(${category})`);
                if (!container) {
                    // 可以動態創建分類卡片
                    console.log(`缺少 ${category} 的分類卡片`);
                } else {
                    container = container.nextElementSibling;
                    this.renderOptionsToContainer(container, options);
                }
            }
        });
    }
    
    // 將選項渲染到容器
    renderOptionsToContainer(container, options) {
        container.innerHTML = '';
        
        options.forEach(option => {
            const checkboxId = `${option.key}Check`;
            
            const checkboxHtml = `
                <div class="form-check">
                    <input class="form-check-input download-option-checkbox" 
                           type="checkbox" 
                           value="${option.key}" 
                           id="${checkboxId}" 
                           ${option.default ? 'checked' : ''}>
                    <label class="form-check-label" for="${checkboxId}">
                        ${option.name} <small class="text-muted">(${option.description})</small>
                    </label>
                </div>
            `;
            
            container.innerHTML += checkboxHtml;
        });
    }
    
    // 全選下載選項
    selectAllOptions() {
        document.querySelectorAll('.download-option-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
    }
    
    // 開始下載
    async startDownload() {
        // 檢查是否已經在下載中
        if (this.isDownloading) {
            this.showAlert('下載已在進行中', 'warning');
            return;
        }
        
        // 檢查是否有 API 金鑰
        if (!this.apiKey) {
            this.showAlert('請先登入 FinLab API', 'danger');
            return;
        }
        
        // 獲取所有選中的資料類型
        const selectedOptions = {};
        document.querySelectorAll('.download-option-checkbox:checked').forEach(checkbox => {
            selectedOptions[checkbox.value] = true;
        });
        
        if (Object.keys(selectedOptions).length === 0) {
            this.showAlert('請至少選擇一種資料類型', 'danger');
            return;
        }
        
        // 獲取新聞下載天數
        const newsDaysSelect = document.getElementById('newsDaysInput');
        this.newsDays = newsDaysSelect ? parseInt(newsDaysSelect.value, 10) : 7;
        
        // 確認是否繼續下載
        const optionNames = Object.keys(selectedOptions).map(key => 
            this.downloadOptions[key] ? this.downloadOptions[key].name : key
        );
        
        const confirmMessage = `即將下載以下資料類型:\n${optionNames.join(', ')}\n\n此過程可能需要一段時間。要繼續嗎？`;
        if (!confirm(confirmMessage)) {
            return;
        }
        
        try {
            // 顯示進度區域
            this.progressSection.style.display = 'block';
            this.logContainer.innerHTML = '<div class="log-entry">準備開始下載，請稍候...</div>';
            this.progressBar.style.width = '0%';
            this.progressBar.textContent = '0%';
            this.statusText.textContent = '準備中...';
            
            // 發送開始下載請求
            const response = await fetch('/api/start_download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    options: selectedOptions,
                    news_days: this.newsDays
                }),
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                // 開始下載成功
                this.isDownloading = true;
                
                // 開始輪詢下載進度
                this.startProgressPolling();
                
                // 滾動到進度區域
                this.progressSection.scrollIntoView({
                    behavior: 'smooth'
                });
            } else {
                // 開始下載失敗
                this.showAlert(`無法開始下載: ${result.message}`, 'danger');
            }
        } catch (error) {
            console.error('發送下載請求時出錯:', error);
            this.showAlert('下載請求發生錯誤，請稍後再試', 'danger');
        }
    }
    
    // 開始輪詢下載進度
    startProgressPolling() {
        // 清除舊的輪詢
        if (this.statusPollingInterval) {
            clearInterval(this.statusPollingInterval);
        }
        
        // 設置新的輪詢
        this.statusPollingInterval = setInterval(async () => {
            await this.updateDownloadProgress();
        }, 1000); // 每秒更新一次
    }
    
    // 更新下載進度
    async updateDownloadProgress() {
        try {
            const response = await fetch('/api/download_status');
            const data = await response.json();
            
            if (data.status === 'error') {
                this.showAlert(`獲取進度失敗: ${data.message}`, 'danger');
                this.stopProgressPolling();
                return;
            }
            
            // 更新進度條
            this.progressBar.style.width = data.progress + '%';
            this.progressBar.textContent = data.progress + '%';
            this.statusText.textContent = data.current_task || '處理中...';
            
            // 更新日誌
            this.updateLogs(data.logs || []);
            
            // 如果下載完成
            if (data.completed) {
                this.stopProgressPolling();
                this.isDownloading = false;
                
                if (data.success) {
                    this.progressBar.classList.remove('progress-bar-animated');
                    this.showAlert('所有資料已成功下載！', 'success');
                } else {
                    this.showAlert('下載過程中發生錯誤，詳情請查看日誌', 'danger');
                }
            }
        } catch (error) {
            console.error('獲取下載進度時出錯:', error);
        }
    }
    
    // 停止進度輪詢
    stopProgressPolling() {
        if (this.statusPollingInterval) {
            clearInterval(this.statusPollingInterval);
            this.statusPollingInterval = null;
        }
    }
    
    // 更新日誌顯示
    updateLogs(logs) {
        if (!logs || logs.length === 0) return;
        
        // 保留原有的日誌內容
        const logEntries = Array.from(this.logContainer.querySelectorAll('.log-entry')).map(
            el => el.innerHTML
        );
        
        // 將新的日誌匯入到列表
        logs.forEach(log => {
            const logMessage = log.message;
            const logClass = log.level || '';
            
            // 如果這個日誌條目不在現有日誌中，則添加它
            if (!logEntries.includes(logMessage)) {
                const logHtml = `<div class="log-entry ${logClass}">${logMessage}</div>`;
                this.logContainer.innerHTML += logHtml;
                logEntries.push(logMessage);
            }
        });
        
        // 滾動到日誌底部
        this.logContainer.scrollTop = this.logContainer.scrollHeight;
    }
    
    // 顯示提示訊息
    showAlert(message, type = 'info') {
        // 如果使用 Bootstrap alert
        const alertBox = document.createElement('div');
        alertBox.className = `alert alert-${type} alert-dismissible fade show`;
        alertBox.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        // 查找 alert 容器
        const alertContainer = document.querySelector('.alert-container');
        if (alertContainer) {
            alertContainer.appendChild(alertBox);
            
            // 3秒後自動關閉
            setTimeout(() => {
                alertBox.classList.remove('show');
                setTimeout(() => alertBox.remove(), 150);
            }, 3000);
        } else {
            // 如果沒有容器，使用原生 alert
            alert(message);
        }
    }
}

// 頁面載入時初始化
document.addEventListener('DOMContentLoaded', function() {
    // 創建下載管理器實例
    window.downloadManager = new DownloadManager();
    
    // 檢查是否支援 FileReader API
    if (!window.FileReader) {
        document.getElementById('apiKeyFileInput').disabled = true;
        document.getElementById('apiKeyFromFileBtn').disabled = true;
        const fileMsg = document.createElement('div');
        fileMsg.className = 'form-text text-danger';
        fileMsg.textContent = '您的瀏覽器不支援檔案讀取，請直接輸入API金鑰';
        document.getElementById('apiKeyFileInput').parentNode.appendChild(fileMsg);
    }
    
    // 檢查 URL 參數是否有自動開始下載的指示
    const urlParams = new URLSearchParams(window.location.search);
    const autoStart = urlParams.get('auto_start');
    const dataType = urlParams.get('data_type');
    
    // 如果 URL 帶有自動開始參數且已有 API 金鑰，則自動選擇資料類型並開始下載
    if (autoStart === "true" && storage.getItem('finlab_api_key')) {
        setTimeout(() => {
            // 先取消全選
            document.querySelectorAll('.download-option-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // 選擇指定的資料類型或預設全選
            if (dataType) {
                // 如果指定了特定資料類型，就只選擇該類型
                const dataTypes = dataType.split(',');
                dataTypes.forEach(type => {
                    const checkbox = document.querySelector(`.download-option-checkbox[value="${type}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            } else {
                // 否則全選
                document.getElementById('selectAllBtn').click();
            }
            
            // 自動滾動到下載按鈕位置
            document.getElementById('startDownloadBtn').scrollIntoView({ behavior: 'smooth' });
            
            // 延遲一下再自動點擊下載按鈕
            setTimeout(() => {
                document.getElementById('startDownloadBtn').click();
            }, 1500);
        }, 1000);
    }
});