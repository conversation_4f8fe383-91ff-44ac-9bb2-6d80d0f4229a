// watchlist.js

document.addEventListener('DOMContentLoaded', function() {
    // 加載觀察清單
    loadWatchlist();
    
    // 綁定添加按鈕事件
    document.getElementById('addToWatchlistBtn').addEventListener('click', function() {
        addToWatchlist();
    });
    
    // 綁定清單創建事件
    document.getElementById('createWatchlistBtn').addEventListener('click', function() {
        createNewWatchlist();
    });
});

// 加載觀察清單
function loadWatchlist() {
    fetch('/api/watchlist')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                displayWatchlist(data.watchlist);
            } else {
                showError(data.message || '加載觀察清單失敗');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('加載觀察清單時發生錯誤');
        });
}

// 顯示觀察清單
function displayWatchlist(watchlist) {
    const watchlistContainer = document.getElementById('watchlistTable');
    watchlistContainer.innerHTML = '';
    
    if (watchlist.length === 0) {
        watchlistContainer.innerHTML = '<tr><td colspan="7" class="text-center">此觀察清單還沒有股票</td></tr>';
        return;
    }
    
    watchlist.forEach(item => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${item.stock_id}</td>
            <td class="stock-name">--</td>
            <td class="current-price">${item.current_price || '--'}</td>
            <td class="change">--</td>
            <td class="volume">--</td>
            <td class="prediction">--</td>
            <td class="notes">${item.notes || ''}</td>
            <td>
                <button class="btn btn-sm btn-primary edit-btn" data-id="${item.id}">編輯</button>
                <button class="btn btn-sm btn-danger delete-btn" data-id="${item.id}">移除</button>
            </td>
        `;
        
        watchlistContainer.appendChild(row);
        
        // 載入股票名稱和更多資訊
        loadStockInfo(item.stock_id, row);
    });
    
    // 綁定編輯和刪除按鈕事件
    bindEditButtons();
    bindDeleteButtons();
}

// 載入股票更多信息
function loadStockInfo(stockId, row) {
    // 這裡可以根據需要從其他 API 獲取股票名稱、漲跌幅等信息
    fetch(`/api/stock_info/${stockId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                row.querySelector('.stock-name').textContent = data.name || '--';
                
                if (data.change) {
                    const changeCell = row.querySelector('.change');
                    changeCell.textContent = data.change.toFixed(2) + '%';
                    changeCell.classList.add(data.change >= 0 ? 'text-success' : 'text-danger');
                }
                
                if (data.volume) {
                    row.querySelector('.volume').textContent = formatNumber(data.volume);
                }
                
                if (data.prediction) {
                    const predCell = row.querySelector('.prediction');
                    predCell.textContent = data.prediction === 'up' ? '看多' : '看空';
                    predCell.classList.add(data.prediction === 'up' ? 'text-success' : 'text-danger');
                }
            }
        })
        .catch(error => {
            console.error('Error loading stock info:', error);
        });
}

// 添加到觀察清單
function addToWatchlist() {
    const stockId = document.getElementById('stockIdInput').value.trim();
    const notes = document.getElementById('notesInput').value.trim();
    
    if (!stockId) {
        showError('請輸入股票代碼');
        return;
    }
    
    fetch('/api/watchlist/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            stock_id: stockId,
            notes: notes
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showSuccess(data.message);
            document.getElementById('stockIdInput').value = '';
            document.getElementById('notesInput').value = '';
            loadWatchlist();  // 重新加載清單
        } else {
            showError(data.message || '添加失敗');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('添加到觀察清單時發生錯誤');
    });
}

// 刪除觀察清單項目
function deleteWatchlistItem(itemId) {
    if (!confirm('確定要從觀察清單中移除此股票嗎？')) {
        return;
    }
    
    fetch(`/api/watchlist/remove/${itemId}`, {
        method: 'DELETE',
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showSuccess(data.message);
            loadWatchlist();  // 重新加載清單
        } else {
            showError(data.message || '移除失敗');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('從觀察清單移除時發生錯誤');
    });
}

// 綁定刪除按鈕事件
function bindDeleteButtons() {
    document.querySelectorAll('.delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.getAttribute('data-id');
            deleteWatchlistItem(itemId);
        });
    });
}

// 綁定編輯按鈕事件
function bindEditButtons() {
    document.querySelectorAll('.edit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.getAttribute('data-id');
            const notesCell = this.closest('tr').querySelector('.notes');
            const currentNotes = notesCell.textContent;
            
            const newNotes = prompt('編輯備註', currentNotes);
            
            if (newNotes !== null) {
                updateWatchlistItem(itemId, newNotes);
            }
        });
    });
}

// 更新觀察清單項目
function updateWatchlistItem(itemId, notes) {
    fetch(`/api/watchlist/update/${itemId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            notes: notes
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showSuccess(data.message);
            loadWatchlist();  // 重新加載清單
        } else {
            showError(data.message || '更新失敗');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('更新觀察清單項目時發生錯誤');
    });
}

// 創建新觀察清單
function createNewWatchlist() {
    const listName = document.getElementById('watchlistNameInput').value.trim();
    
    if (!listName) {
        showError('請輸入清單名稱');
        return;
    }
    
    // 這裡可以擴展為支持多個觀察清單
    showSuccess('創建成功！');
    document.getElementById('watchlistNameInput').value = '';
}

// 顯示錯誤消息
function showError(message) {
    const alertBox = document.getElementById('alertBox');
    alertBox.className = 'alert alert-danger';
    alertBox.textContent = message;
    alertBox.style.display = 'block';
    
    setTimeout(() => {
        alertBox.style.display = 'none';
    }, 3000);
}

// 顯示成功消息
function showSuccess(message) {
    const alertBox = document.getElementById('alertBox');
    alertBox.className = 'alert alert-success';
    alertBox.textContent = message;
    alertBox.style.display = 'block';
    
    setTimeout(() => {
        alertBox.style.display = 'none';
    }, 3000);
}

// 格式化數字（加千分位）
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function loadStockChart(stockId) {
    fetch(`/api/stock_price/${stockId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // 繪製K線圖
                const priceData = data.data;
                
                // 準備K線圖數據
                const chartData = priceData.map(item => ({
                    date: item.date,
                    open: item.open,
                    high: item.high,
                    low: item.low,
                    close: item.close,
                    volume: item.volume
                }));
                
                // 使用Chart.js或其他圖表庫繪製K線圖
                renderCandlestickChart('chartContainer', chartData);
                
                // 顯示最新價格及日期
                document.getElementById('latestPrice').textContent = `最新價格: ${priceData[0].close}`;
                document.getElementById('priceDate').textContent = `日期: ${priceData[0].date}`;
            }
        });
}