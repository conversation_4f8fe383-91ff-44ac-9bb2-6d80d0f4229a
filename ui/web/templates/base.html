<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}台股漲跌預測系統{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 56px;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            position: fixed;
            top: 56px;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 20px 0;
            overflow-x: hidden;
            overflow-y: auto;
            background-color: #343a40;
        }
        
        .sidebar .nav-link {
            color: #ced4da;
            font-weight: 500;
            padding: 10px 15px;
        }
        
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        
        .sidebar .nav-link:hover {
            color: #fff;
        }
        
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        
        @media (max-width: 767.98px) {
            .sidebar {
                top: 56px;
                width: 100%;
                position: relative;
                height: auto;
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        
        .card-header {
            font-weight: bold;
            background-color: #f8f9fa;
        }
        
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
            flex-direction: column;
        }
        
        .loading .spinner-border {
            width: 3rem;
            height: 3rem;
            margin-bottom: 1rem;
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">台股漲跌預測系統</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="bi bi-house-door"></i> 首頁</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 側邊欄 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/watchlist' %}active{% endif %}" href="/watchlist">
                                <i class="bi bi-star"></i> 觀察清單
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/download' %}active{% endif %}" href="/download">
                                <i class="bi bi-cloud-download"></i> 資料下載
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/train' %}active{% endif %}" href="/train">
                                <i class="bi bi-gear"></i> 模型訓練
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/predict' %}active{% endif %}" href="/predict">
                                <i class="bi bi-graph-up"></i> 股票預測
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/batch_predict' %}active{% endif %}" href="/batch_predict">
                                <i class="bi bi-diagram-3"></i> 批次預測
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/filter' %}active{% endif %}" href="/filter">
                                <i class="bi bi-funnel"></i> 股票篩選
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要內容 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                {% block content %}
                <div class="container-fluid">
                    <h1 class="mt-4 mb-4">股票觀察清單</h1>
                    
                    <div id="alertBox" class="alert" style="display: none;"></div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">添加股票到觀察清單</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="stockIdInput">股票代碼</label>
                                        <input type="text" class="form-control" id="stockIdInput" placeholder="例如: 2330">
                                    </div>
                                    <div class="form-group mt-2">
                                        <label for="notesInput">備註</label>
                                        <textarea class="form-control" id="notesInput" rows="2" placeholder="可選備註"></textarea>
                                    </div>
                                    <button id="addToWatchlistBtn" class="btn btn-primary mt-3">添加</button>
                                </div>
                            </div>
                            
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">創建新觀察清單</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="watchlistNameInput">清單名稱</label>
                                        <input type="text" class="form-control" id="watchlistNameInput" placeholder="例如: 科技股">
                                    </div>
                                    <button id="createWatchlistBtn" class="btn btn-success mt-3">創建清單</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">我的觀察清單</h5>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                            操作
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" id="refreshWatchlistBtn">刷新</a></li>
                                            <li><a class="dropdown-item" href="#" id="exportWatchlistBtn">匯出</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>代碼</th>
                                                    <th>名稱</th>
                                                    <th>現價</th>
                                                    <th>漲跌</th>
                                                    <th>成交量</th>
                                                    <th>預測</th>
                                                    <th>備註</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="watchlistTable">
                                                <tr>
                                                    <td colspan="8" class="text-center">載入中...</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <script src="{{ url_for('static', filename='js/watchlist.js') }}"></script>
                {% endblock %}
            </main>
        </div>
    </div>

    <!-- 加載指示器 -->
    <div class="loading d-none">
        <div class="spinner-border text-light" role="status"></div>
        <div>處理中，請稍候...</div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>
    <script>
        // 通用 AJAX 設置
        $.ajaxSetup({
            beforeSend: function() {
                $('.loading').removeClass('d-none');
            },
            complete: function() {
                $('.loading').addClass('d-none');
            },
            error: function(xhr, status, error) {
                $('.loading').addClass('d-none');
                alert('發生錯誤: ' + error);
            }
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>