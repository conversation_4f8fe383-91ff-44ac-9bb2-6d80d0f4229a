<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次預測 - 台股預測系統</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 使用與 index.html 相同的樣式 */
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        .feature-card {
            border-radius: 10px;
            transition: transform 0.3s, box-shadow 0.3s;
            margin-bottom: 20px;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            padding: 8px 20px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            transform: scale(1.05);
        }
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
            flex-direction: column;
        }
        .loading .spinner-border {
            width: 3rem;
            height: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>台股預測系統
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">資料下載</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/predict">股票預測</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/batch_predict">批次預測</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/news">新聞分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/topics">熱門主題</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/train">模型訓練</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <input class="form-control me-2" type="search" placeholder="股票代碼" id="quickSearchInput">
                    <button class="btn btn-outline-light" onclick="quickSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="text-center mb-4">批次股票漲跌預測</h1>
        
        <div class="card">
            <div class="card-body">
                <form id="batchPredictForm">
                    <!-- 股票輸入 -->
                    <div class="mb-3">
                        <label for="stockIds" class="form-label">股票代碼清單:</label>
                        <textarea class="form-control" id="stockIds" name="stock_ids" rows="3" placeholder="輸入多個股票代碼，以逗號分隔 (例如: 2330,2454,2317)" required></textarea>
                        <div class="form-text">每個股票代碼必須已經訓練過對應的模型，否則將無法預測</div>
                    </div>
                    
                    <!-- 預測天數 -->
                    <div class="mb-3">
                        <label for="predictionDays" class="form-label">預測未來幾天:</label>
                        <select class="form-select" id="predictionDays" name="prediction_days">
                            <option value="1" selected>1天</option>
                            <option value="3">3天</option>
                            <option value="5">5天</option>
                            <option value="10">10天</option>
                        </select>
                    </div>
                    
                    <!-- 排序選項 -->
                    <div class="mb-3">
                        <label for="sortBy" class="form-label">排序方式:</label>
                        <select class="form-select" id="sortBy" name="sort_by">
                            <option value="機率由高至低" selected>機率由高至低</option>
                            <option value="機率由低至高">機率由低至高</option>
                            <option value="股票代碼">股票代碼</option>
                        </select>
                    </div>
                    
                    <!-- 操作按鈕 -->
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="batchPredictBtn">
                            <i class="fas fa-layer-group me-2"></i> 開始批次預測
                        </button>
                        <button type="button" class="btn btn-secondary" id="exportResultBtn" disabled>
                            <i class="fas fa-download me-2"></i> 匯出結果
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 預測結果 -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-table me-2"></i> 預測結果
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="resultTable">
                        <thead>
                            <tr>
                                <th>股票代碼</th>
                                <th>預測日期</th>
                                <th>目標日期</th>
                                <th>預測天數</th>
                                <th>預測結果</th>
                                <th>預測機率</th>
                                <th>目前價格</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 預測結果將動態添加 -->
                        </tbody>
                    </table>
                </div>
                <p class="text-muted mt-3" id="emptyResult">尚無預測結果，請進行批次預測</p>
            </div>
        </div>
        
        <!-- 統計摘要 -->
        <div class="card mt-4" id="summaryCard" style="display: none;">
            <div class="card-header">
                <i class="fas fa-chart-pie me-2"></i> 預測統計摘要
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-body">
                                <h5 class="card-title">上漲預測</h5>
                                <p class="card-text">
                                    <span class="text-success fw-bold" id="upPredictionCount">0</span> 檔股票預測為上漲
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-body">
                                <h5 class="card-title">下跌預測</h5>
                                <p class="card-text">
                                    <span class="text-danger fw-bold" id="downPredictionCount">0</span> 檔股票預測為下跌
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">機率分布</h5>
                                <div>
                                    <canvas id="probabilityChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 載入指示器 -->
    <div class="loading d-none">
        <div class="spinner-border text-light" role="status"></div>
        <div>處理中，請稍候...</div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script>
        // 快速搜尋功能
        function quickSearch() {
            const searchInput = document.getElementById('quickSearchInput');
            const stockId = searchInput.value.trim();
            if (stockId) {
                window.location.href = `/predict?stock_id=${stockId}`;
            }
        }

        $(document).ready(function() {
            // 預測結果存儲
            var predictionResults = [];
            var probabilityChart = null;
            
            // 批次預測表單提交
            $('#batchPredictForm').submit(function(e) {
                e.preventDefault();
                
                var stockIds = $('#stockIds').val().trim();
                var predictionDays = $('#predictionDays').val();
                var sortBy = $('#sortBy').val();
                
                if (!stockIds) {
                    alert('請輸入股票代碼清單');
                    return;
                }
                
                // 更新UI
                $('#batchPredictBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> 預測中...');
                $('#resultTable tbody').empty();
                $('#emptyResult').text('正在進行預測...').show();
                $('#exportResultBtn').prop('disabled', true);
                $('#summaryCard').hide();
                
                // 顯示載入中
                $('.loading').removeClass('d-none');
                
                // 發送AJAX請求
                $.ajax({
                    url: '/api/batch_predict',
                    type: 'POST',
                    data: {
                        stock_ids: stockIds,
                        prediction_days: predictionDays,
                        sort_by: sortBy
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            predictionResults = response.results;
                            displayBatchResults(predictionResults);
                        } else {
                            alert('預測失敗: ' + response.message);
                            $('#emptyResult').text('預測失敗，請稍後再試').show();
                        }
                        $('#batchPredictBtn').prop('disabled', false).html('<i class="fas fa-layer-group me-2"></i> 開始批次預測');
                        
                        // 隱藏載入中
                        $('.loading').addClass('d-none');
                    },
                    error: function() {
                        alert('發生錯誤，請稍後再試');
                        $('#emptyResult').text('預測失敗，請稍後再試').show();
                        $('#batchPredictBtn').prop('disabled', false).html('<i class="fas fa-layer-group me-2"></i> 開始批次預測');
                        
                        // 隱藏載入中
                        $('.loading').addClass('d-none');
                    }
                });
            });
            
            // 顯示批次預測結果的函數保持不變
            function displayBatchResults(results) {
                var tableBody = $('#resultTable tbody');
                tableBody.empty();
                
                if (results.length > 0) {
                    $('#emptyResult').hide();
                    $('#exportResultBtn').prop('disabled', false);
                    
                    // 計算統計數據
                    var upCount = 0;
                    var downCount = 0;
                    var modelErrorCount = 0;
                    var probabilityData = {
                        upProbs: [],
                        downProbs: []
                    };
                    
                    results.forEach(function(result) {
                        var row = $('<tr>');
                        
                        // 股票代碼
                        row.append($('<td>').text(result.stock_id));
                        
                        // 預測日期
                        row.append($('<td>').text(result.prediction_date));
                        
                        // 目標日期
                        row.append($('<td>').text(result.target_date));
                        
                        // 預測天數
                        row.append($('<td>').text(result.prediction_days));
                        
                        // 預測結果
                        var predCell = $('<td>');
                        if (result.prediction === '模型不存在' || result.prediction === '預測失敗') {
                            predCell.text(result.prediction).addClass('text-muted');
                            modelErrorCount++;
                        } else if (result.prediction === 'up') {
                            predCell.html('<i class="fas fa-arrow-up text-success"></i> 上漲');
                            upCount++;
                            probabilityData.upProbs.push(result.probability);
                        } else {
                            predCell.html('<i class="fas fa-arrow-down text-danger"></i> 下跌');
                            downCount++;
                            probabilityData.downProbs.push(result.probability);
                        }
                        row.append(predCell);
                        
                        // 預測機率
                        var probCell = $('<td>');
                        if (typeof result.probability === 'number') {
                            probCell.text((result.probability * 100).toFixed(2) + '%');
                        } else {
                            probCell.text('-');
                        }
                        row.append(probCell);
                        
                        // 目前價格
                        row.append($('<td>').text(result.current_price));
                        
                        tableBody.append(row);
                    });
                    
                    // 更新統計摘要
                    updateStatsSummary(upCount, downCount, probabilityData);
                } else {
                    $('#emptyResult').text('無預測結果').show();
                }
            }
            
            // 更新統計摘要
            function updateStatsSummary(upCount, downCount, probabilityData) {
                $('#upPredictionCount').text(upCount);
                $('#downPredictionCount').text(downCount);
                $('#summaryCard').show();
                
                // 更新圖表
                updateProbabilityChart(probabilityData);
            }
            
            // 更新機率分布圖表
            function updateProbabilityChart(data) {
                // 準備圖表資料
                var upBins = createHistogramBins(data.upProbs);
                var downBins = createHistogramBins(data.downProbs);
                
                var chartData = {
                    labels: ['50-60%', '60-70%', '70-80%', '80-90%', '90-100%'],
                    datasets: [
                        {
                            label: '上漲預測',
                            backgroundColor: 'rgba(40, 167, 69, 0.6)',
                            borderColor: 'rgba(40, 167, 69, 1)',
                            borderWidth: 1,
                            data: upBins
                        },
                        {
                            label: '下跌預測',
                            backgroundColor: 'rgba(220, 53, 69, 0.6)',
                            borderColor: 'rgba(220, 53, 69, 1)',
                            borderWidth: 1,
                            data: downBins
                        }
                    ]
                };
                
                // 銷毀舊圖表
                if (probabilityChart !== null) {
                    probabilityChart.destroy();
                }
                
                // 創建圖表
                var ctx = document.getElementById('probabilityChart').getContext('2d');
                probabilityChart = new Chart(ctx, {
                    type: 'bar',
                    data: chartData,
                    options: {
                        responsive: true,
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: '預測機率區間'
                                }
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '股票數量'
                                },
                                ticks: {
                                    precision: 0
                                }
                            }
                        }
                    }
                });
            }
            
            // 創建直方圖數據
            function createHistogramBins(probArray) {
                // 將0-1的機率分為5個區間: [0.5-0.6, 0.6-0.7, 0.7-0.8, 0.8-0.9, 0.9-1.0]
                var bins = [0, 0, 0, 0, 0];
                
                probArray.forEach(function(prob) {
                    if (prob >= 0.9) {
                        bins[4]++;
                    } else if (prob >= 0.8) {
                        bins[3]++;
                    } else if (prob >= 0.7) {
                        bins[2]++;
                    } else if (prob >= 0.6) {
                        bins[1]++;
                    } else if (prob >= 0.5) {
                        bins[0]++;
                    }
                });
                
                return bins;
            }
            
            // 匯出結果按鈕點擊
            $('#exportResultBtn').click(function() {
                if (predictionResults.length === 0) {
                    alert('沒有可匯出的預測結果');
                    return;
                }
                
                // 顯示載入中
                $('.loading').removeClass('d-none');
                
                // 發送AJAX請求
                $.ajax({
                    url: '/api/download_batch_results',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        results: predictionResults
                    }),
                    success: function(response) {
                        // 隱藏載入中
                        $('.loading').addClass('d-none');
                        
                        if (response.status === 'success') {
                            // 創建隱藏的下載連結
                            var a = document.createElement('a');
                            a.href = '/download_file?path=' + encodeURIComponent(response.file_path) + '&name=' + encodeURIComponent(response.file_name);
                            a.download = response.file_name;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                        } else {
                            alert('匯出失敗: ' + response.message);
                        }
                    },
                    error: function() {
                        // 隱藏載入中
                        $('.loading').addClass('d-none');
                        
                        alert('發生錯誤，請稍後再試');
                    }
                });
            });
        });
    </script>
</body>
</html>