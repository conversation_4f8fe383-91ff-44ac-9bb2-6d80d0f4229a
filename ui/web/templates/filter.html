{% extends "base.html" %}

{% block title %}股票篩選 - 台股漲跌預測系統{% endblock %}

{% block content %}
<div class="p-4">
    <h1 class="mb-4"><i class="bi bi-funnel"></i> 股票條件篩選</h1>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <form id="filterForm">
                        <!-- 篩選模式 -->
                        <div class="mb-3">
                            <label for="filterMode" class="form-label">篩選模式:</label>
                            <select class="form-select" id="filterMode" name="filter_mode">
                                <option value="均線交叉" selected>均線交叉</option>
                                <option value="價格突破">價格突破</option>
                                <option value="全部條件">全部條件</option>
                            </select>
                        </div>
                        
                        <!-- 日期範圍 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="startDate" class="form-label">起始日期:</label>
                                <input type="date" class="form-control" id="startDate" name="start_date">
                            </div>
                            <div class="col-md-6">
                                <label for="endDate" class="form-label">結束日期:</label>
                                <input type="date" class="form-control" id="endDate" name="end_date">
                            </div>
                        </div>
                        
                        <!-- 均線交叉設置 -->
                        <div id="crossoverOptions">
                            <div class="card mb-3">
                                <div class="card-header bg-light">均線交叉設置</div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label for="maShort" class="form-label">短期均線:</label>
                                            <select class="form-select" id="maShort" name="ma_short">
                                                <option value="3">3日</option>
                                                <option value="5" selected>5日</option>
                                                <option value="8">8日</option>
                                                <option value="10">10日</option>
                                                <option value="15">15日</option>
                                                <option value="20">20日</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <label for="maLong" class="form-label">長期均線:</label>
                                            <select class="form-select" id="maLong" name="ma_long">
                                                <option value="10" selected>10日</option>
                                                <option value="15">15日</option>
                                                <option value="20">20日</option>
                                                <option value="30">30日</option>
                                                <option value="60">60日</option>
                                                <option value="120">120日</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="useMonthMa" name="use_month_ma" value="true" checked>
                                        <label class="form-check-label" for="useMonthMa">使用月線作為第三條均線</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 價格突破設置 -->
                        <div id="breakoutOptions" style="display: none;">
                            <div class="card mb-3">
                                <div class="card-header bg-light">價格突破設置</div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="breakoutDays" class="form-label">突破天數:</label>
                                        <select class="form-select" id="breakoutDays" name="breakout_days">
                                            <option value="20">20日</option>
                                            <option value="30">30日</option>
                                            <option value="60" selected>60日</option>
                                            <option value="90">90日</option>
                                            <option value="120">120日</option>
                                            <option value="180">180日</option>
                                            <option value="240">240日</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 通用設置 -->
                        <div class="card mb-3">
                            <div class="card-header bg-light">通用設置</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="volRatio" class="form-label">成交量放大倍數:</label>
                                    <select class="form-select" id="volRatio" name="vol_ratio">
                                        <option value="1.0">1.0倍</option>
                                        <option value="1.5" selected>1.5倍</option>
                                        <option value="2.0">2.0倍</option>
                                        <option value="2.5">2.5倍</option>
                                        <option value="3.0">3.0倍</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="recentDays" class="form-label">僅顯示最近天數:</label>
                                    <select class="form-select" id="recentDays" name="recent_days">
                                        <option value="3">3天</option>
                                        <option value="5">5天</option>
                                        <option value="7" selected>7天</option>
                                        <option value="10">10天</option>
                                        <option value="15">15天</option>
                                        <option value="30">30天</option>
                                        <option value="0">顯示全部</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按鈕 -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="filterBtn">
                                <i class="bi bi-funnel"></i> 執行篩選
                            </button>
                            <button type="button" class="btn btn-secondary" id="exportResultsBtn" disabled>
                                <i class="bi bi-download"></i> 匯出結果
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <!-- 篩選結果表格 -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-table"></i> 篩選結果
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="filterResultTable">
                            <thead>
                                <tr>
                                    <th>股票代碼</th>
                                    <th>公司名稱</th>
                                    <th>類型</th>
                                    <th>起始日期</th>
                                    <th>結束日期</th>
                                    <th>持續/突破幅度</th>
                                    <th>成交量放大</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 篩選結果將動態添加 -->
                            </tbody>
                        </table>
                    </div>
                    <p class="text-muted mt-3" id="emptyFilterResult">尚無篩選結果，請執行篩選</p>
                </div>
            </div>
            
            <!-- 股價圖表 -->
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-bar-chart-line"></i> 股價走勢圖
                </div>
                <div class="card-body">
                    <div id="chartContainer" class="text-center">
                        <p class="text-muted" id="chartPlaceholder">請從篩選結果中選擇股票以顯示圖表</p>
                        <img id="chartImage" class="img-fluid" style="display: none;">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 提示訊息 -->
    <div class="alert alert-info mt-4">
        <i class="bi bi-info-circle"></i> <strong>提示：</strong> 
        「均線交叉」篩選會尋找短期均線穿越長期均線的股票，常用於尋找買入時機。
        「價格突破」篩選會尋找突破指定天數前高點的股票，用於發現突破強勢股。
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 設置默認日期
    var today = new Date();
    var oneYearAgo = new Date();
    oneYearAgo.setFullYear(today.getFullYear() - 1);
    
    $('#endDate').val(today.toISOString().split('T')[0]);
    $('#startDate').val(oneYearAgo.toISOString().split('T')[0]);
    
    // 篩選模式切換
    $('#filterMode').change(function() {
        var mode = $(this).val();
        
        if (mode === '均線交叉') {
            $('#crossoverOptions').show();
            $('#breakoutOptions').hide();
        } else if (mode === '價格突破') {
            $('#crossoverOptions').hide();
            $('#breakoutOptions').show();
        } else {  // 全部條件
            $('#crossoverOptions').show();
            $('#breakoutOptions').show();
        }
    });
    
    // 篩選結果存儲
    var filterResults = [];
    
    // 篩選表單提交
    $('#filterForm').submit(function(e) {
        e.preventDefault();
        
        // 更新UI
        $('#filterBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> 篩選中...');
        $('#filterResultTable tbody').empty();
        $('#emptyFilterResult').text('正在執行篩選...').show();
        $('#exportResultsBtn').prop('disabled', true);
        $('#chartPlaceholder').show();
        $('#chartImage').hide();
        
        // 獲取表單數據
        var formData = $(this).serialize();
        
        // 發送AJAX請求
        $.ajax({
            url: '/api/filter',
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.status === 'success') {
                    filterResults = response.results;
                    displayFilterResults(filterResults);
                } else {
                    alert('篩選失敗: ' + response.message);
                    $('#emptyFilterResult').text('篩選失敗，請稍後再試').show();
                }
                $('#filterBtn').prop('disabled', false).html('<i class="bi bi-funnel"></i> 執行篩選');
            },
            error: function() {
                alert('發生錯誤，請稍後再試');
                $('#emptyFilterResult').text('篩選失敗，請稍後再試').show();
                $('#filterBtn').prop('disabled', false).html('<i class="bi bi-funnel"></i> 執行篩選');
            }
        });
    });
    
    // 顯示篩選結果
    function displayFilterResults(results) {
        var tableBody = $('#filterResultTable tbody');
        tableBody.empty();
        
        if (results.length > 0) {
            $('#emptyFilterResult').hide();
            $('#exportResultsBtn').prop('disabled', false);
            
            results.forEach(function(result) {
                var row = $('<tr style="cursor: pointer;">').data('stock-id', result.stock_id);
                
                // 股票代碼
                row.append($('<td>').text(result.stock_id));
                
                // 公司名稱
                row.append($('<td>').text(result.company_name));
                
                // 類型
                row.append($('<td>').text(result.type));
                
                // 起始日期
                row.append($('<td>').text(result.start_date));
                
                // 結束日期
                row.append($('<td>').text(result.end_date));
                
                // 持續/突破幅度
                row.append($('<td>').text(result.length));
                
                // 成交量放大
                row.append($('<td>').text(result.vol_surge));
                
                tableBody.append(row);
            });
            
            // 添加行點擊事件
            $('#filterResultTable tbody tr').click(function() {
                var stockId = $(this).data('stock-id');
                loadStockChart(stockId);
                $(this).addClass('table-primary').siblings().removeClass('table-primary');
            });
        } else {
            $('#emptyFilterResult').text('沒有找到符合條件的股票').show();
        }
    }
    
    // 載入股價圖表
    function loadStockChart(stockId) {
        // 更新UI
        $('#chartPlaceholder').text('正在載入股價圖表...').show();
        $('#chartImage').hide();
        
        // 發送AJAX請求
        $.ajax({
            url: '/api/stock_chart/' + stockId,
            type: 'GET',
            success: function(response) {
                if (response.status === 'success') {
                    $('#chartPlaceholder').hide();
                    $('#chartImage').attr('src', response.chart).show();
                } else {
                    $('#chartPlaceholder').text('載入圖表失敗: ' + response.message).show();
                    $('#chartImage').hide();
                }
            },
            error: function() {
                $('#chartPlaceholder').text('載入圖表時發生錯誤，請稍後再試').show();
                $('#chartImage').hide();
            }
        });
    }
    
    // 匯出結果按鈕點擊
    $('#exportResultsBtn').click(function() {
        if (filterResults.length === 0) {
            alert('沒有可匯出的篩選結果');
            return;
        }
        
        // 發送AJAX請求
        $.ajax({
            url: '/api/download_filter_results',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                results: filterResults
            }),
            success: function(response) {
                if (response.status === 'success') {
                    // 創建隱藏的下載連結
                    var a = document.createElement('a');
                    a.href = '/download_file?path=' + encodeURIComponent(response.file_path) + '&name=' + encodeURIComponent(response.file_name);
                    a.download = response.file_name;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                } else {
                    alert('匯出失敗: ' + response.message);
                }
            },
            error: function() {
                alert('發生錯誤，請稍後再試');
            }
        });
    });
});
</script>
{% endblock %}