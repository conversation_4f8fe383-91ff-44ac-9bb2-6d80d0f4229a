{% extends "base.html" %}

{% block title %}股票預測 - 台股漲跌預測系統{% endblock %}

{% block content %}
<div class="p-4">
    <h1 class="mb-4"><i class="bi bi-graph-up"></i> 股票漲跌預測</h1>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <form id="predictForm">
                        <!-- 股票選擇 -->
                        <div class="mb-3">
                            <label for="stockId" class="form-label">股票代碼:</label>
                            <input type="text" class="form-control" id="stockId" name="stock_id" placeholder="例如: 2330" required>
                            <div class="form-text">輸入要預測的股票代碼</div>
                        </div>
                        
                        <!-- 預測天數 -->
                        <div class="mb-3">
                            <label for="predictionDays" class="form-label">預測未來幾天:</label>
                            <select class="form-select" id="predictionDays" name="prediction_days">
                                <option value="1" selected>1天</option>
                                <option value="3">3天</option>
                                <option value="5">5天</option>
                                <option value="10">10天</option>
                            </select>
                        </div>
                        
                        <!-- 操作按鈕 -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="predictBtn">
                                <i class="bi bi-graph-up"></i> 進行預測
                            </button>
                        </div>
                    </form>
                    
                    <!-- 預測結果 -->
                    <div class="mt-4" id="resultSection" style="display: none;">
                        <h5 class="card-title">預測結果</h5>
                        <div class="alert" id="resultAlert">
                            <div id="resultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 提示訊息 -->
            <div class="alert alert-info mt-3">
                <i class="bi bi-info-circle"></i> <strong>提示：</strong> 請確保已針對該股票訓練了模型，否則無法進行預測。
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-bar-chart-line"></i> 股價走勢與預測圖
                </div>
                <div class="card-body">
                    <div id="chartContainer" class="text-center">
                        <p class="text-muted" id="chartPlaceholder">請選擇股票並進行預測以顯示圖表</p>
                        <img id="chartImage" class="img-fluid" style="display: none;">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 歷史預測記錄 -->
    <div class="card mt-4">
        <div class="card-header">
            <i class="bi bi-clock-history"></i> 預測歷史記錄
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="historyTable">
                    <thead>
                        <tr>
                            <th>股票代碼</th>
                            <th>預測日期</th>
                            <th>目標日期</th>
                            <th>預測結果</th>
                            <th>預測機率</th>
                            <th>當時股價</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 歷史記錄將動態添加 -->
                    </tbody>
                </table>
            </div>
            <p class="text-muted" id="emptyHistory">尚無預測記錄</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 初始化本地存儲的預測歷史
    var predictionHistory = JSON.parse(localStorage.getItem('predictionHistory') || '[]');
    updateHistoryTable();
    
    // 預測表單提交
    $('#predictForm').submit(function(e) {
        e.preventDefault();
        
        var stockId = $('#stockId').val();
        var predictionDays = $('#predictionDays').val();
        
        if (!stockId) {
            alert('請輸入股票代碼');
            return;
        }
        
        // 更新UI
        $('#predictBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> 預測中...');
        $('#resultSection').hide();
        $('#chartPlaceholder').show();
        $('#chartImage').hide();
        
        // 發送AJAX請求
        $.ajax({
            url: '/api/predict',
            type: 'POST',
            data: {
                stock_id: stockId,
                prediction_days: predictionDays
            },
            success: function(response) {
                if (response.status === 'success') {
                    displayPredictionResult(response.prediction, response.chart);
                    addToHistory(response.prediction);
                } else {
                    showError(response.message);
                }
                $('#predictBtn').prop('disabled', false).html('<i class="bi bi-graph-up"></i> 進行預測');
            },
            error: function() {
                showError('發生錯誤，請稍後再試');
                $('#predictBtn').prop('disabled', false).html('<i class="bi bi-graph-up"></i> 進行預測');
            }
        });
    });
    
    // 顯示預測結果
    function displayPredictionResult(prediction, chartData) {
        $('#resultSection').show();
        
        var resultContent = '';
        var alertClass = '';
        
        if (prediction.prediction === 'up') {
            alertClass = 'alert-success';
            resultContent += '<i class="bi bi-arrow-up-circle"></i> <strong>預測結果: 上漲</strong><br>';
        } else {
            alertClass = 'alert-danger';
            resultContent += '<i class="bi bi-arrow-down-circle"></i> <strong>預測結果: 下跌</strong><br>';
        }
        
        $('#resultAlert').removeClass('alert-success alert-danger alert-warning').addClass(alertClass);
        
        resultContent += '<hr>';
        resultContent += '股票代碼: ' + prediction.stock_id + '<br>';
        resultContent += '現在股價: ' + prediction.current_price + '<br>';
        resultContent += '預測日期: ' + prediction.prediction_date + '<br>';
        resultContent += '目標日期: ' + prediction.target_date + '<br>';
        resultContent += '預測機率: ' + (prediction.probability * 100).toFixed(2) + '%';
        
        $('#resultContent').html(resultContent);
        
        // 顯示圖表
        if (chartData) {
            $('#chartPlaceholder').hide();
            $('#chartImage').attr('src', chartData).show();
        }
    }
    
    // 顯示錯誤訊息
    function showError(message) {
        $('#resultSection').show();
        $('#resultAlert').removeClass('alert-success alert-danger').addClass('alert-warning');
        $('#resultContent').html('<i class="bi bi-exclamation-triangle"></i> <strong>錯誤:</strong> ' + message);
    }
    
    // 添加到預測歷史
    function addToHistory(prediction) {
        // 限制歷史記錄最多10筆
        if (predictionHistory.length >= 10) {
            predictionHistory.pop();
        }
        
        // 添加新記錄到開頭
        predictionHistory.unshift({
            stock_id: prediction.stock_id,
            prediction_date: prediction.prediction_date,
            target_date: prediction.target_date,
            prediction: prediction.prediction,
            probability: prediction.probability,
            current_price: prediction.current_price,
            timestamp: new Date().toISOString()
        });
        
        // 保存到本地存儲
        localStorage.setItem('predictionHistory', JSON.stringify(predictionHistory));
        
        // 更新表格
        updateHistoryTable();
    }
    
    // 更新歷史記錄表格
    function updateHistoryTable() {
        var tableBody = $('#historyTable tbody');
        tableBody.empty();
        
        if (predictionHistory.length > 0) {
            $('#emptyHistory').hide();
            
            predictionHistory.forEach(function(record) {
                var row = $('<tr>');
                
                row.append($('<td>').text(record.stock_id));
                row.append($('<td>').text(record.prediction_date));
                row.append($('<td>').text(record.target_date));
                
                var predCell = $('<td>');
                if (record.prediction === 'up') {
                    predCell.html('<span class="text-success"><i class="bi bi-arrow-up-circle"></i> 上漲</span>');
                } else {
                    predCell.html('<span class="text-danger"><i class="bi bi-arrow-down-circle"></i> 下跌</span>');
                }
                row.append(predCell);
                
                row.append($('<td>').text((record.probability * 100).toFixed(2) + '%'));
                row.append($('<td>').text(record.current_price));
                
                tableBody.append(row);
            });
        } else {
            $('#emptyHistory').show();
        }
    }
});
</script>
{% endblock %}