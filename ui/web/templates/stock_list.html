<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <title>股票清單</title>
    <!-- 引入 Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入 DataTables -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <h2 class="mb-4">股票清單</h2>
        
        <!-- 篩選器 -->
        <div class="row mb-3">
            <div class="col-md-3">
                <select id="industryFilter" class="form-select">
                    <option value="">全部產業</option>
                    <!-- 動態生成產業選項 -->
                </select>
            </div>
            <div class="col-md-3">
                <input type="text" id="stockSearch" class="form-control" placeholder="搜尋股票代碼或名稱">
            </div>
        </div>

        <!-- 股票表格 -->
        <table id="stockTable" class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>代碼</th>
                    <th>名稱</th>
                    <th>現價</th>
                    <th>漲跌</th>
                    <th>漲跌幅(%)</th>
                    <th>成交量</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 股票資料將動態插入 -->
            </tbody>
        </table>
    </div>

    <!-- JavaScript 依賴 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
    $(document).ready(function() {
        // 初始化 DataTable
        var table = $('#stockTable').DataTable({
            ajax: {
                url: '/api/get_stock_list',
                dataSrc: ''
            },
            columns: [
                { data: 'code' },
                { data: 'name' },
                { data: 'current_price' },
                { data: 'price_change' },
                { data: 'price_change_percent' },
                { data: 'volume' },
                { 
                    data: null,
                    render: function(data, type, row) {
                        return `<button class="btn btn-sm btn-info" onclick="viewStockDetail('${row.code}')">詳細</button>`;
                    }
                }
            ],
            language: {
                // 中文本地化
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/zh-HANT.json'
            }
        });

        // 股票搜尋功能
        $('#stockSearch').on('keyup', function() {
            table.search(this.value).draw();
        });

        // 產業篩選
        $('#industryFilter').on('change', function() {
            table.column(1).search(this.value).draw();
        });
    });

    // 查看股票詳細資訊
    function viewStockDetail(stockCode) {
        window.location.href = `/stock_detail/${stockCode}`;
    }
    </script>
</body>
</html>