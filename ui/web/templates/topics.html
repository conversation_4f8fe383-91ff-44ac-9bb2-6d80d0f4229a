{% extends "base.html" %}

{% block title %}熱門主題 - 台股漲跌預測系統{% endblock %}

{% block content %}
<div class="p-4">
    <h1 class="mb-4"><i class="bi bi-chat-square-text"></i> 新聞熱門主題</h1>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-list-stars"></i> 本週熱門主題
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="topicsTable">
                            <thead>
                                <tr>
                                    <th>主題</th>
                                    <th>新聞數量</th>
                                    <th>情緒指數</th>
                                </tr>
                            </thead>
                            <tbody id="topicsTableBody">
                                <!-- 熱門主題將動態添加 -->
                            </tbody>
                        </table>
                    </div>
                    <div id="loadingTopics" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">載入中...</span>
                        </div>
                        <p>載入熱門主題中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 載入熱門主題
    loadTopics();
    
    function loadTopics() {
        $.ajax({
            url: '/api/news/topics',
            type: 'GET',
            data: {
                days: 7,
                limit: 10
            },
            success: function(response) {
                if (response.status === 'success') {
                    displayTopics(response.topics);
                } else {
                    showError('無法載入熱門主題：' + response.message);
                }
            },
            error: function() {
                showError('載入熱門主題時發生錯誤');
            }
        });
    }
    
    function displayTopics(topics) {
        $('#loadingTopics').hide();
        var tableBody = $('#topicsTableBody');
        tableBody.empty();
        
        if (topics && topics.length > 0) {
            topics.forEach(function(topic) {
                var row = $('<tr>');
                
                // 主題名稱
                row.append($('<td>').text(topic.topic));
                
                // 新聞數量
                row.append($('<td>').text(topic.news_count));
                
                // 情緒指數
                var sentimentCell = $('<td>');
                var sentimentValue = topic.avg_sentiment;
                var sentimentText = '';
                var sentimentClass = '';
                
                if (sentimentValue > 0.2) {
                    sentimentText = '正面 (+' + sentimentValue.toFixed(2) + ')';
                    sentimentClass = 'text-success';
                } else if (sentimentValue < -0.2) {
                    sentimentText = '負面 (' + sentimentValue.toFixed(2) + ')';
                    sentimentClass = 'text-danger';
                } else {
                    sentimentText = '中性 (' + sentimentValue.toFixed(2) + ')';
                    sentimentClass = 'text-secondary';
                }
                
                sentimentCell.addClass(sentimentClass).text(sentimentText);
                row.append(sentimentCell);
                
                tableBody.append(row);
            });
        } else {
            tableBody.append('<tr><td colspan="3" class="text-center">暫無熱門主題</td></tr>');
        }
    }
    
    function showError(message) {
        $('#loadingTopics').hide();
        $('#topicsTableBody').html('<tr><td colspan="3" class="text-center text-danger">' + message + '</td></tr>');
    }
});
</script>
{% endblock %}