{% extends "base.html" %}

{% block title %}模型訓練 - 台股漲跌預測系統{% endblock %}

{% block content %}
<div class="p-4">
    <h1 class="mb-4"><i class="bi bi-gear"></i> 股票漲跌預測模型訓練</h1>
    
    <div class="card">
        <div class="card-body">
            <form id="trainForm">
                <!-- 股票選擇 -->
                <div class="mb-3">
                    <label for="stockId" class="form-label">股票代碼:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="stockId" name="stock_id" placeholder="例如: 2330" required>
                        <button class="btn btn-outline-secondary" type="button" id="checkModelBtn">
                            <i class="bi bi-search"></i> 檢查模型
                        </button>
                    </div>
                    <div class="form-text">輸入要訓練模型的股票代碼</div>
                </div>
                
                <!-- 日期範圍 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="startDate" class="form-label">起始日期:</label>
                        <input type="date" class="form-control" id="startDate" name="start_date">
                    </div>
                    <div class="col-md-6">
                        <label for="endDate" class="form-label">結束日期:</label>
                        <input type="date" class="form-control" id="endDate" name="end_date">
                    </div>
                </div>
                
                <!-- 預測天數 -->
                <div class="mb-3">
                    <label for="predictionDays" class="form-label">預測未來幾天:</label>
                    <select class="form-select" id="predictionDays" name="prediction_days">
                        <option value="1" selected>1天</option>
                        <option value="3">3天</option>
                        <option value="5">5天</option>
                        <option value="10">10天</option>
                    </select>
                </div>
                
                <!-- 操作按鈕 -->
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary" id="trainBtn">
                        <i class="bi bi-gear"></i> 開始訓練模型
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 訓練結果 -->
    <div class="card mt-4">
        <div class="card-header">
            <i class="bi bi-clipboard-data"></i> 訓練結果
        </div>
        <div class="card-body">
            <div id="modelStatus" class="alert alert-info d-none">
                <i class="bi bi-info-circle"></i> <span id="modelStatusText"></span>
            </div>
            <div id="resultContainer" class="bg-light p-3" style="min-height: 300px; border-radius: 5px; font-family: monospace;">
                <div id="trainResult">尚未開始訓練...</div>
            </div>
        </div>
    </div>
    
    <!-- 提示訊息 -->
    <div class="alert alert-info mt-4">
        <i class="bi bi-info-circle"></i> <strong>提示：</strong> 模型訓練可能需要幾分鐘的時間，取決於資料量和系統負載。訓練完成後，模型將被保存以供預測使用。
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 設置默認日期
    var today = new Date();
    var threeYearsAgo = new Date();
    threeYearsAgo.setFullYear(today.getFullYear() - 20);
    
    $('#endDate').val(today.toISOString().split('T')[0]);
    $('#startDate').val(threeYearsAgo.toISOString().split('T')[0]);
    
    // 檢查模型按鈕
    $('#checkModelBtn').click(function() {
        var stockId = $('#stockId').val();
        if (!stockId) {
            alert('請輸入股票代碼');
            return;
        }
        
        checkModelStatus(stockId);
    });
    
    // 檢查模型狀態
    function checkModelStatus(stockId) {
        $('#modelStatus').removeClass('d-none alert-success alert-warning').addClass('alert-info');
        $('#modelStatusText').html('<span class="spinner-border spinner-border-sm"></span> 正在檢查模型狀態...');
        
        $.ajax({
            url: '/api/train/status/' + stockId,
            type: 'GET',
            success: function(response) {
                if (response.status === 'success') {
                    if (response.trained) {
                        $('#modelStatus').removeClass('alert-info').addClass('alert-success');
                        $('#modelStatusText').html('該股票的模型已存在，您可以直接用於預測或重新訓練以更新模型。');
                    } else {
                        $('#modelStatus').removeClass('alert-info').addClass('alert-warning');
                        $('#modelStatusText').html('找不到該股票的模型，請先訓練模型。');
                    }
                } else {
                    $('#modelStatus').removeClass('alert-info').addClass('alert-danger');
                    $('#modelStatusText').html('檢查模型狀態時發生錯誤: ' + response.message);
                }
            },
            error: function() {
                $('#modelStatus').removeClass('alert-info').addClass('alert-danger');
                $('#modelStatusText').html('檢查模型狀態時發生錯誤，請稍後再試');
            }
        });
    }
    
    // 訓練表單提交
    $('#trainForm').submit(function(e) {
        e.preventDefault();
        
        var stockId = $('#stockId').val();
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        var predictionDays = $('#predictionDays').val();
        
        if (!stockId) {
            alert('請輸入股票代碼');
            return;
        }
        
        if (confirm('確定要開始訓練 ' + stockId + ' 的模型嗎？訓練可能需要幾分鐘。')) {
            // 更新UI
            $('#trainBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> 訓練中...');
            $('#trainResult').html('開始訓練模型...');
            
            // 發送AJAX請求
            $.ajax({
                url: '/api/train',
                type: 'POST',
                data: {
                    stock_id: stockId,
                    start_date: startDate,
                    end_date: endDate,
                    prediction_days: predictionDays
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#trainResult').html('訓練任務已啟動，請等待訓練完成...');
                        startTrainingCheck(stockId);
                    } else {
                        $('#trainResult').html('錯誤: ' + response.message);
                        $('#trainBtn').prop('disabled', false).html('<i class="bi bi-gear"></i> 開始訓練模型');
                    }
                },
                error: function() {
                    $('#trainResult').html('發生錯誤，請稍後再試');
                    $('#trainBtn').prop('disabled', false).html('<i class="bi bi-gear"></i> 開始訓練模型');
                }
            });
        }
    });
    
    // 檢查訓練進度
    function startTrainingCheck(stockId) {
        var checkCount = 0;
        var trainedFlag = false;
        
        var checkInterval = setInterval(function() {
            checkCount++;
            
            $.ajax({
                url: '/api/train/status/' + stockId,
                type: 'GET',
                success: function(response) {
                    if (response.status === 'success' && response.trained) {
                        clearInterval(checkInterval);
                        trainedFlag = true;
                        $('#trainResult').html(
                            '模型訓練完成！<br><br>' +
                            '股票: ' + stockId + '<br>' +
                            '預測天數: ' + $('#predictionDays').val() + '天<br><br>' +
                            '現在您可以前往「股票預測」或「批次預測」頁面使用此模型進行預測。'
                        );
                        $('#trainBtn').prop('disabled', false).html('<i class="bi bi-gear"></i> 開始訓練模型');
                        
                        // 更新模型狀態
                        $('#modelStatus').removeClass('d-none alert-info alert-warning').addClass('alert-success');
                        $('#modelStatusText').html('模型訓練成功！您現在可以使用此模型進行預測。');
                    }
                }
            });
            
            // 模擬訓練進度更新
            if (!trainedFlag) {
                if (checkCount <= 10) {
                    updateTrainingProgress(checkCount * 10);
                }
                
                // 最多檢查20次 (約2分鐘)
                if (checkCount >= 20 && !trainedFlag) {
                    clearInterval(checkInterval);
                    $('#trainResult').append('<br><br>訓練時間較長，您可以稍後回來查看結果，或前往預測頁面檢查模型是否可用。');
                    $('#trainBtn').prop('disabled', false).html('<i class="bi bi-gear"></i> 開始訓練模型');
                }
            }
        }, 6000); // 每6秒檢查一次
    }
    
    // 更新訓練進度顯示
    function updateTrainingProgress(percent) {
        var progressText = '';
        
        if (percent <= 10) {
            progressText = '正在準備資料...';
        } else if (percent <= 30) {
            progressText = '正在載入歷史價格和技術指標...';
        } else if (percent <= 50) {
            progressText = '正在處理特徵資料...';
        } else if (percent <= 70) {
            progressText = '正在訓練隨機森林模型...';
        } else if (percent <= 90) {
            progressText = '正在進行參數優化...';
        } else {
            progressText = '正在評估模型性能...';
        }
        
        $('#trainResult').html(
            '訓練進度: ' + percent + '%<br>' +
            progressText + '<br>' +
            '<div class="progress mt-2">' +
            '  <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: ' + percent + '%"></div>' +
            '</div>'
        );
    }
});
</script>
{% endblock %}