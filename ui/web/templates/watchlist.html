{% extends "base.html" %}

{% block title %}觀察清單 - 台股漲跌預測系統{% endblock %}

{% block content %}
<div class="watchlist-header">
    <h5 class="watchlist-title">觀察清單</h5>
    <span class="watchlist-date">資料日期：{{ watchlist.update_date if watchlist else '未更新' }}</span>
</div>
<div class="p-4">
    <h1 class="mb-4"><i class="bi bi-star"></i> 股票觀察清單</h1>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-plus-circle"></i> 添加觀察清單
                </div>
                <div class="card-body">
                    <form id="newWatchlistForm" class="mb-3">
                        <div class="mb-3">
                            <label for="watchlistName" class="form-label">清單名稱</label>
                            <input type="text" class="form-control" id="watchlistName" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">創建新清單</button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="list-group" id="watchlistsList">
                        <!-- 觀察清單將在這裡動態顯示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-list-ul"></i> <span id="currentWatchlistName">選擇或創建一個觀察清單</span>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="watchlistActionsBtn" data-bs-toggle="dropdown" aria-expanded="false">
                            操作
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="watchlistActionsBtn">
                            <li><a class="dropdown-item" href="#" id="refreshWatchlistBtn"><i class="bi bi-arrow-clockwise"></i> 更新數據</a></li>
                            <li><a class="dropdown-item" href="#" id="batchPredictBtn"><i class="bi bi-graph-up"></i> 批次預測</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="renameWatchlistBtn"><i class="bi bi-pencil"></i> 重新命名</a></li>
                            <li><a class="dropdown-item text-danger" href="#" id="deleteWatchlistBtn"><i class="bi bi-trash"></i> 刪除清單</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3 row align-items-end" id="stockAddForm" style="display: none;">
                        <div class="col-md-5">
                            <label for="stockId" class="form-label">股票代碼</label>
                            <input type="text" class="form-control" id="stockId" placeholder="例如: 2330">
                        </div>
                        <div class="col-md-5">
                            <label for="stockNote" class="form-label">備註</label>
                            <input type="text" class="form-control" id="stockNote" placeholder="(選填)">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" id="addStockBtn">添加</button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="watchlistTable">
                            <thead>
                                <tr>
                                    <th>代碼</th>
                                    <th>名稱</th>
                                    <th>現價</th>
                                    <th>漲跌</th>
                                    <th>成交量</th>
                                    <th>預測</th>
                                    <th>備註</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 觀察清單的股票將在這裡動態顯示 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div id="emptyWatchlistMsg" class="text-center p-5">
                        <i class="bi bi-info-circle text-muted fs-1"></i>
                        <p class="mt-3">請先選擇或創建一個觀察清單，然後添加股票</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 儲存所有觀察清單的變數
    let watchlists = JSON.parse(localStorage.getItem('watchlists') || '[]');
    let currentWatchlistId = localStorage.getItem('currentWatchlistId');
    
    // 初始化頁面
    loadWatchlists();
    if (currentWatchlistId) {
        loadWatchlistContent(currentWatchlistId);
    }
    
    // 創建新的觀察清單
    $('#newWatchlistForm').submit(function(e) {
        e.preventDefault();
        
        const watchlistName = $('#watchlistName').val().trim();
        if (!watchlistName) return;
        
        const newWatchlist = {
            id: Date.now().toString(),
            name: watchlistName,
            stocks: []
        };
        
        watchlists.push(newWatchlist);
        saveWatchlists();
        
        // 更新UI
        $('#watchlistName').val('');
        loadWatchlists();
        selectWatchlist(newWatchlist.id);
    });
    
    // 選擇觀察清單
    $(document).on('click', '.watchlist-item', function() {
        const watchlistId = $(this).data('id');
        selectWatchlist(watchlistId);
    });
    
    // 添加股票到當前觀察清單
    $('#addStockBtn').click(function() {
        if (!currentWatchlistId) return;
        
        const stockId = $('#stockId').val().trim();
        const stockNote = $('#stockNote').val().trim();
        
        if (!stockId) return;
        
        // 檢查股票是否已存在於清單中
        const watchlist = watchlists.find(wl => wl.id === currentWatchlistId);
        if (watchlist.stocks.some(s => s.id === stockId)) {
            alert('該股票已在觀察清單中');
            return;
        }
        
        // 獲取股票資料 (這裡使用模擬數據，實際應用中應通過API獲取)
        getStockInfo(stockId).then(stockInfo => {
            watchlist.stocks.push({
                id: stockId,
                name: stockInfo.name || `${stockId}`,
                note: stockNote,
                addedAt: new Date().toISOString()
            });
            
            saveWatchlists();
            loadWatchlistContent(currentWatchlistId);
            
            // 清空輸入框
            $('#stockId').val('');
            $('#stockNote').val('');
        });
    });
    
    // 從觀察清單中移除股票
    $(document).on('click', '.remove-stock-btn', function() {
        if (!currentWatchlistId) return;
        
        const stockId = $(this).data('id');
        const watchlist = watchlists.find(wl => wl.id === currentWatchlistId);
        
        watchlist.stocks = watchlist.stocks.filter(s => s.id !== stockId);
        saveWatchlists();
        loadWatchlistContent(currentWatchlistId);
    });
    
    // 刪除整個觀察清單
    $('#deleteWatchlistBtn').click(function() {
        if (!currentWatchlistId) return;
        
        if (confirm('確定要刪除此觀察清單嗎？此操作無法撤銷。')) {
            watchlists = watchlists.filter(wl => wl.id !== currentWatchlistId);
            saveWatchlists();
            
            currentWatchlistId = watchlists.length > 0 ? watchlists[0].id : null;
            localStorage.setItem('currentWatchlistId', currentWatchlistId);
            
            loadWatchlists();
            if (currentWatchlistId) {
                loadWatchlistContent(currentWatchlistId);
            } else {
                showEmptyState();
            }
        }
    });
    
    // 重命名觀察清單
    $('#renameWatchlistBtn').click(function() {
        if (!currentWatchlistId) return;
        
        const watchlist = watchlists.find(wl => wl.id === currentWatchlistId);
        const newName = prompt('請輸入新名稱', watchlist.name);
        
        if (newName && newName.trim()) {
            watchlist.name = newName.trim();
            saveWatchlists();
            loadWatchlists();
            $('#currentWatchlistName').text(watchlist.name);
        }
    });
    
    // 刷新觀察清單數據
    $('#refreshWatchlistBtn').click(function() {
        if (currentWatchlistId) {
            loadWatchlistContent(currentWatchlistId, true);
        }
    });
    
    // 批次預測
    $('#batchPredictBtn').click(function() {
        if (!currentWatchlistId) return;
        
        const watchlist = watchlists.find(wl => wl.id === currentWatchlistId);
        if (watchlist.stocks.length === 0) {
            alert('觀察清單中沒有股票，無法進行批次預測');
            return;
        }
        
        const stockIds = watchlist.stocks.map(s => s.id).join(',');
        window.location.href = `/batch_predict?stocks=${stockIds}`;
    });
    
    // 載入觀察清單
    function loadWatchlists() {
        const $list = $('#watchlistsList');
        $list.empty();
        
        if (watchlists.length === 0) {
            $list.html('<div class="alert alert-info">尚未創建觀察清單</div>');
            return;
        }
        
        watchlists.forEach(watchlist => {
            const $item = $(`
                <a href="#" class="list-group-item list-group-item-action watchlist-item d-flex justify-content-between align-items-center" data-id="${watchlist.id}">
                    ${watchlist.name}
                    <span class="badge bg-primary rounded-pill">${watchlist.stocks.length}</span>
                </a>
            `);
            
            if (watchlist.id === currentWatchlistId) {
                $item.addClass('active');
            }
            
            $list.append($item);
        });
    }
    
    // 選擇觀察清單
    function selectWatchlist(watchlistId) {
        currentWatchlistId = watchlistId;
        localStorage.setItem('currentWatchlistId', currentWatchlistId);
        
        // 更新UI
        $('.watchlist-item').removeClass('active');
        $(`.watchlist-item[data-id="${watchlistId}"]`).addClass('active');
        
        loadWatchlistContent(watchlistId);
    }
    
    // 載入觀察清單內容
    function loadWatchlistContent(watchlistId, refresh = false) {
        const watchlist = watchlists.find(wl => wl.id === watchlistId);
        if (!watchlist) return;
        
        // 更新UI
        $('#currentWatchlistName').text(watchlist.name);
        $('#stockAddForm').show();
        $('#emptyWatchlistMsg').hide();
        
        const $tbody = $('#watchlistTable tbody');
        $tbody.empty();
        
        if (watchlist.stocks.length === 0) {
            $tbody.html(`<tr><td colspan="8" class="text-center">此觀察清單還沒有股票</td></tr>`);
            return;
        }
        
        // 顯示載入中
        watchlist.stocks.forEach(stock => {
            $tbody.append(`
                <tr id="stock-row-${stock.id}">
                    <td>${stock.id}</td>
                    <td>${stock.name}</td>
                    <td colspan="4" class="text-center">載入中...</td>
                    <td>${stock.note || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-danger remove-stock-btn" data-id="${stock.id}">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            `);
        });
        
        // 獲取股票資料
        watchlist.stocks.forEach(stock => {
            getStockInfo(stock.id, refresh).then(stockInfo => {
                updateStockRow(stock.id, stockInfo, stock.note);
            });
        });
    }
    
    // 更新股票資料行
    function updateStockRow(stockId, stockInfo, note) {
        const $row = $(`#stock-row-${stockId}`);
        if (!$row.length) return;
        
        const priceChangeClass = stockInfo.priceChange > 0 ? 'text-success' : 
                              stockInfo.priceChange < 0 ? 'text-danger' : '';
        
        const predictionBadge = stockInfo.prediction === 'up' ? 
                            '<span class="badge bg-success">看多</span>' : 
                            '<span class="badge bg-danger">看空</span>';
        
        $row.html(`
            <td>${stockId}</td>
            <td>${stockInfo.name}</td>
            <td>${stockInfo.price.toFixed(2)}</td>
            <td class="${priceChangeClass}">${stockInfo.priceChange > 0 ? '+' : ''}${stockInfo.priceChange.toFixed(2)} (${stockInfo.priceChangePercent.toFixed(2)}%)</td>
            <td>${formatNumber(stockInfo.volume)}</td>
            <td>${predictionBadge}</td>
            <td>${note || '-'}</td>
            <td>
                <button class="btn btn-sm btn-danger remove-stock-btn" data-id="${stockId}">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `);
    }
    
    // 保存觀察清單到本地存儲
    function saveWatchlists() {
        localStorage.setItem('watchlists', JSON.stringify(watchlists));
    }
    
    // 顯示空狀態
    function showEmptyState() {
        $('#currentWatchlistName').text('選擇或創建一個觀察清單');
        $('#stockAddForm').hide();
        $('#emptyWatchlistMsg').show();
        $('#watchlistTable tbody').empty();
    }
    
    // 獲取股票資訊 (模擬數據，實際應用應通過API獲取)
    async function getStockInfo(stockId, forceRefresh = false) {
        // 嘗試從緩存獲取
        const cacheKey = `stock_${stockId}`;
        const cacheTime = localStorage.getItem(`${cacheKey}_time`);
        const cachedData = localStorage.getItem(cacheKey);
        
        // 如果有緩存且不強制刷新，且緩存時間在60分鐘內，則使用緩存
        if (cachedData && !forceRefresh && cacheTime && (Date.now() - parseInt(cacheTime)) < 60 * 60 * 1000) {
            return JSON.parse(cachedData);
        }
        
        // 這裡假設有一個獲取股票數據的API
        // 在實際應用中，這裡應該調用您的後端API
        // return fetch(`/api/stock/${stockId}`).then(r => r.json());
        
        // 模擬API請求延遲
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 生成模擬數據
        const stockInfo = {
            id: stockId,
            name: getStockName(stockId),
            price: stockId.startsWith('00') ? getRandomNumber(100, 200) : getRandomNumber(20, 500),
            priceChange: getRandomNumber(-20, 20),
            priceChangePercent: getRandomNumber(-5, 5),
            volume: getRandomNumber(1000000, 50000000),
            prediction: Math.random() > 0.5 ? 'up' : 'down'
        };
        
        // 保存到緩存
        localStorage.setItem(cacheKey, JSON.stringify(stockInfo));
        localStorage.setItem(`${cacheKey}_time`, Date.now().toString());
        
        return stockInfo;
    }
    
    // 獲取隨機數字
    function getRandomNumber(min, max) {
        return min + Math.random() * (max - min);
    }
    
    // 格式化數字
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // 根據股票代碼獲取股票名稱（模擬數據）
    function getStockName(stockId) {
        const stockNames = {
            '2330': '台積電',
            '2317': '鴻海',
            '2454': '聯發科',
            '2412': '中華電',
            '2308': '台達電',
            '2303': '聯電',
            '2881': '富邦金',
            '1301': '台塑',
            '2882': '國泰金',
            '3008': '大立光'
        };
        
        return stockNames[stockId] || `股票 ${stockId}`;
    }
});
</script>
{% endblock %}