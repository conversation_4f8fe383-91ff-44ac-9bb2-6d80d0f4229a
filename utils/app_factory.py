"""應用程序工廠

負責創建和配置依賴注入容器，初始化所有服務。
"""

import os
from pathlib import Path
from typing import Optional

from utils.dependency_injection import DIContainer
from utils.enhanced_db_manager import EnhancedDatabaseManager
from utils.enhanced_logger import EnhancedLogger
from utils.enhanced_config_manager import EnhancedConfigManager
from models.combined_model import CombinedModel
from models.price_model import StockPricePredictor
from models.news_model import NewsModel


class ApplicationFactory:
    """應用程序工廠
    
    負責創建和配置整個應用程序的依賴注入容器
    """
    
    def __init__(self, config_file: Optional[str] = None, env_prefix: str = "STOCK_"):
        """初始化應用程序工廠
        
        Args:
            config_file: 配置文件路徑
            env_prefix: 環境變量前綴
        """
        self.config_file = config_file or "config/config.json"
        self.env_prefix = env_prefix
        self.container = DIContainer()
        self._setup_container()
    
    def _setup_container(self):
        """設置依賴注入容器"""
        # 註冊配置管理器（單例）
        self.container.register_singleton(
            'config_manager',
            lambda: EnhancedConfigManager(
                config_file=self.config_file,
                env_prefix=self.env_prefix
            )
        )
        
        # 註冊日誌管理器（單例）
        self.container.register_singleton(
            'logger',
            lambda: self._create_logger()
        )
        
        # 註冊數據庫管理器（單例）
        self.container.register_singleton(
            'db_manager',
            lambda: self._create_db_manager()
        )
        
        # 註冊模型服務（瞬態）
        self.container.register_transient(
            'combined_model',
            lambda: CombinedModel(
                db_manager=self.container.resolve('db_manager'),
                config_manager=self.container.resolve('config_manager'),
                logger=self.container.resolve('logger')
            )
        )
        
        self.container.register_transient(
            'price_predictor',
            lambda: StockPricePredictor(
                db_manager=self.container.resolve('db_manager'),
                config_manager=self.container.resolve('config_manager'),
                logger=self.container.resolve('logger')
            )
        )
        
        self.container.register_transient(
            'news_model',
            lambda: NewsModel(
                db_manager=self.container.resolve('db_manager'),
                config_manager=self.container.resolve('config_manager'),
                logger=self.container.resolve('logger')
            )
        )
    
    def _create_logger(self) -> EnhancedLogger:
        """創建日誌管理器"""
        config_manager = self.container.resolve('config_manager')
        logging_config = config_manager.get_logging_config()
        
        return EnhancedLogger(
            name="stock_analysis",
            log_level=logging_config.level,
            log_dir=logging_config.log_dir,
            max_file_size=logging_config.max_file_size,
            backup_count=logging_config.backup_count,
            enable_console=logging_config.enable_console,
            enable_file=logging_config.enable_file
        )
    
    def _create_db_manager(self) -> EnhancedDatabaseManager:
        """創建數據庫管理器"""
        config_manager = self.container.resolve('config_manager')
        db_config = config_manager.get_database_config()
        
        # 確保數據庫目錄存在
        db_path = Path(db_config.path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        return EnhancedDatabaseManager(
            database_path=db_config.path,
            pool_size=db_config.pool_size,
            max_overflow=db_config.max_overflow
        )
    
    def get_container(self) -> DIContainer:
        """獲取依賴注入容器
        
        Returns:
            配置好的依賴注入容器
        """
        return self.container
    
    def create_combined_model(self) -> CombinedModel:
        """創建組合模型實例
        
        Returns:
            配置好的組合模型
        """
        return self.container.resolve('combined_model')
    
    def create_price_predictor(self) -> StockPricePredictor:
        """創建價格預測器實例
        
        Returns:
            配置好的價格預測器
        """
        return self.container.resolve('price_predictor')
    
    def create_news_model(self) -> NewsModel:
        """創建新聞模型實例
        
        Returns:
            配置好的新聞模型
        """
        return self.container.resolve('news_model')
    
    def get_config_manager(self) -> EnhancedConfigManager:
        """獲取配置管理器
        
        Returns:
            配置管理器實例
        """
        return self.container.resolve('config_manager')
    
    def get_logger(self) -> EnhancedLogger:
        """獲取日誌管理器
        
        Returns:
            日誌管理器實例
        """
        return self.container.resolve('logger')
    
    def get_db_manager(self) -> EnhancedDatabaseManager:
        """獲取數據庫管理器
        
        Returns:
            數據庫管理器實例
        """
        return self.container.resolve('db_manager')
    
    def cleanup(self):
        """清理資源"""
        try:
            # 關閉數據庫連接
            db_manager = self.container.resolve('db_manager')
            db_manager.close()
            
            # 清理日誌
            logger = self.container.resolve('logger')
            logger.info("應用程序正在關閉")
            
        except Exception as e:
            print(f"清理資源時發生錯誤: {e}")
    
    def health_check(self) -> dict:
        """健康檢查
        
        Returns:
            健康狀態信息
        """
        health_status = {
            'status': 'healthy',
            'components': {}
        }
        
        try:
            # 檢查配置管理器
            config_manager = self.container.resolve('config_manager')
            health_status['components']['config'] = {
                'status': 'healthy',
                'config_file': str(config_manager.config_file)
            }
        except Exception as e:
            health_status['components']['config'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['status'] = 'unhealthy'
        
        try:
            # 檢查數據庫管理器
            db_manager = self.container.resolve('db_manager')
            # 嘗試簡單查詢
            with db_manager.get_session() as session:
                session.execute("SELECT 1")
            
            health_status['components']['database'] = {
                'status': 'healthy',
                'database_path': db_manager.database_path
            }
        except Exception as e:
            health_status['components']['database'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['status'] = 'unhealthy'
        
        try:
            # 檢查日誌管理器
            logger = self.container.resolve('logger')
            log_stats = logger.get_log_stats()
            health_status['components']['logging'] = {
                'status': 'healthy',
                'stats': log_stats
            }
        except Exception as e:
            health_status['components']['logging'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['status'] = 'unhealthy'
        
        return health_status
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()


# 全局應用程序工廠實例
_app_factory = None


def get_app_factory(config_file: Optional[str] = None, env_prefix: str = "STOCK_") -> ApplicationFactory:
    """獲取應用程序工廠實例（單例模式）
    
    Args:
        config_file: 配置文件路徑
        env_prefix: 環境變量前綴
        
    Returns:
        應用程序工廠實例
    """
    global _app_factory
    
    if _app_factory is None:
        _app_factory = ApplicationFactory(config_file, env_prefix)
    
    return _app_factory


def reset_app_factory():
    """重置應用程序工廠（主要用於測試）"""
    global _app_factory
    
    if _app_factory is not None:
        _app_factory.cleanup()
        _app_factory = None