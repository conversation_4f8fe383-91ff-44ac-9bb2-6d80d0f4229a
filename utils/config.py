# utils/config.py
import json
import os
from typing import Any, Dict, Optional

import yaml


class ConfigManager:
    def __init__(self, config_dir: str = "config"):
        self.config_dir = config_dir
        os.makedirs(config_dir, exist_ok=True)

        # 計算專案根目錄
        # __file__ 是 utils/config.py 的路徑
        # os.path.dirname(__file__) 是 utils/
        # os.path.dirname(os.path.dirname(__file__)) 是 main-news/ (專案根目錄)
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        self.default_config = {
            "database": {
                # 將資料庫路徑設置在專案根目錄下的 database/tw_stock_data.db
                "path": os.path.join(project_root, "database", "tw_stock_data.db"),
                "type": "sqlite",
            },
            "models": {
                "price_predictor": {"days": 30, "test_size": 0.2},
                "news_model": {"sentiment_threshold": 0.2},
            },
            "logging": {
                "level": "INFO",
                "dir": os.path.join(project_root, "logs"),  # 日誌目錄也可以相對專案根目錄
            },
        }
        # ... (其餘 ConfigManager 的程式碼保持不變)

    def load_config(self, filename: str) -> Dict[str, Any]:
        """
        載入配置文件

        Args:
            filename (str): 配置文件名稱 (例如 app_config.json)

        Returns:
            Dict: 配置內容
        """
        # 讓設定檔的路徑也相對於專案根目錄下的 config 資料夾
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_file_dir = os.path.join(project_root, self.config_dir)
        filepath = os.path.join(config_file_dir, filename)

        # 判斷文件類型
        _, ext = os.path.splitext(filename)
        ext = ext.lower()

        try:
            with open(filepath, "r", encoding="utf-8") as f:
                if ext == ".json":
                    config = json.load(f)
                elif ext in [".yaml", ".yml"]:
                    config = yaml.safe_load(f)
                else:
                    # 如果設定檔本身不存在，或者格式不支援，直接返回預設配置
                    print(f"配置文件 {filename} 格式不支持或解析錯誤，使用預設配置")
                    return self.default_config

            # 合併預設配置和用戶配置
            merged_config = self._deep_merge(self.default_config, config)
            return merged_config

        except FileNotFoundError:
            print(f"配置文件 {filepath} 未找到，使用預設配置")
            return self.default_config
        except json.JSONDecodeError:
            print(f"JSON配置文件 {filename} 解析錯誤，使用預設配置")
            return self.default_config
        except yaml.YAMLError:
            print(f"YAML配置文件 {filename} 解析錯誤，使用預設配置")
            return self.default_config
        except Exception as e:
            print(f"載入配置文件 {filename} 時發生未知錯誤: {e}，使用預設配置")
            return self.default_config

    def save_config(self, config: Dict[str, Any], filename: str = "config.json") -> None:
        """
        保存配置文件

        Args:
            config (Dict): 配置內容
            filename (str): 保存的文件名 (例如 app_config.json)
        """
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_file_dir = os.path.join(project_root, self.config_dir)
        filepath = os.path.join(config_file_dir, filename)

        # 判斷文件類型
        _, ext = os.path.splitext(filename)
        ext = ext.lower()

        try:
            with open(filepath, "w", encoding="utf-8") as f:
                if ext == ".json":
                    json.dump(config, f, ensure_ascii=False, indent=4)
                elif ext in [".yaml", ".yml"]:
                    yaml.dump(config, f, allow_unicode=True, default_flow_style=False)
                else:
                    raise ValueError(f"不支持的文件類型: {ext}")
            print(f"配置已成功保存到 {filepath}")

        except Exception as e:
            print(f"保存配置文件時發生錯誤: {e}")

    def get_config_value(
        self, config: Dict[str, Any], key_path: str, default: Optional[Any] = None
    ) -> Any:
        """
        安全取得巢狀字典的值

        Args:
            config (Dict): 配置字典
            key_path (str): 以點分隔的鍵路徑
            default (Any, optional): 默認值

        Returns:
            Any: 配置值
        """
        keys = key_path.split(".")
        value = config

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default

        return value

    def _deep_merge(self, base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
        """
        遞歸合併兩個字典

        Args:
            base (Dict): 基礎配置
            update (Dict): 更新的配置

        Returns:
            Dict: 合併後的配置
        """
        result = base.copy()
        for key, value in update.items():
            if isinstance(value, dict) and key in result and isinstance(result[key], dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        return result


# 全局配置管理器實例 (可以考慮不在這裡直接實例化，而是在應用程式主入口處實例化並傳遞)
# config_manager = ConfigManager()
