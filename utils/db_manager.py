import os
from contextlib import contextmanager

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool

from utils.logger import get_logger
from sqlalchemy.exc import IntegrityError, OperationalError

# 初始化 logger
logger = get_logger(__name__)


class DatabaseManager:
    """数据库连接池管理器"""

    def __init__(self, db_path=None):
        """初始化数据库管理器

        Args:
            db_path (str): 数据库文件路径，如果为None则使用默认路径
        """
        if db_path is None:
            basedir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
            db_path = os.path.join(basedir, "database", "tw_stock_data.db")

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 保存数据库路径
        self.db_path = db_path

        # 创建数据库URL
        self.db_url = f"sqlite:///{db_path}"

        # 创建引擎，配置连接池
        self.engine = create_engine(
            self.db_url,
            poolclass=QueuePool,
            pool_size=5,  # 连接池大小
            max_overflow=10,  # 最大溢出连接数
            pool_timeout=30,  # 连接超时时间
            pool_recycle=1800,  # 连接回收时间（30分钟）
            echo=False,  # 是否打印SQL语句
        )

        # 创建会话工厂
        self.session_factory = sessionmaker(bind=self.engine)

        # 创建线程安全的会话
        self.Session = scoped_session(self.session_factory)

        logger.info(f"数据库连接池初始化完成: {db_path}")

    @contextmanager
    def get_session(self):
        """获取数据库会话的上下文管理器

        Yields:
            Session: 数据库会话对象
        """
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作错误: {str(e)}", exc_info=True)
            raise
        finally:
            session.close()

    def init_db(self):
        """初始化数据库表结构"""
        from models.stock import Base

        try:
            Base.metadata.create_all(self.engine)
            logger.info("数据库表结构初始化完成")
        except Exception as e:
            logger.error(f"数据库表结构初始化失败: {str(e)}", exc_info=True)
            raise

    def close(self):
        """关闭数据库连接池"""
        try:
            self.Session.remove()
            self.engine.dispose()
            logger.info("数据库连接池已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接池时发生错误: {str(e)}", exc_info=True)
            raise


# 创建全局数据库管理器实例
db_manager = DatabaseManager()


class DatabaseError(Exception):
    """自定义数据库错误类"""

    pass
