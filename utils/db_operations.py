from datetime import datetime, timedelta
from sqlalchemy import desc
from models.stock import Stock, DailyPrice, News, Watchlist, WatchlistItem
from utils.logger import logger

def get_stock_list(session):
    """获取股票列表
    
    Args:
        session: 数据库会话
        
    Returns:
        list: 股票列表
    """
    try:
        stocks = session.query(Stock).all()
        return [{
            'stock_code': stock.stock_id,
            'stock_name': stock.name,
            'current_price': get_latest_price(session, stock.stock_id),
            'price_change': get_price_change(session, stock.stock_id),
            'price_change_percent': get_price_change_percent(session, stock.stock_id),
            'volume': get_latest_volume(session, stock.stock_id)
        } for stock in stocks]
    except Exception as e:
        logger.error(f"获取股票列表失败: {str(e)}")
        raise

def get_latest_price(session, stock_id):
    """获取最新价格
    
    Args:
        session: 数据库会话
        stock_id (str): 股票代码
        
    Returns:
        float: 最新价格
    """
    latest_price = session.query(DailyPrice)\
        .filter(DailyPrice.stock_id == stock_id)\
        .order_by(desc(DailyPrice.date))\
        .first()
    return latest_price.close_price if latest_price else None

def get_price_change(session, stock_id):
    """获取价格变化
    
    Args:
        session: 数据库会话
        stock_id (str): 股票代码
        
    Returns:
        float: 价格变化
    """
    latest_prices = session.query(DailyPrice)\
        .filter(DailyPrice.stock_id == stock_id)\
        .order_by(desc(DailyPrice.date))\
        .limit(2)\
        .all()
    if len(latest_prices) >= 2:
        return latest_prices[0].close_price - latest_prices[1].close_price
    return None

def get_price_change_percent(session, stock_id):
    """获取价格变化百分比
    
    Args:
        session: 数据库会话
        stock_id (str): 股票代码
        
    Returns:
        float: 价格变化百分比
    """
    latest_prices = session.query(DailyPrice)\
        .filter(DailyPrice.stock_id == stock_id)\
        .order_by(desc(DailyPrice.date))\
        .limit(2)\
        .all()
    if len(latest_prices) >= 2:
        return ((latest_prices[0].close_price - latest_prices[1].close_price) / 
                latest_prices[1].close_price * 100)
    return None

def get_latest_volume(session, stock_id):
    """获取最新成交量
    
    Args:
        session: 数据库会话
        stock_id (str): 股票代码
        
    Returns:
        int: 最新成交量
    """
    latest_price = session.query(DailyPrice)\
        .filter(DailyPrice.stock_id == stock_id)\
        .order_by(desc(DailyPrice.date))\
        .first()
    return latest_price.volume if latest_price else None

def get_stock_history(session, stock_id, days=30):
    """获取股票历史数据
    
    Args:
        session: 数据库会话
        stock_id (str): 股票代码
        days (int): 获取天数
        
    Returns:
        list: 历史数据列表
    """
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        history = session.query(DailyPrice)\
            .filter(
                DailyPrice.stock_id == stock_id,
                DailyPrice.date >= start_date,
                DailyPrice.date <= end_date
            )\
            .order_by(DailyPrice.date)\
            .all()
            
        return [{
            'date': price.date.strftime('%Y-%m-%d'),
            'open': price.open_price,
            'high': price.high_price,
            'low': price.low_price,
            'close': price.close_price,
            'volume': price.volume
        } for price in history]
    except Exception as e:
        logger.error(f"获取股票历史数据失败: {str(e)}")
        raise

def get_stock_news(session, stock_id, limit=10):
    """获取股票相关新闻
    
    Args:
        session: 数据库会话
        stock_id (str): 股票代码
        limit (int): 获取数量
        
    Returns:
        list: 新闻列表
    """
    try:
        news = session.query(News)\
            .filter(News.stock_id == stock_id)\
            .order_by(desc(News.published_at))\
            .limit(limit)\
            .all()
            
        return [{
            'id': news_item.id,
            'title': news_item.title,
            'content': news_item.content,
            'source': news_item.source,
            'url': news_item.url,
            'published_at': news_item.published_at.strftime('%Y-%m-%d %H:%M:%S'),
            'sentiment_score': news_item.sentiment_score
        } for news_item in news]
    except Exception as e:
        logger.error(f"获取股票新闻失败: {str(e)}")
        raise

def get_watchlist(session, watchlist_id=None):
    """获取观察清单
    
    Args:
        session: 数据库会话
        watchlist_id (int, optional): 观察清单ID
        
    Returns:
        dict: 观察清单数据
    """
    try:
        if watchlist_id:
            watchlist = session.query(Watchlist)\
                .filter(Watchlist.id == watchlist_id)\
                .first()
            if not watchlist:
                return None
                
            items = session.query(WatchlistItem)\
                .filter(WatchlistItem.watchlist_id == watchlist_id)\
                .all()
                
            return {
                'id': watchlist.id,
                'name': watchlist.name,
                'description': watchlist.description,
                'created_at': watchlist.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': watchlist.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                'items': [{
                    'id': item.id,
                    'stock_id': item.stock_id,
                    'note': item.note,
                    'created_at': item.created_at.strftime('%Y-%m-%d %H:%M:%S')
                } for item in items]
            }
        else:
            watchlists = session.query(Watchlist).all()
            return [{
                'id': watchlist.id,
                'name': watchlist.name,
                'description': watchlist.description,
                'created_at': watchlist.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': watchlist.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            } for watchlist in watchlists]
    except Exception as e:
        logger.error(f"获取观察清单失败: {str(e)}")
        raise 