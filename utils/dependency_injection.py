"""依賴注入容器

提供統一的依賴管理和注入機制，提高代碼的可測試性和可維護性。
"""

import logging
from typing import Any, Dict, Type, TypeVar, Callable, Optional
from abc import ABC, abstractmethod

T = TypeVar('T')


class DIContainer:
    """依賴注入容器"""
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
        self._logger = logging.getLogger(__name__)
    
    def register_singleton(self, interface: Type[T], implementation: Type[T]) -> None:
        """註冊單例服務
        
        Args:
            interface: 接口類型
            implementation: 實現類型
        """
        key = interface.__name__
        self._factories[key] = implementation
        self._logger.debug(f"註冊單例服務: {key} -> {implementation.__name__}")
    
    def register_transient(self, interface: Type[T], implementation: Type[T]) -> None:
        """註冊瞬態服務（每次都創建新實例）
        
        Args:
            interface: 接口類型
            implementation: 實現類型
        """
        key = interface.__name__
        self._services[key] = implementation
        self._logger.debug(f"註冊瞬態服務: {key} -> {implementation.__name__}")
    
    def register_instance(self, interface: Type[T], instance: T) -> None:
        """註冊實例
        
        Args:
            interface: 接口類型
            instance: 實例對象
        """
        key = interface.__name__
        self._singletons[key] = instance
        self._logger.debug(f"註冊實例: {key}")
    
    def resolve(self, interface: Type[T]) -> T:
        """解析依賴
        
        Args:
            interface: 要解析的接口類型
            
        Returns:
            解析的實例
            
        Raises:
            ValueError: 當無法解析依賴時
        """
        key = interface.__name__
        
        # 檢查是否有已註冊的實例
        if key in self._singletons:
            return self._singletons[key]
        
        # 檢查是否有單例工廠
        if key in self._factories:
            if key not in self._singletons:
                self._singletons[key] = self._create_instance(self._factories[key])
            return self._singletons[key]
        
        # 檢查是否有瞬態服務
        if key in self._services:
            return self._create_instance(self._services[key])
        
        raise ValueError(f"無法解析依賴: {key}")
    
    def _create_instance(self, cls: Type[T]) -> T:
        """創建實例，支持構造函數注入
        
        Args:
            cls: 要創建的類
            
        Returns:
            創建的實例
        """
        try:
            # 獲取構造函數參數
            import inspect
            sig = inspect.signature(cls.__init__)
            params = {}
            
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue
                
                # 嘗試解析參數類型
                if param.annotation != inspect.Parameter.empty:
                    try:
                        params[param_name] = self.resolve(param.annotation)
                    except ValueError:
                        # 如果無法解析，使用默認值或跳過
                        if param.default != inspect.Parameter.empty:
                            params[param_name] = param.default
            
            return cls(**params)
        except Exception as e:
            self._logger.error(f"創建實例失敗: {cls.__name__}, 錯誤: {e}")
            # 嘗試無參數創建
            return cls()


# 全局容器實例
container = DIContainer()


def inject(interface: Type[T]) -> T:
    """依賴注入裝飾器
    
    Args:
        interface: 要注入的接口類型
        
    Returns:
        解析的實例
    """
    return container.resolve(interface)


class Injectable(ABC):
    """可注入的基類"""
    pass


# 數據庫接口
class IDatabaseManager(Injectable):
    """數據庫管理器接口"""
    
    @abstractmethod
    def get_connection(self):
        """獲取數據庫連接"""
        pass
    
    @abstractmethod
    def execute_query(self, query: str, params: tuple = None):
        """執行查詢"""
        pass


# 配置管理接口
class IConfigManager(Injectable):
    """配置管理器接口"""
    
    @abstractmethod
    def get_config(self, section: str, key: str, default: Any = None) -> Any:
        """獲取配置值"""
        pass


# 日誌接口
class ILogger(Injectable):
    """日誌接口"""
    
    @abstractmethod
    def info(self, message: str) -> None:
        """記錄信息日誌"""
        pass
    
    @abstractmethod
    def error(self, message: str, exc_info: bool = False) -> None:
        """記錄錯誤日誌"""
        pass
    
    @abstractmethod
    def warning(self, message: str) -> None:
        """記錄警告日誌"""
        pass
    
    @abstractmethod
    def debug(self, message: str) -> None:
        """記錄調試日誌"""
        pass