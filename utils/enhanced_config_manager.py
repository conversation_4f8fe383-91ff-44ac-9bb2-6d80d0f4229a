"""增強的配置管理器

實現依賴注入接口，提供更好的配置管理功能。
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from configparser import ConfigParser

from utils.dependency_injection import IConfigManager
from utils.exceptions import ConfigurationError


@dataclass
class DatabaseConfig:
    """數據庫配置"""
    path: str = "data/stock_data.db"
    pool_size: int = 10
    max_overflow: int = 20
    timeout: float = 30.0
    enable_foreign_keys: bool = True


@dataclass
class APIConfig:
    """API配置"""
    alpha_vantage_key: str = ""
    news_api_key: str = ""
    request_timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit_calls: int = 5
    rate_limit_period: int = 60


@dataclass
class LoggingConfig:
    """日誌配置"""
    level: str = "INFO"
    log_dir: str = "logs"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    enable_console: bool = True
    enable_file: bool = True
    cleanup_days: int = 30


@dataclass
class ModelConfig:
    """模型配置"""
    prediction_days: int = 30
    technical_indicators: List[str] = None
    sentiment_weight: float = 0.3
    technical_weight: float = 0.7
    train_test_split: float = 0.8
    random_state: int = 42
    model_save_dir: str = "models/saved"
    
    def __post_init__(self):
        if self.technical_indicators is None:
            self.technical_indicators = [
                'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26',
                'RSI', 'MACD', 'MACD_signal', 'BB_upper', 'BB_lower'
            ]


@dataclass
class SecurityConfig:
    """安全配置"""
    secret_key: str = ""
    encryption_algorithm: str = "HS256"
    token_expiry_hours: int = 24
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 30


@dataclass
class PerformanceConfig:
    """性能配置"""
    cache_size: int = 1000
    cache_ttl_seconds: int = 3600
    batch_size: int = 100
    max_workers: int = 4
    memory_limit_mb: int = 512


class EnhancedConfigManager(IConfigManager):
    """增強的配置管理器
    
    支持多種配置格式，環境變量覆蓋，配置驗證等功能
    """
    
    def __init__(self, config_file: str = "config/config.json", env_prefix: str = "STOCK_"):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路徑
            env_prefix: 環境變量前綴
        """
        self.config_file = Path(config_file)
        self.env_prefix = env_prefix
        self._config_data = {}
        self._watchers = []
        
        # 初始化默認配置
        self._init_default_config()
        
        # 加載配置
        self._load_config()
        
        # 應用環境變量覆蓋
        self._apply_env_overrides()
        
        # 驗證配置
        self._validate_config()
    
    def _init_default_config(self):
        """初始化默認配置"""
        self._config_data = {
            'database': asdict(DatabaseConfig()),
            'api': asdict(APIConfig()),
            'logging': asdict(LoggingConfig()),
            'model': asdict(ModelConfig()),
            'security': asdict(SecurityConfig()),
            'performance': asdict(PerformanceConfig())
        }
    
    def _load_config(self):
        """加載配置文件"""
        if not self.config_file.exists():
            # 創建默認配置文件
            self._save_config()
            return
        
        try:
            if self.config_file.suffix.lower() == '.json':
                self._load_json_config()
            elif self.config_file.suffix.lower() in ['.ini', '.cfg']:
                self._load_ini_config()
            else:
                raise ConfigurationError(f"不支持的配置文件格式: {self.config_file.suffix}")
        except Exception as e:
            raise ConfigurationError(f"加載配置文件失敗: {e}")
    
    def _load_json_config(self):
        """加載JSON配置文件"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            file_config = json.load(f)
        
        # 深度合併配置
        self._deep_merge(self._config_data, file_config)
    
    def _load_ini_config(self):
        """加載INI配置文件"""
        parser = ConfigParser()
        parser.read(self.config_file, encoding='utf-8')
        
        for section_name in parser.sections():
            if section_name not in self._config_data:
                self._config_data[section_name] = {}
            
            for key, value in parser[section_name].items():
                # 嘗試轉換數據類型
                self._config_data[section_name][key] = self._convert_value(value)
    
    def _convert_value(self, value: str) -> Union[str, int, float, bool, List]:
        """轉換配置值的數據類型"""
        # 布爾值
        if value.lower() in ('true', 'yes', '1', 'on'):
            return True
        elif value.lower() in ('false', 'no', '0', 'off'):
            return False
        
        # 列表（逗號分隔）
        if ',' in value:
            return [item.strip() for item in value.split(',')]
        
        # 數字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 字符串
        return value
    
    def _deep_merge(self, target: Dict, source: Dict):
        """深度合併字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value
    
    def _apply_env_overrides(self):
        """應用環境變量覆蓋"""
        for env_key, env_value in os.environ.items():
            if not env_key.startswith(self.env_prefix):
                continue
            
            # 移除前綴並轉換為配置路徑
            config_path = env_key[len(self.env_prefix):].lower()
            
            # 支持嵌套配置（使用雙下劃線分隔）
            path_parts = config_path.split('__')
            
            # 設置配置值
            current = self._config_data
            for part in path_parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            current[path_parts[-1]] = self._convert_value(env_value)
    
    def _validate_config(self):
        """驗證配置"""
        errors = []
        
        # 驗證數據庫配置
        db_config = self._config_data.get('database', {})
        if not db_config.get('path'):
            errors.append("數據庫路徑不能為空")
        
        # 驗證API配置
        api_config = self._config_data.get('api', {})
        if not api_config.get('alpha_vantage_key'):
            errors.append("Alpha Vantage API密鑰不能為空")
        
        # 驗證模型配置
        model_config = self._config_data.get('model', {})
        prediction_days = model_config.get('prediction_days', 0)
        if prediction_days <= 0 or prediction_days > 365:
            errors.append("預測天數必須在1-365之間")
        
        # 驗證性能配置
        perf_config = self._config_data.get('performance', {})
        max_workers = perf_config.get('max_workers', 0)
        if max_workers <= 0 or max_workers > 32:
            errors.append("最大工作線程數必須在1-32之間")
        
        if errors:
            raise ConfigurationError(f"配置驗證失敗: {'; '.join(errors)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """獲取配置值
        
        Args:
            key: 配置鍵（支持點號分隔的嵌套鍵）
            default: 默認值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        current = self._config_data
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """設置配置值
        
        Args:
            key: 配置鍵（支持點號分隔的嵌套鍵）
            value: 配置值
        """
        keys = key.split('.')
        current = self._config_data
        
        # 創建嵌套結構
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
        
        # 通知觀察者
        self._notify_watchers(key, value)
    
    def get_database_config(self) -> DatabaseConfig:
        """獲取數據庫配置"""
        config_dict = self.get('database', {})
        return DatabaseConfig(**config_dict)
    
    def get_api_config(self) -> APIConfig:
        """獲取API配置"""
        config_dict = self.get('api', {})
        return APIConfig(**config_dict)
    
    def get_logging_config(self) -> LoggingConfig:
        """獲取日誌配置"""
        config_dict = self.get('logging', {})
        return LoggingConfig(**config_dict)
    
    def get_model_config(self) -> ModelConfig:
        """獲取模型配置"""
        config_dict = self.get('model', {})
        return ModelConfig(**config_dict)
    
    def get_security_config(self) -> SecurityConfig:
        """獲取安全配置"""
        config_dict = self.get('security', {})
        return SecurityConfig(**config_dict)
    
    def get_performance_config(self) -> PerformanceConfig:
        """獲取性能配置"""
        config_dict = self.get('performance', {})
        return PerformanceConfig(**config_dict)
    
    def update(self, config_dict: Dict[str, Any]):
        """批量更新配置
        
        Args:
            config_dict: 配置字典
        """
        self._deep_merge(self._config_data, config_dict)
        
        # 重新驗證配置
        self._validate_config()
        
        # 通知觀察者
        for key in config_dict.keys():
            self._notify_watchers(key, config_dict[key])
    
    def reload(self):
        """重新加載配置"""
        self._init_default_config()
        self._load_config()
        self._apply_env_overrides()
        self._validate_config()
    
    def save(self, file_path: Optional[str] = None):
        """保存配置到文件
        
        Args:
            file_path: 保存路徑（可選）
        """
        save_path = Path(file_path) if file_path else self.config_file
        self._save_config(save_path)
    
    def _save_config(self, file_path: Optional[Path] = None):
        """保存配置文件"""
        save_path = file_path or self.config_file
        
        # 確保目錄存在
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        if save_path.suffix.lower() == '.json':
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, indent=2, ensure_ascii=False)
        else:
            raise ConfigurationError(f"不支持保存到格式: {save_path.suffix}")
    
    def add_watcher(self, callback):
        """添加配置變更觀察者
        
        Args:
            callback: 回調函數，接收 (key, value) 參數
        """
        self._watchers.append(callback)
    
    def remove_watcher(self, callback):
        """移除配置變更觀察者
        
        Args:
            callback: 要移除的回調函數
        """
        if callback in self._watchers:
            self._watchers.remove(callback)
    
    def _notify_watchers(self, key: str, value: Any):
        """通知觀察者配置變更"""
        for watcher in self._watchers:
            try:
                watcher(key, value)
            except Exception as e:
                # 忽略觀察者錯誤，避免影響主流程
                pass
    
    def get_all(self) -> Dict[str, Any]:
        """獲取所有配置
        
        Returns:
            完整的配置字典
        """
        return self._config_data.copy()
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """獲取配置段
        
        Args:
            section: 段名
            
        Returns:
            配置段字典
        """
        return self._config_data.get(section, {}).copy()
    
    def has_key(self, key: str) -> bool:
        """檢查配置鍵是否存在
        
        Args:
            key: 配置鍵
            
        Returns:
            是否存在
        """
        return self.get(key) is not None
    
    def get_env_info(self) -> Dict[str, str]:
        """獲取環境變量信息
        
        Returns:
            相關環境變量字典
        """
        env_vars = {}
        for key, value in os.environ.items():
            if key.startswith(self.env_prefix):
                env_vars[key] = value
        return env_vars