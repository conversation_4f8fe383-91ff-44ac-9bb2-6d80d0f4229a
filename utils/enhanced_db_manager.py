"""增強的數據庫管理器

實現依賴注入接口，提供更好的連接管理和錯誤處理。
"""

import sqlite3
import threading
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Tuple, Generator
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

from utils.dependency_injection import IDatabaseManager
from utils.exceptions import (
    DatabaseConnectionError,
    DatabaseQueryError,
    DatabaseError
)
from utils.logger import logger


class EnhancedDatabaseManager(IDatabaseManager):
    """增強的數據庫管理器
    
    提供連接池、事務管理、錯誤處理等功能
    """
    
    def __init__(self, database_path: str, pool_size: int = 10, max_overflow: int = 20):
        """初始化數據庫管理器
        
        Args:
            database_path: 數據庫文件路徑
            pool_size: 連接池大小
            max_overflow: 最大溢出連接數
        """
        self.database_path = database_path
        self._local = threading.local()
        self._lock = threading.Lock()
        
        # 創建 SQLAlchemy 引擎
        self.engine = create_engine(
            f'sqlite:///{database_path}',
            poolclass=QueuePool,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_pre_ping=True,
            echo=False
        )
        
        # 創建會話工廠
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        logger.info(f"數據庫管理器初始化完成: {database_path}")
    
    def get_connection(self) -> sqlite3.Connection:
        """獲取數據庫連接
        
        Returns:
            數據庫連接對象
            
        Raises:
            DatabaseConnectionError: 連接失敗時
        """
        try:
            if not hasattr(self._local, 'connection') or self._local.connection is None:
                self._local.connection = sqlite3.connect(
                    self.database_path,
                    check_same_thread=False,
                    timeout=30.0
                )
                self._local.connection.row_factory = sqlite3.Row
                # 啟用外鍵約束
                self._local.connection.execute("PRAGMA foreign_keys = ON")
                
            return self._local.connection
            
        except sqlite3.Error as e:
            logger.error(f"數據庫連接失敗: {e}")
            raise DatabaseConnectionError(f"無法連接到數據庫: {e}", error_code="DB001")
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """獲取 SQLAlchemy 會話（上下文管理器）
        
        Yields:
            SQLAlchemy 會話對象
            
        Raises:
            DatabaseConnectionError: 連接失敗時
        """
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"數據庫會話錯誤: {e}")
            raise DatabaseError(f"數據庫操作失敗: {e}", error_code="DB002")
        except Exception as e:
            session.rollback()
            logger.error(f"未預期的數據庫錯誤: {e}")
            raise DatabaseError(f"數據庫操作失敗: {e}", error_code="DB004")
        finally:
            session.close()
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
        """執行查詢
        
        Args:
            query: SQL 查詢語句
            params: 查詢參數
            
        Returns:
            查詢結果列表
            
        Raises:
            DatabaseQueryError: 查詢失敗時
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            results = cursor.fetchall()
            logger.debug(f"查詢執行成功，返回 {len(results)} 條記錄")
            return results
            
        except sqlite3.Error as e:
            logger.error(f"查詢執行失敗: {query}, 錯誤: {e}")
            raise DatabaseQueryError(f"查詢執行失敗: {e}", error_code="DB002")
    
    def execute_non_query(self, query: str, params: Optional[Tuple] = None) -> int:
        """執行非查詢語句（INSERT, UPDATE, DELETE）
        
        Args:
            query: SQL 語句
            params: 參數
            
        Returns:
            受影響的行數
            
        Raises:
            DatabaseQueryError: 執行失敗時
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            affected_rows = cursor.rowcount
            logger.debug(f"非查詢語句執行成功，影響 {affected_rows} 行")
            return affected_rows
            
        except sqlite3.Error as e:
            logger.error(f"非查詢語句執行失敗: {query}, 錯誤: {e}")
            raise DatabaseQueryError(f"語句執行失敗: {e}", error_code="DB002")
    
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """批量執行語句
        
        Args:
            query: SQL 語句
            params_list: 參數列表
            
        Returns:
            總共影響的行數
            
        Raises:
            DatabaseQueryError: 執行失敗時
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.executemany(query, params_list)
            conn.commit()
            
            affected_rows = cursor.rowcount
            logger.debug(f"批量執行成功，影響 {affected_rows} 行")
            return affected_rows
            
        except sqlite3.Error as e:
            logger.error(f"批量執行失敗: {query}, 錯誤: {e}")
            raise DatabaseQueryError(f"批量執行失敗: {e}", error_code="DB002")
    
    @contextmanager
    def transaction(self):
        """事務上下文管理器
        
        Yields:
            數據庫連接對象
            
        Raises:
            DatabaseError: 事務失敗時
        """
        conn = self.get_connection()
        try:
            conn.execute("BEGIN")
            yield conn
            conn.commit()
            logger.debug("事務提交成功")
        except Exception as e:
            conn.rollback()
            logger.error(f"事務回滾: {e}")
            raise DatabaseError(f"事務執行失敗: {e}", error_code="DB004")
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """獲取表結構信息
        
        Args:
            table_name: 表名
            
        Returns:
            表結構信息列表
        """
        try:
            query = f"PRAGMA table_info({table_name})"
            results = self.execute_query(query)
            
            return [
                {
                    'cid': row['cid'],
                    'name': row['name'],
                    'type': row['type'],
                    'notnull': bool(row['notnull']),
                    'dflt_value': row['dflt_value'],
                    'pk': bool(row['pk'])
                }
                for row in results
            ]
        except Exception as e:
            logger.error(f"獲取表信息失敗: {table_name}, 錯誤: {e}")
            return []
    
    def table_exists(self, table_name: str) -> bool:
        """檢查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            表是否存在
        """
        try:
            query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
            results = self.execute_query(query, (table_name,))
            return len(results) > 0
        except Exception as e:
            logger.error(f"檢查表存在性失敗: {table_name}, 錯誤: {e}")
            return False
    
    def get_row_count(self, table_name: str, where_clause: str = "", params: Optional[Tuple] = None) -> int:
        """獲取表行數
        
        Args:
            table_name: 表名
            where_clause: WHERE 子句
            params: 查詢參數
            
        Returns:
            行數
        """
        try:
            query = f"SELECT COUNT(*) as count FROM {table_name}"
            if where_clause:
                query += f" WHERE {where_clause}"
            
            results = self.execute_query(query, params)
            return results[0]['count'] if results else 0
        except Exception as e:
            logger.error(f"獲取行數失敗: {table_name}, 錯誤: {e}")
            return 0
    
    def close(self):
        """關閉數據庫連接"""
        try:
            if hasattr(self._local, 'connection') and self._local.connection:
                self._local.connection.close()
                self._local.connection = None
            
            if hasattr(self, 'engine'):
                self.engine.dispose()
            
            logger.info("數據庫連接已關閉")
        except Exception as e:
            logger.error(f"關閉數據庫連接時發生錯誤: {e}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()