"""增強的日誌管理器

實現依賴注入接口，提供統一的日誌記錄功能。
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from utils.dependency_injection import ILogger


class EnhancedLogger(ILogger):
    """增強的日誌管理器
    
    提供統一的日誌記錄、格式化、輪轉等功能
    """
    
    def __init__(self, 
                 name: str = "stock_analysis",
                 log_level: str = "INFO",
                 log_dir: str = "logs",
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5,
                 enable_console: bool = True,
                 enable_file: bool = True):
        """初始化日誌管理器
        
        Args:
            name: 日誌器名稱
            log_level: 日誌級別
            log_dir: 日誌目錄
            max_file_size: 最大文件大小（字節）
            backup_count: 備份文件數量
            enable_console: 是否啟用控制台輸出
            enable_file: 是否啟用文件輸出
        """
        self.name = name
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        self.log_dir = Path(log_dir)
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.enable_console = enable_console
        self.enable_file = enable_file
        
        # 創建日誌目錄
        if self.enable_file:
            self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化日誌器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(self.log_level)
        
        # 清除現有處理器
        self.logger.handlers.clear()
        
        # 設置日誌格式
        self._setup_formatters()
        
        # 設置處理器
        self._setup_handlers()
        
        self.info(f"日誌管理器初始化完成: {name}")
    
    def _setup_formatters(self):
        """設置日誌格式器"""
        # 詳細格式（用於文件）
        self.detailed_formatter = logging.Formatter(
            fmt='%(asctime)s | %(name)s | %(levelname)s | %(filename)s:%(lineno)d | %(funcName)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 簡潔格式（用於控制台）
        self.simple_formatter = logging.Formatter(
            fmt='%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 錯誤格式（包含異常信息）
        self.error_formatter = logging.Formatter(
            fmt='%(asctime)s | %(name)s | %(levelname)s | %(filename)s:%(lineno)d | %(funcName)s | %(message)s\n%(exc_info)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    def _setup_handlers(self):
        """設置日誌處理器"""
        # 控制台處理器
        if self.enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.log_level)
            console_handler.setFormatter(self.simple_formatter)
            self.logger.addHandler(console_handler)
        
        if not self.enable_file:
            return
        
        # 主日誌文件處理器（輪轉）
        main_log_file = self.log_dir / f"{self.name}.log"
        main_handler = logging.handlers.RotatingFileHandler(
            filename=main_log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        main_handler.setLevel(self.log_level)
        main_handler.setFormatter(self.detailed_formatter)
        self.logger.addHandler(main_handler)
        
        # 錯誤日誌文件處理器
        error_log_file = self.log_dir / f"{self.name}_error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            filename=error_log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(self.error_formatter)
        self.logger.addHandler(error_handler)
        
        # 按日期分割的處理器
        daily_log_file = self.log_dir / f"{self.name}_daily.log"
        daily_handler = logging.handlers.TimedRotatingFileHandler(
            filename=daily_log_file,
            when='midnight',
            interval=1,
            backupCount=30,  # 保留30天
            encoding='utf-8'
        )
        daily_handler.setLevel(self.log_level)
        daily_handler.setFormatter(self.detailed_formatter)
        self.logger.addHandler(daily_handler)
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """記錄調試信息"""
        self.logger.debug(self._format_message(message, extra))
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """記錄信息"""
        self.logger.info(self._format_message(message, extra))
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """記錄警告"""
        self.logger.warning(self._format_message(message, extra))
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """記錄錯誤"""
        self.logger.error(self._format_message(message, extra), exc_info=exc_info)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """記錄嚴重錯誤"""
        self.logger.critical(self._format_message(message, extra), exc_info=exc_info)
    
    def exception(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """記錄異常（自動包含異常信息）"""
        self.logger.exception(self._format_message(message, extra))
    
    def _format_message(self, message: str, extra: Optional[Dict[str, Any]] = None) -> str:
        """格式化日誌消息
        
        Args:
            message: 基本消息
            extra: 額外信息
            
        Returns:
            格式化後的消息
        """
        if not extra:
            return message
        
        extra_str = " | ".join([f"{k}={v}" for k, v in extra.items()])
        return f"{message} | {extra_str}"
    
    def log_function_call(self, func_name: str, args: tuple = (), kwargs: dict = None, result: Any = None):
        """記錄函數調用
        
        Args:
            func_name: 函數名
            args: 位置參數
            kwargs: 關鍵字參數
            result: 返回結果
        """
        kwargs = kwargs or {}
        
        # 記錄函數調用
        call_info = {
            'function': func_name,
            'args_count': len(args),
            'kwargs_keys': list(kwargs.keys())
        }
        
        self.debug(f"函數調用開始", call_info)
        
        # 記錄結果（如果提供）
        if result is not None:
            result_info = {
                'function': func_name,
                'result_type': type(result).__name__
            }
            
            if hasattr(result, '__len__'):
                try:
                    result_info['result_length'] = len(result)
                except:
                    pass
            
            self.debug(f"函數調用完成", result_info)
    
    def log_performance(self, operation: str, duration: float, extra: Optional[Dict[str, Any]] = None):
        """記錄性能信息
        
        Args:
            operation: 操作名稱
            duration: 執行時間（秒）
            extra: 額外信息
        """
        perf_info = {
            'operation': operation,
            'duration_ms': round(duration * 1000, 2)
        }
        
        if extra:
            perf_info.update(extra)
        
        if duration > 1.0:  # 超過1秒的操作記錄為警告
            self.warning(f"性能警告: {operation} 執行時間較長", perf_info)
        else:
            self.info(f"性能記錄: {operation}", perf_info)
    
    def log_api_call(self, method: str, url: str, status_code: int, duration: float, extra: Optional[Dict[str, Any]] = None):
        """記錄API調用
        
        Args:
            method: HTTP方法
            url: 請求URL
            status_code: 響應狀態碼
            duration: 請求時間
            extra: 額外信息
        """
        api_info = {
            'method': method,
            'url': url,
            'status_code': status_code,
            'duration_ms': round(duration * 1000, 2)
        }
        
        if extra:
            api_info.update(extra)
        
        if 200 <= status_code < 300:
            self.info(f"API調用成功", api_info)
        elif 400 <= status_code < 500:
            self.warning(f"API調用客戶端錯誤", api_info)
        else:
            self.error(f"API調用服務器錯誤", api_info)
    
    def log_database_operation(self, operation: str, table: str, affected_rows: int = 0, duration: float = 0, extra: Optional[Dict[str, Any]] = None):
        """記錄數據庫操作
        
        Args:
            operation: 操作類型（SELECT, INSERT, UPDATE, DELETE）
            table: 表名
            affected_rows: 影響行數
            duration: 執行時間
            extra: 額外信息
        """
        db_info = {
            'operation': operation,
            'table': table,
            'affected_rows': affected_rows,
            'duration_ms': round(duration * 1000, 2) if duration > 0 else 0
        }
        
        if extra:
            db_info.update(extra)
        
        self.info(f"數據庫操作: {operation} {table}", db_info)
    
    def set_level(self, level: str):
        """設置日誌級別
        
        Args:
            level: 日誌級別（DEBUG, INFO, WARNING, ERROR, CRITICAL）
        """
        new_level = getattr(logging, level.upper(), logging.INFO)
        self.logger.setLevel(new_level)
        self.log_level = new_level
        
        # 更新所有處理器的級別
        for handler in self.logger.handlers:
            if not isinstance(handler, logging.handlers.RotatingFileHandler) or "error" not in str(handler.baseFilename):
                handler.setLevel(new_level)
        
        self.info(f"日誌級別已更改為: {level}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """獲取日誌統計信息
        
        Returns:
            日誌統計信息
        """
        stats = {
            'logger_name': self.name,
            'current_level': logging.getLevelName(self.log_level),
            'handlers_count': len(self.logger.handlers),
            'log_directory': str(self.log_dir) if self.enable_file else None
        }
        
        if self.enable_file and self.log_dir.exists():
            log_files = list(self.log_dir.glob(f"{self.name}*.log"))
            stats['log_files_count'] = len(log_files)
            
            total_size = sum(f.stat().st_size for f in log_files if f.exists())
            stats['total_log_size_mb'] = round(total_size / (1024 * 1024), 2)
        
        return stats
    
    def cleanup_old_logs(self, days: int = 30):
        """清理舊日誌文件
        
        Args:
            days: 保留天數
        """
        if not self.enable_file or not self.log_dir.exists():
            return
        
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        cleaned_count = 0
        
        for log_file in self.log_dir.glob("*.log*"):
            try:
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    cleaned_count += 1
            except Exception as e:
                self.warning(f"清理日誌文件失敗: {log_file}, 錯誤: {e}")
        
        if cleaned_count > 0:
            self.info(f"清理了 {cleaned_count} 個舊日誌文件")