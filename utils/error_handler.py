from functools import wraps
from flask import jsonify
from utils.logger import logger

def handle_api_errors(f):
    """API错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            logger.error(f"输入验证错误: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 400
        except Exception as e:
            logger.error(f"API错误: {str(e)}", exc_info=True)
            return jsonify({
                'status': 'error',
                'message': '服务器内部错误'
            }), 500
    return decorated_function

def validate_input(required_fields=None):
    """输入验证装饰器
    
    Args:
        required_fields (list): 必需字段列表
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if required_fields:
                data = request.get_json() if request.is_json else request.form
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    raise ValueError(f"缺少必需字段: {', '.join(missing_fields)}")
            return f(*args, **kwargs)
        return decorated_function
    return decorator 