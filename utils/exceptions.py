"""自定義異常類模組

定義系統中使用的各種異常類型
"""

from typing import Any, Dict, Optional


class StockSystemError(Exception):
    """股票系統基礎異常類"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
        }


class DatabaseError(StockSystemError):
    """資料庫相關錯誤"""

    pass


class DatabaseConnectionError(DatabaseError):
    """資料庫連接錯誤"""

    pass


class DatabaseQueryError(DatabaseError):
    """資料庫查詢錯誤"""

    pass


class DatabaseIntegrityError(DatabaseError):
    """資料庫完整性錯誤"""

    pass


class APIError(StockSystemError):
    """API 相關錯誤"""

    pass


class APIConnectionError(APIError):
    """API 連接錯誤"""

    pass


class APIAuthenticationError(APIError):
    """API 認證錯誤"""

    pass


class APIRateLimitError(APIError):
    """API 速率限制錯誤"""

    pass


class APITimeoutError(APIError):
    """API 超時錯誤"""

    pass


class APIResponseError(APIError):
    """API 響應錯誤"""

    pass


class DataError(StockSystemError):
    """數據相關錯誤"""

    pass


class DataValidationError(DataError):
    """數據驗證錯誤"""

    pass


class DataNotFoundError(DataError):
    """數據未找到錯誤"""

    pass


class DataFormatError(DataError):
    """數據格式錯誤"""

    pass


class DataIncompleteError(DataError):
    """數據不完整錯誤"""

    pass


class CrawlerError(StockSystemError):
    """爬蟲相關錯誤"""

    pass


class CrawlerTimeoutError(CrawlerError):
    """爬蟲超時錯誤"""

    pass


class CrawlerBlockedError(CrawlerError):
    """爬蟲被阻擋錯誤"""

    pass


class CrawlerParseError(CrawlerError):
    """爬蟲解析錯誤"""

    pass


class ModelError(StockSystemError):
    """模型相關錯誤"""

    pass


class ModelTrainingError(ModelError):
    """模型訓練錯誤"""

    pass


class ModelPredictionError(ModelError):
    """模型預測錯誤"""

    pass


class ModelNotFoundError(ModelError):
    """模型未找到錯誤"""

    pass


class ModelValidationError(ModelError):
    """模型驗證錯誤"""

    pass


class ConfigurationError(StockSystemError):
    """配置相關錯誤"""

    pass


class ConfigurationMissingError(ConfigurationError):
    """配置缺失錯誤"""

    pass


class ConfigurationValidationError(ConfigurationError):
    """配置驗證錯誤"""

    pass


class FileError(StockSystemError):
    """文件相關錯誤"""

    pass


class FileNotFoundError(FileError):
    """文件未找到錯誤"""

    pass


class FilePermissionError(FileError):
    """文件權限錯誤"""

    pass


class FileFormatError(FileError):
    """文件格式錯誤"""

    pass


class NetworkError(StockSystemError):
    """網絡相關錯誤"""

    pass


class NetworkTimeoutError(NetworkError):
    """網絡超時錯誤"""

    pass


class NetworkConnectionError(NetworkError):
    """網絡連接錯誤"""

    pass


class ValidationError(StockSystemError):
    """驗證錯誤"""

    pass


class BusinessLogicError(StockSystemError):
    """業務邏輯錯誤"""

    pass


class InsufficientDataError(BusinessLogicError):
    """數據不足錯誤"""

    pass


class InvalidParameterError(BusinessLogicError):
    """無效參數錯誤"""

    pass


class OperationNotAllowedError(BusinessLogicError):
    """操作不允許錯誤"""

    pass


# 錯誤代碼映射
ERROR_CODES = {
    # 資料庫錯誤 (DB)
    "DB001": "資料庫連接失敗",
    "DB002": "資料庫查詢錯誤",
    "DB003": "資料庫完整性約束違反",
    "DB004": "資料庫事務失敗",
    # API 錯誤 (API)
    "API001": "API 連接失敗",
    "API002": "API 認證失敗",
    "API003": "API 速率限制",
    "API004": "API 請求超時",
    "API005": "API 響應格式錯誤",
    # 數據錯誤 (DATA)
    "DATA001": "數據驗證失敗",
    "DATA002": "數據未找到",
    "DATA003": "數據格式錯誤",
    "DATA004": "數據不完整",
    # 爬蟲錯誤 (CRAWLER)
    "CRAWLER001": "爬蟲請求超時",
    "CRAWLER002": "爬蟲被網站阻擋",
    "CRAWLER003": "網頁解析失敗",
    # 模型錯誤 (MODEL)
    "MODEL001": "模型訓練失敗",
    "MODEL002": "模型預測失敗",
    "MODEL003": "模型文件未找到",
    "MODEL004": "模型驗證失敗",
    # 配置錯誤 (CONFIG)
    "CONFIG001": "配置文件缺失",
    "CONFIG002": "配置驗證失敗",
    "CONFIG003": "環境變數缺失",
    # 文件錯誤 (FILE)
    "FILE001": "文件未找到",
    "FILE002": "文件權限不足",
    "FILE003": "文件格式不支持",
    # 網絡錯誤 (NETWORK)
    "NETWORK001": "網絡連接超時",
    "NETWORK002": "網絡連接失敗",
    # 業務邏輯錯誤 (BUSINESS)
    "BUSINESS001": "數據不足，無法執行操作",
    "BUSINESS002": "參數無效",
    "BUSINESS003": "操作不被允許",
}


def get_error_message(error_code: str) -> str:
    """根據錯誤代碼獲取錯誤訊息"""
    return ERROR_CODES.get(error_code, "未知錯誤")


def create_error(
    error_class: type, message: str, error_code: Optional[str] = None, **details
) -> StockSystemError:
    """創建錯誤實例的工廠函數"""
    if not issubclass(error_class, StockSystemError):
        raise ValueError("error_class 必須是 StockSystemError 的子類")

    return error_class(message=message, error_code=error_code, details=details)
