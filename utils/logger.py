"""改進的日誌系統

支持結構化日誌、日誌輪轉和多種輸出格式
"""

import json
import logging
import logging.handlers
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from .exceptions import StockSystemError


class StructuredFormatter(logging.Formatter):
    """結構化日誌格式器"""

    def format(self, record: logging.LogRecord) -> str:
        """格式化日誌記錄為 JSON"""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # 添加異常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # 添加自定義字段
        if hasattr(record, "extra_data"):
            log_data.update(record.extra_data)

        # 處理 StockSystemError
        if record.exc_info and isinstance(record.exc_info[1], StockSystemError):
            error = record.exc_info[1]
            log_data["error_details"] = error.to_dict()

        return json.dumps(log_data, ensure_ascii=False, default=str)


class ColoredFormatter(logging.Formatter):
    """彩色日誌格式器（用於控制台）"""

    COLORS = {
        "DEBUG": "\033[36m",  # 青色
        "INFO": "\033[32m",  # 綠色
        "WARNING": "\033[33m",  # 黃色
        "ERROR": "\033[31m",  # 紅色
        "CRITICAL": "\033[35m",  # 紫色
        "RESET": "\033[0m",  # 重置
    }

    def format(self, record: logging.LogRecord) -> str:
        """格式化帶顏色的日誌"""
        color = self.COLORS.get(record.levelname, self.COLORS["RESET"])
        reset = self.COLORS["RESET"]

        # 格式化基本信息
        formatted = super().format(record)

        # 添加顏色
        return f"{color}{formatted}{reset}"


class LoggerAdapter(logging.LoggerAdapter):
    """日誌適配器，支持結構化日誌"""

    def process(self, msg: str, kwargs: Dict[str, Any]) -> tuple:
        """處理日誌消息和額外數據"""
        extra_data = kwargs.pop("extra_data", {})
        if self.extra:
            extra_data.update(self.extra)

        if extra_data:
            kwargs["extra"] = {"extra_data": extra_data}

        return msg, kwargs

    def log_with_data(self, level: int, msg: str, **extra_data):
        """記錄帶有額外數據的日誌"""
        self.log(level, msg, extra_data=extra_data)

    def info_with_data(self, msg: str, **extra_data):
        """記錄 INFO 級別的結構化日誌"""
        self.log_with_data(logging.INFO, msg, **extra_data)

    def error_with_data(self, msg: str, **extra_data):
        """記錄 ERROR 級別的結構化日誌"""
        self.log_with_data(logging.ERROR, msg, **extra_data)

    def warning_with_data(self, msg: str, **extra_data):
        """記錄 WARNING 級別的結構化日誌"""
        self.log_with_data(logging.WARNING, msg, **extra_data)


class LoggerManager:
    """日誌管理器"""

    _loggers: Dict[str, LoggerAdapter] = {}
    _configured = False

    @classmethod
    def configure(
        cls,
        log_level: str = "INFO",
        log_file: Optional[str] = None,
        max_bytes: int = 10485760,  # 10MB
        backup_count: int = 5,
        use_structured_format: bool = False,
        use_colored_console: bool = True,
    ):
        """配置日誌系統"""
        if cls._configured:
            return

        # 設置根日誌級別
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))

        # 清除現有處理器
        root_logger.handlers.clear()

        # 控制台處理器
        console_handler = logging.StreamHandler(sys.stdout)
        if use_colored_console and sys.stdout.isatty():
            console_formatter = ColoredFormatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
        else:
            console_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

        # 文件處理器
        if log_file:
            # 確保日誌目錄存在
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)

            # 使用輪轉文件處理器
            file_handler = logging.handlers.RotatingFileHandler(
                log_file, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8"
            )

            if use_structured_format:
                file_formatter = StructuredFormatter()
            else:
                file_formatter = logging.Formatter(
                    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                )

            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)

        cls._configured = True

    @classmethod
    def get_logger(cls, name: str, **extra_context) -> LoggerAdapter:
        """獲取日誌記錄器"""
        if name not in cls._loggers:
            base_logger = logging.getLogger(name)
            cls._loggers[name] = LoggerAdapter(base_logger, extra_context)

        return cls._loggers[name]

    @classmethod
    def reset(cls):
        """重置日誌系統"""
        cls._loggers.clear()
        cls._configured = False
        logging.getLogger().handlers.clear()


def setup_logger(
    name: str,
    log_file: Optional[str] = None,
    level: str = "INFO",
    max_bytes: int = 10485760,
    backup_count: int = 5,
    use_structured_format: bool = False,
    **extra_context,
) -> LoggerAdapter:
    """設置並獲取日誌記錄器（向後兼容）"""
    LoggerManager.configure(
        log_level=level,
        log_file=log_file,
        max_bytes=max_bytes,
        backup_count=backup_count,
        use_structured_format=use_structured_format,
    )

    return LoggerManager.get_logger(name, **extra_context)


def get_logger(name: str, **extra_context) -> LoggerAdapter:
    """獲取日誌記錄器"""
    return LoggerManager.get_logger(name, **extra_context)


def configure_logging_from_settings(settings):
    """從設置配置日誌系統"""
    LoggerManager.configure(
        log_level=settings.logging.level,
        log_file=settings.logging.file,
        max_bytes=settings.logging.max_bytes,
        backup_count=settings.logging.backup_count,
        use_structured_format=not settings.is_development,
        use_colored_console=settings.is_development,
    )


# 預定義的日誌記錄器
def get_app_logger() -> LoggerAdapter:
    """獲取應用主日誌記錄器"""
    return get_logger("app")


def get_database_logger() -> LoggerAdapter:
    """獲取資料庫日誌記錄器"""
    return get_logger("database")


def get_api_logger() -> LoggerAdapter:
    """獲取 API 日誌記錄器"""
    return get_logger("api")


def get_crawler_logger() -> LoggerAdapter:
    """獲取爬蟲日誌記錄器"""
    return get_logger("crawler")


def get_model_logger() -> LoggerAdapter:
    """獲取模型日誌記錄器"""
    return get_logger("model")


# 向後兼容的默認 logger
logger = get_app_logger()


if __name__ == "__main__":
    # 測試日誌系統
    setup_logger("test", "test.log", use_structured_format=True)

    test_logger = get_logger("test")
    test_logger.info("這是一條測試日誌")
    test_logger.info_with_data("這是結構化日誌", user_id=123, action="test")

    try:
        raise ValueError("測試異常")
    except Exception:
        test_logger.error("發生異常", exc_info=True)
