import os
from datetime import datetime

class SimpleLogger:
    """简单的日志管理器"""
    
    def __init__(self, log_dir='logs'):
        """初始化日志管理器
        
        Args:
            log_dir (str): 日志目录
        """
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 生成日志文件名
        self.log_file = os.path.join(log_dir, f'app_{datetime.now().strftime("%Y%m%d")}.log')
    
    def _write_log(self, level: str, message: str):
        """写入日志
        
        Args:
            level (str): 日志级别
            message (str): 日志消息
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {level}: {message}\n"
        
        # 同时输出到控制台和文件
        print(log_message.strip())
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message)
    
    def info(self, message: str):
        """记录信息日志"""
        self._write_log('INFO', message)
    
    def warning(self, message: str):
        """记录警告日志"""
        self._write_log('WARNING', message)
    
    def error(self, message: str, exc_info: bool = False):
        """记录错误日志
        
        Args:
            message (str): 错误消息
            exc_info (bool): 是否包含异常信息
        """
        if exc_info:
            import traceback
            message = f"{message}\n{traceback.format_exc()}"
        self._write_log('ERROR', message)
    
    def debug(self, message: str):
        """记录调试日志"""
        self._write_log('DEBUG', message)

# 创建全局日志实例
logger = SimpleLogger()