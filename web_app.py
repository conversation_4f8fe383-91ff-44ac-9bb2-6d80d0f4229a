# web_app.py
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
import os
import json
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 設定 matplotlib 在後台運行
import matplotlib.pyplot as plt
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
from io import BytesIO
import base64
import requests
from datetime import datetime, timedelta
import traceback
import random
import time
from ui.routes.download_routes import register_download_routes
from ui.routes.watchlist_routes import register_watchlist_routes
from utils.logger import logger
from utils.db_manager import db_manager
from utils.db_operations import (
    get_stock_list,
    get_stock_history,
    get_stock_news,
    get_watchlist
)
from utils.error_handler import handle_api_errors, validate_input
from models.stock import Stock, DailyPrice, News, Watchlist, WatchlistItem
from core.stock_price_predictor import StockPricePredictor

plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

# 基本配置
config = {
    'app_config': {
        'secret_key': 'stock_prediction_webapp_secret_key',
        'debug': True,
        'port': 8888
    },
    'api_config': {
        'base_url': 'http://localhost:5000'
    }
}

# 基本目录路径
basedir = os.path.abspath(os.path.dirname(__file__))
os.makedirs(os.path.join(basedir, "database"), exist_ok=True)
templates_path = os.path.join(basedir, 'ui', 'web', 'templates')

# 初始化 Flask 应用
app = Flask(__name__, 
            template_folder=templates_path,
            static_folder=os.path.join(basedir, 'ui', 'web', 'static'))
app.secret_key = config['app_config']['secret_key']
app.debug = config['app_config']['debug']

# API 服务器地址
API_BASE_URL = config['api_config']['base_url']

# 注册路由
register_download_routes(app)
register_watchlist_routes(app)

@app.route('/health')
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/get_stock_list')
@handle_api_errors
def api_get_stock_list():
    """获取股票列表"""
    try:
        with db_manager.get_session() as session:
            stock_list = get_stock_list(session)
            return jsonify(stock_list)
    except Exception as e:
        logger.error(f"获取股票列表失败: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/stock/<stock_id>/history')
@handle_api_errors
def api_get_stock_history(stock_id):
    """获取股票历史数据"""
    try:
        days = int(request.args.get('days', 30))
        with db_manager.get_session() as session:
            history = get_stock_history(session, stock_id, days)
            return jsonify(history)
    except Exception as e:
        logger.error(f"获取股票历史数据失败: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/stock/<stock_id>/news')
@handle_api_errors
def api_get_stock_news(stock_id):
    """获取股票相关新闻"""
    try:
        limit = int(request.args.get('limit', 10))
        with db_manager.get_session() as session:
            news = get_stock_news(session, stock_id, limit)
            return jsonify(news)
    except Exception as e:
        logger.error(f"获取股票新闻失败: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/watchlist')
@handle_api_errors
def api_get_watchlist():
    """获取观察清单"""
    try:
        watchlist_id = request.args.get('id')
        with db_manager.get_session() as session:
            watchlist_data = get_watchlist(session, watchlist_id)
            return jsonify(watchlist_data)
    except Exception as e:
        logger.error(f"获取观察清单失败: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/market_summary/twii', methods=['GET'])
@handle_api_errors
def get_twii_summary():
    """获取台湾加权指数摘要"""
    try:
        # 模拟数据
        latest_price = random.uniform(17000, 18000)
        change = random.uniform(-200, 200)
        change_percent = (change / latest_price) * 100
        latest_date_str = datetime.now().strftime('%Y-%m-%d')
        latest_update_formatted = datetime.now().strftime('%Y/%m/%d %H:%M')

        return jsonify({
            "status": "success",
            "index_name": "台灣加權指數",
            "value": round(latest_price, 2),
            "change": round(change, 2),
            "change_percent": round(change_percent, 2),
            "last_update": latest_update_formatted
        })
    except Exception as e:
        logger.error(f"获取大盘指数错误: {e}")
        raise

@app.route('/api/predict', methods=['POST'])
@handle_api_errors
def api_predict():
    """处理预测请求"""
    try:
        # 获取表单数据
        stock_id = request.form.get('stock_id')
        prediction_days = int(request.form.get('prediction_days', 1))
        
        if not stock_id:
            raise ValueError('请提供股票代码')
        
        # 初始化預測器並進行預測
        predictor = StockPricePredictor()
        prediction_result_raw = predictor.predict_price_movement(stock_id, prediction_days=prediction_days)
        predictor.close()

        if not prediction_result_raw:
            raise ValueError(f'无法为 {stock_id} 生成预测结果，可能資料不足')

        # 獲取最新股價
        with db_manager.get_session() as session:
            latest_price_data = get_stock_history(session, stock_id, days=1)
            current_price = latest_price_data[0]['close'] if latest_price_data else 0

        # 整理預測結果
        prediction_result = {
            'stock_id': stock_id,
            'prediction_date': datetime.now().strftime('%Y-%m-%d'),
            'target_date': (datetime.now() + timedelta(days=prediction_days)).strftime('%Y-%m-%d'),
            'prediction': 'up' if prediction_result_raw['prediction'] == 1 else 'down',
            'probability': prediction_result_raw['probability'],
            'current_price': round(current_price, 2)
        }
        
        # 生成基於真實歷史數據的圖表
        chart_data = generate_simple_chart(stock_id, days=90)
        
        return jsonify({
            'status': 'success',
            'prediction': prediction_result,
            'chart': chart_data
        })
        
    except Exception as e:
        logger.error(f"预测过程中发生错误: {str(e)}")
        raise

def generate_simple_chart(stock_id, days=30):
    """生成基於真實歷史數據的圖表"""
    try:
        plt.figure(figsize=(10, 6))
        
        # 從資料庫獲取真實歷史數據
        with db_manager.get_session() as session:
            history_data = get_stock_history(session, stock_id, days=days)
        
        if not history_data:
            logger.warning(f"无法为 {stock_id} 获取历史数据以生成图表")
            return None

        df = pd.DataFrame(history_data)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        # 繪製趨勢線
        plt.plot(df['date'], df['close'], 'b-', linewidth=2, label='收盤價')
        
        # 計算移動平均線
        if len(df) >= 20:
            df['MA20'] = df['close'].rolling(window=20).mean()
            plt.plot(df['date'], df['MA20'], 'r--', linewidth=1, label='20日均線')
            
        plt.title(f'{stock_id} 股價趨勢', fontsize=16)
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('價格', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        
        # 转换为 base64 图像
        buffer = BytesIO()
        plt.savefig(buffer, format='png')
        plt.close()
        
        data = base64.b64encode(buffer.getbuffer()).decode("ascii")
        return f"data:image/png;base64,{data}"
    except Exception as e:
        logger.error(f"生成图表时发生错误: {str(e)}")
        return None

@app.errorhandler(404)
def page_not_found(e):
    logger.warning(f"404错误: {request.path}")
    if request.path.startswith('/api/'):
        return jsonify({
            'status': 'error',
            'message': f'找不到API路径: {request.path}'
        }), 404
    return render_template('404.html', path=request.path), 404

if __name__ == '__main__':
    # 初始化数据库
    db_manager.init_db()
    
    port = config['app_config']['port']
    logger.info(f"Web应用启动于 http://localhost:{port}")
    app.run(host='0.0.0.0', port=port, debug=app.debug)

import requests

# 假設 API 路徑如下，請依實際情況修改
url = "http://localhost:8888/api/download"
payload = {
    # 根據你的 API 設計填寫參數
    "data_types": ["stock_price", "financial_report", "news"],
    "days": 7
}
response = requests.post(url, json=payload)
print(response.json())
