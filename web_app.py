# web_app.py
import json
import os

import matplotlib
import numpy as np
import pandas as pd
from flask import Flask, flash, jsonify, redirect, render_template, request, send_file, url_for

matplotlib.use("Agg")  # 設定 matplotlib 在後台運行
import base64
import random
import time
import traceback
from datetime import datetime, timedelta
from io import BytesIO

import matplotlib.pyplot as plt
import requests
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas

from core.stock_price_predictor import StockPricePredictor
from models.stock import DailyPrice, News, Stock, Watchlist, WatchlistItem
from ui.routes.download_routes import register_download_routes
from ui.routes.watchlist_routes import register_watchlist_routes
from utils.db_manager import db_manager
from utils.db_operations import get_stock_history, get_stock_list, get_stock_news, get_watchlist
from utils.error_handler import handle_api_errors, validate_input
from utils.logger import logger

plt.rcParams["font.sans-serif"] = [
    "Arial Unicode MS",
    "<PERSON>m<PERSON>ei",
    "Microsoft YaHei",
    "WenQuanYi Micro Hei",
]
plt.rcParams["axes.unicode_minus"] = False

# 基本配置
config = {
    "app_config": {"secret_key": "stock_prediction_webapp_secret_key", "debug": True, "port": 8890},
    "api_config": {"base_url": "http://localhost:5000"},
}

# 基本目录路径
basedir = os.path.abspath(os.path.dirname(__file__))
os.makedirs(os.path.join(basedir, "database"), exist_ok=True)
templates_path = os.path.join(basedir, "ui", "web", "templates")

# 初始化 Flask 应用
app = Flask(
    __name__,
    template_folder=templates_path,
    static_folder=os.path.join(basedir, "ui", "web", "static"),
)
app.secret_key = config["app_config"]["secret_key"]
app.debug = config["app_config"]["debug"]

# API 服务器地址
API_BASE_URL = config["api_config"]["base_url"]

# 注册路由
register_download_routes(app)
register_watchlist_routes(app)


@app.route("/health")
def health_check():
    """健康检查端点"""
    return jsonify({"status": "healthy", "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")})


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/api/get_stock_list")
@handle_api_errors
def api_get_stock_list():
    """获取股票列表"""
    try:
        with db_manager.get_session() as session:
            stock_list = get_stock_list(session)
            return jsonify(stock_list)
    except Exception as e:
        logger.error(f"获取股票列表失败: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/stock/<stock_id>/history")
@handle_api_errors
def api_get_stock_history(stock_id):
    """获取股票历史数据"""
    try:
        days = int(request.args.get("days", 30))
        with db_manager.get_session() as session:
            history = get_stock_history(session, stock_id, days)
            return jsonify(history)
    except Exception as e:
        logger.error(f"获取股票历史数据失败: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/stock/<stock_id>/news")
@handle_api_errors
def api_get_stock_news(stock_id):
    """获取股票相关新闻"""
    try:
        limit = int(request.args.get("limit", 10))
        with db_manager.get_session() as session:
            news = get_stock_news(session, stock_id, limit)
            return jsonify(news)
    except Exception as e:
        logger.error(f"获取股票新闻失败: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/watchlist")
@handle_api_errors
def api_get_watchlist():
    """获取观察清单"""
    try:
        watchlist_id = request.args.get("id")
        with db_manager.get_session() as session:
            watchlist_data = get_watchlist(session, watchlist_id)
            return jsonify(watchlist_data)
    except Exception as e:
        logger.error(f"获取观察清单失败: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/market_summary/twii", methods=["GET"])
@handle_api_errors
def get_twii_summary():
    """获取台湾加权指数摘要 - 優先使用真實大盤指數數據"""
    try:
        import sqlite3

        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()

        # 首先嘗試獲取真正的台灣加權指數數據
        cursor.execute(
            """
            SELECT date, close FROM market_index_history
            WHERE index_code = 'TWII'
            ORDER BY date DESC
            LIMIT 2
        """
        )
        index_results = cursor.fetchall()

        if index_results and len(index_results) >= 2:
            # 有真正的大盤指數數據
            current_date, current_price = index_results[0]
            previous_date, previous_price = index_results[1]

            change = current_price - previous_price
            change_percent = (change / previous_price) * 100

            latest_update_formatted = datetime.strptime(current_date, "%Y-%m-%d").strftime(
                "%Y/%m/%d"
            )

            conn.close()

            return jsonify(
                {
                    "status": "success",
                    "index_name": "台灣加權指數 (TWII)",
                    "value": round(current_price, 2),
                    "change": round(change, 2),
                    "change_percent": round(change_percent, 2),
                    "last_update": latest_update_formatted,
                    "data_source": "official_index",
                }
            )
        elif index_results and len(index_results) == 1:
            # 只有一筆大盤指數數據
            current_date, current_price = index_results[0]
            latest_update_formatted = datetime.strptime(current_date, "%Y-%m-%d").strftime(
                "%Y/%m/%d"
            )

            conn.close()

            return jsonify(
                {
                    "status": "success",
                    "index_name": "台灣加權指數 (TWII)",
                    "value": round(current_price, 2),
                    "change": 0,
                    "change_percent": 0,
                    "last_update": latest_update_formatted,
                    "data_source": "official_index",
                }
            )

        # 如果沒有大盤指數數據，使用台積電作為市場代表（但明確標示）
        logger.warning("台灣加權指數數據不可用，使用台積電作為市場代表")
        cursor.execute(
            """
            SELECT date, close FROM daily_prices
            WHERE stock_id = '2330'
            ORDER BY date DESC
            LIMIT 2
        """
        )
        stock_results = cursor.fetchall()

        if stock_results and len(stock_results) >= 2:
            current_date, current_price = stock_results[0]
            previous_date, previous_price = stock_results[1]

            change = current_price - previous_price
            change_percent = (change / previous_price) * 100

            latest_update_formatted = datetime.strptime(current_date, "%Y-%m-%d").strftime(
                "%Y/%m/%d"
            )

            conn.close()

            return jsonify(
                {
                    "status": "success",
                    "index_name": "台積電 (2330) - 市場代表",
                    "value": round(current_price, 2),
                    "change": round(change, 2),
                    "change_percent": round(change_percent, 2),
                    "last_update": latest_update_formatted,
                    "data_source": "stock_proxy",
                    "note": "大盤指數數據不可用，顯示台積電股價作為市場參考",
                }
            )
        elif stock_results and len(stock_results) == 1:
            current_date, current_price = stock_results[0]
            latest_update_formatted = datetime.strptime(current_date, "%Y-%m-%d").strftime(
                "%Y/%m/%d"
            )

            conn.close()

            return jsonify(
                {
                    "status": "success",
                    "index_name": "台積電 (2330) - 市場代表",
                    "value": round(current_price, 2),
                    "change": 0,
                    "change_percent": 0,
                    "last_update": latest_update_formatted,
                    "data_source": "stock_proxy",
                    "note": "大盤指數數據不可用，顯示台積電股價作為市場參考",
                }
            )

        conn.close()
        raise ValueError("無法獲取任何市場數據")

    except Exception as e:
        logger.error(f"获取大盘指数错误: {e}")
        return jsonify(
            {
                "status": "error",
                "index_name": "台灣加權指數",
                "value": "--",
                "change": "--",
                "change_percent": "--",
                "last_update": "資料載入失敗",
                "data_source": "error",
                "message": f"資料庫連接錯誤: {str(e)}",
            }
        )


@app.route("/api/predict", methods=["POST"])
@handle_api_errors
def api_predict():
    """处理预测请求"""
    try:
        # 获取表单数据
        stock_id = request.form.get("stock_id")
        prediction_days = int(request.form.get("prediction_days", 1))

        if not stock_id:
            raise ValueError("请提供股票代码")

        # 初始化預測器並進行預測
        predictor = StockPricePredictor()
        prediction_result_raw = predictor.predict_price_movement(
            stock_id, prediction_days=prediction_days
        )
        predictor.close()

        if not prediction_result_raw:
            raise ValueError(f"无法为 {stock_id} 生成预测结果，可能資料不足")

        # 獲取最新股價
        with db_manager.get_session() as session:
            latest_price_data = get_stock_history(session, stock_id, days=1)
            current_price = latest_price_data[0]["close"] if latest_price_data else 0

        # 整理預測結果
        prediction_result = {
            "stock_id": stock_id,
            "prediction_date": datetime.now().strftime("%Y-%m-%d"),
            "target_date": (datetime.now() + timedelta(days=prediction_days)).strftime("%Y-%m-%d"),
            "prediction": "up" if prediction_result_raw["prediction"] == 1 else "down",
            "probability": prediction_result_raw["probability"],
            "current_price": round(current_price, 2),
        }

        # 生成基於真實歷史數據的圖表
        chart_data = generate_simple_chart(stock_id, days=90)

        return jsonify({"status": "success", "prediction": prediction_result, "chart": chart_data})

    except Exception as e:
        logger.error(f"预测过程中发生错误: {str(e)}")
        raise


@app.route("/api/market_summary/hot_stocks", methods=["GET"])
@handle_api_errors
def get_hot_stocks():
    """获取熱門股票 - 使用真實資料庫數據"""
    try:
        # 直接使用SQL查詢避免ORM模型不匹配問題
        import sqlite3

        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()

        # 定義熱門股票代碼
        hot_stock_codes = ["2330", "2454", "2317", "2308", "2412", "2303", "3008", "2881"]
        hot_stocks = []

        # 預定義股票名稱
        stock_names = {
            "2330": "台積電",
            "2454": "聯發科",
            "2317": "鴻海",
            "2308": "台達電",
            "2412": "中華電",
            "2303": "聯電",
            "3008": "大立光",
            "2881": "富邦金",
        }

        for code in hot_stock_codes:
            try:
                # 獲取最近2天的數據來計算變化
                cursor.execute(
                    """
                    SELECT date, close FROM daily_prices
                    WHERE stock_id = ?
                    ORDER BY date DESC
                    LIMIT 2
                """,
                    (code,),
                )
                results = cursor.fetchall()

                if results and len(results) >= 2:
                    current_date, current_price = results[0]
                    previous_date, previous_price = results[1]

                    change = current_price - previous_price
                    change_percent = (change / previous_price) * 100

                    stock_name = stock_names.get(code, f"股票{code}")

                    hot_stocks.append(
                        {
                            "code": code,
                            "name": stock_name,
                            "price": round(current_price, 2),
                            "change": round(change, 2),
                            "change_percent": round(change_percent, 2),
                            "trend": f"{'+' if change >= 0 else ''}{change_percent:.1f}%",
                            "class": "text-success" if change >= 0 else "text-danger",
                        }
                    )
            except Exception as e:
                logger.warning(f"無法獲取股票 {code} 的數據: {e}")
                continue

        conn.close()

        # 如果獲取到足夠的數據，返回真實數據
        if len(hot_stocks) >= 3:
            return jsonify({"status": "success", "hot_stocks": hot_stocks[:5]})  # 只返回前5個

        # 如果沒有獲取到足夠的數據，使用模擬數據
        logger.warning("熱門股票數據不足，使用模擬數據")
        hot_stocks = [
            {"code": "2330", "name": "台積電", "trend": "+1.8%", "class": "text-success"},
            {"code": "2454", "name": "聯發科", "trend": "+2.3%", "class": "text-success"},
            {"code": "2317", "name": "鴻海", "trend": "-0.7%", "class": "text-danger"},
            {"code": "2308", "name": "台達電", "trend": "+1.2%", "class": "text-success"},
            {"code": "2412", "name": "中華電", "trend": "+0.3%", "class": "text-success"},
        ]

        return jsonify({"status": "success", "hot_stocks": hot_stocks})

    except Exception as e:
        logger.error(f"获取熱門股票错误: {e}")
        # 備用模擬數據
        hot_stocks = [
            {"code": "2330", "name": "台積電", "trend": "+1.8%", "class": "text-success"},
            {"code": "2454", "name": "聯發科", "trend": "+2.3%", "class": "text-success"},
            {"code": "2317", "name": "鴻海", "trend": "-0.7%", "class": "text-danger"},
            {"code": "2308", "name": "台達電", "trend": "+1.2%", "class": "text-success"},
            {"code": "2412", "name": "中華電", "trend": "+0.3%", "class": "text-success"},
        ]
        return jsonify({"status": "success", "hot_stocks": hot_stocks})


def generate_simple_chart(stock_id, days=30):
    """生成基於真實歷史數據的圖表"""
    try:
        plt.figure(figsize=(10, 6))

        # 從資料庫獲取真實歷史數據
        with db_manager.get_session() as session:
            history_data = get_stock_history(session, stock_id, days=days)

        if not history_data:
            logger.warning(f"无法为 {stock_id} 获取历史数据以生成图表")
            return None

        df = pd.DataFrame(history_data)
        df["date"] = pd.to_datetime(df["date"])
        df = df.sort_values("date")

        # 繪製趨勢線
        plt.plot(df["date"], df["close"], "b-", linewidth=2, label="收盤價")

        # 計算移動平均線
        if len(df) >= 20:
            df["MA20"] = df["close"].rolling(window=20).mean()
            plt.plot(df["date"], df["MA20"], "r--", linewidth=1, label="20日均線")

        plt.title(f"{stock_id} 股價趨勢", fontsize=16)
        plt.xlabel("日期", fontsize=12)
        plt.ylabel("價格", fontsize=12)
        plt.grid(True, linestyle="--", alpha=0.7)
        plt.legend()
        plt.tight_layout()

        # 转换为 base64 图像
        buffer = BytesIO()
        plt.savefig(buffer, format="png")
        plt.close()

        data = base64.b64encode(buffer.getbuffer()).decode("ascii")
        return f"data:image/png;base64,{data}"
    except Exception as e:
        logger.error(f"生成图表时发生错误: {str(e)}")
        return None


@app.errorhandler(404)
def page_not_found(e):
    logger.warning(f"404错误: {request.path}")
    if request.path.startswith("/api/"):
        return jsonify({"status": "error", "message": f"找不到API路径: {request.path}"}), 404
    return render_template("404.html", path=request.path), 404


if __name__ == "__main__":
    # 初始化数据库
    db_manager.init_db()

    port = config["app_config"]["port"]
    logger.info(f"Web应用启动于 http://localhost:{port}")
    app.run(host="0.0.0.0", port=port, debug=app.debug)
